"""Channel subscription service for managing channel memberships."""

import logging
from typing import List, Dict, Any, Optional
from telegram import <PERSON><PERSON>, ChatMember
from telegram.error import TelegramError
from bot.database import get_db_connection
from bot.utils.helpers import ChannelHelper, get_text

logger = logging.getLogger(__name__)


class ChannelService:
    """Service for managing channel subscriptions and verification."""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def get_required_channels(self, is_active: bool = True) -> List[Dict[str, Any]]:
        """Get all required channels for subscription."""
        try:
            async with get_db_connection() as conn:
                query = "SELECT * FROM channels WHERE is_required = true"
                params = []

                if is_active:
                    query += " AND is_active = $1"
                    params.append(True)

                query += " ORDER BY priority ASC, created_at ASC"

                channels = await conn.fetch(query, *params)
                return [dict(channel) for channel in channels]

        except Exception as e:
            self.logger.error(f"Error getting required channels: {e}")
            return []

    async def get_advertising_channels(self) -> List[Dict[str, Any]]:
        """Get all channels with advertising enabled."""
        try:
            async with get_db_connection() as conn:
                query = """
                    SELECT * FROM channels
                    WHERE advertising_enabled = true AND is_active = true
                    ORDER BY priority ASC, created_at ASC
                """

                channels = await conn.fetch(query)
                return [dict(channel) for channel in channels]

        except Exception as e:
            self.logger.error(f"Error getting advertising channels: {e}")
            return []

    async def update_channel_status(self, channel_id: int, is_active: bool) -> bool:
        """Update channel active status."""
        try:
            async with get_db_connection() as conn:
                await conn.execute(
                    "UPDATE channels SET is_active = $1, updated_at = NOW() WHERE id = $2",
                    is_active, channel_id
                )
                return True

        except Exception as e:
            self.logger.error(f"Error updating channel status: {e}")
            return False

    async def update_channel_advertising(self, channel_id: int, advertising_enabled: bool, advertising_message: str = None) -> bool:
        """Update channel advertising settings."""
        try:
            async with get_db_connection() as conn:
                if advertising_message is not None:
                    await conn.execute(
                        """
                        UPDATE channels
                        SET advertising_enabled = $1, advertising_message = $2, updated_at = NOW()
                        WHERE id = $3
                        """,
                        advertising_enabled, advertising_message, channel_id
                    )
                else:
                    await conn.execute(
                        "UPDATE channels SET advertising_enabled = $1, updated_at = NOW() WHERE id = $2",
                        advertising_enabled, channel_id
                    )
                return True

        except Exception as e:
            self.logger.error(f"Error updating channel advertising: {e}")
            return False

    async def update_user_subscription_status(self, user_id: int, channel_id: int, is_subscribed: bool) -> bool:
        """Update user's subscription status for a specific channel."""
        try:
            async with get_db_connection() as conn:
                if is_subscribed:
                    # Insert or update subscription record
                    await conn.execute(
                        """
                        INSERT INTO channel_subscriptions (user_id, channel_id, is_subscribed, verified_at)
                        VALUES ($1, $2, $3, NOW())
                        ON CONFLICT (user_id, channel_id)
                        DO UPDATE SET is_subscribed = $3, verified_at = NOW()
                        """,
                        user_id, channel_id, is_subscribed
                    )
                else:
                    # Update existing record or insert new one
                    await conn.execute(
                        """
                        INSERT INTO channel_subscriptions (user_id, channel_id, is_subscribed, verified_at)
                        VALUES ($1, $2, $3, NULL)
                        ON CONFLICT (user_id, channel_id)
                        DO UPDATE SET is_subscribed = $3, verified_at = NULL
                        """,
                        user_id, channel_id, is_subscribed
                    )
                return True

        except Exception as e:
            self.logger.error(f"Error updating user subscription status: {e}")
            return False

    async def get_user_subscription_status(self, user_id: int) -> Dict[str, Any]:
        """Get user's subscription status for all required channels."""
        try:
            async with get_db_connection() as conn:
                query = """
                    SELECT c.*, cs.is_subscribed, cs.verified_at
                    FROM channels c
                    LEFT JOIN channel_subscriptions cs ON c.id = cs.channel_id AND cs.user_id = $1
                    WHERE c.is_required = true AND c.is_active = true
                    ORDER BY c.priority ASC, c.created_at ASC
                """

                channels = await conn.fetch(query, user_id)

                subscribed_count = 0
                total_required = len(channels)
                channel_details = []

                for channel in channels:
                    channel_dict = dict(channel)
                    if channel_dict.get('is_subscribed', False):
                        subscribed_count += 1
                    channel_details.append(channel_dict)

                return {
                    'is_fully_subscribed': subscribed_count == total_required and total_required > 0,
                    'subscribed_count': subscribed_count,
                    'total_required': total_required,
                    'channels': channel_details
                }

        except Exception as e:
            self.logger.error(f"Error getting user subscription status: {e}")
            return {
                'is_fully_subscribed': False,
                'subscribed_count': 0,
                'total_required': 0,
                'channels': []
            }
    
    async def add_required_channel(
        self, 
        channel_id: str, 
        channel_name: str, 
        channel_url: str,
        priority: int = 0,
        is_active: bool = True
    ) -> bool:
        """Add a new required channel."""
        try:
            # Normalize channel ID (remove @ symbol if present)
            normalized_channel_id = ChannelHelper.normalize_channel_id(channel_id)
            
            # Validate channel ID
            if not ChannelHelper.is_valid_channel_id(normalized_channel_id):
                self.logger.error(f"Invalid channel ID format: {channel_id}")
                return False
            
            async with get_db_connection() as conn:
                await conn.execute(
                    """
                    INSERT INTO required_channels 
                    (channel_id, channel_name, channel_url, priority, is_active, created_at)
                    VALUES ($1, $2, $3, $4, $5, NOW())
                    ON CONFLICT (channel_id) DO UPDATE SET
                        channel_name = EXCLUDED.channel_name,
                        channel_url = EXCLUDED.channel_url,
                        priority = EXCLUDED.priority,
                        is_active = EXCLUDED.is_active,
                        updated_at = NOW()
                    """,
                    normalized_channel_id, channel_name, channel_url, priority, is_active
                )
                
                self.logger.info(f"Added/updated required channel: {channel_name} ({normalized_channel_id})")
                return True
        
        except Exception as e:
            self.logger.error(f"Error adding required channel {channel_id}: {e}")
            return False
    
    async def remove_required_channel(self, channel_id: str) -> bool:
        """Remove a required channel."""
        try:
            # Normalize channel ID (remove @ symbol if present)
            normalized_channel_id = ChannelHelper.normalize_channel_id(channel_id)
            
            async with get_db_connection() as conn:
                result = await conn.execute(
                    "DELETE FROM required_channels WHERE channel_id = $1",
                    normalized_channel_id
                )
                
                if result == "DELETE 1":
                    self.logger.info(f"Removed required channel: {normalized_channel_id}")
                    return True
                else:
                    self.logger.warning(f"Channel {normalized_channel_id} not found for removal")
                    return False
        
        except Exception as e:
            self.logger.error(f"Error removing required channel {channel_id}: {e}")
            return False
    
    async def check_user_membership(
        self, 
        bot: Bot, 
        user_id: int, 
        channel_id: str
    ) -> bool:
        """Check if user is a member of a specific channel."""
        try:
            # Format channel ID for Telegram API (add @ for usernames)
            # If it's a numeric ID, use as-is; if it's a username, ensure it has @
            api_channel_id = channel_id
            if not channel_id.startswith('-') and not channel_id.isdigit():
                api_channel_id = ChannelHelper.format_channel_id(channel_id, include_at=True)
            
            # Get chat member status
            member = await bot.get_chat_member(chat_id=api_channel_id, user_id=user_id)
            
            # Check if user is a member (not left or kicked)
            return member.status in [
                ChatMember.OWNER,
                ChatMember.ADMINISTRATOR,
                ChatMember.MEMBER
            ]
        
        except TelegramError as e:
            if "user not found" in str(e).lower():
                self.logger.debug(f"User {user_id} not found in channel {api_channel_id}")
                return False
            elif "chat not found" in str(e).lower():
                self.logger.warning(f"Channel {api_channel_id} not found or bot not admin")
                return False
            else:
                self.logger.error(f"Error checking membership for user {user_id} in {api_channel_id}: {e}")
                return False
        
        except Exception as e:
            self.logger.error(f"Unexpected error checking membership: {e}")
            return False
    
    async def verify_all_subscriptions(
        self, 
        bot: Bot, 
        user_id: int
    ) -> Dict[str, Any]:
        """Verify user's subscription to all required channels."""
        try:
            required_channels = await self.get_required_channels(is_active=True)
            
            if not required_channels:
                return {
                    'is_subscribed': True,
                    'missing_channels': [],
                    'subscribed_channels': [],
                    'total_required': 0
                }
            
            subscribed_channels = []
            missing_channels = []
            
            for channel in required_channels:
                is_member = await self.check_user_membership(
                    bot, user_id, channel['channel_id']
                )

                if is_member:
                    subscribed_channels.append(channel)
                    # Update subscription status in database
                    await self.update_user_subscription_status(user_id, channel['id'], True)
                else:
                    missing_channels.append(channel)
                    # Update subscription status in database
                    await self.update_user_subscription_status(user_id, channel['id'], False)
            
            is_fully_subscribed = len(missing_channels) == 0
            
            # Update user subscription status in database
            await self.update_user_subscription_status(
                user_id, is_fully_subscribed, subscribed_channels, missing_channels
            )
            
            return {
                'is_subscribed': is_fully_subscribed,
                'missing_channels': missing_channels,
                'subscribed_channels': subscribed_channels,
                'total_required': len(required_channels)
            }
        
        except Exception as e:
            self.logger.error(f"Error verifying subscriptions for user {user_id}: {e}")
            return {
                'is_subscribed': False,
                'missing_channels': [],
                'subscribed_channels': [],
                'total_required': 0,
                'error': str(e)
            }
    
    async def update_user_subscription_status(
        self,
        user_id: int,
        is_subscribed: bool,
        subscribed_channels: List[Dict[str, Any]],
        missing_channels: List[Dict[str, Any]]
    ) -> bool:
        """Update user's subscription status in database."""
        try:
            async with get_db_connection() as conn:
                # Update or insert user subscription status
                await conn.execute(
                    """
                    INSERT INTO user_subscriptions 
                    (user_id, is_subscribed, subscribed_channels, missing_channels, 
                     last_checked, created_at)
                    VALUES ($1, $2, $3, $4, NOW(), NOW())
                    ON CONFLICT (user_id) DO UPDATE SET
                        is_subscribed = EXCLUDED.is_subscribed,
                        subscribed_channels = EXCLUDED.subscribed_channels,
                        missing_channels = EXCLUDED.missing_channels,
                        last_checked = NOW(),
                        updated_at = NOW()
                    """,
                    user_id, is_subscribed, 
                    [ch['channel_id'] for ch in subscribed_channels],
                    [ch['channel_id'] for ch in missing_channels]
                )
                
                return True
        
        except Exception as e:
            self.logger.error(f"Error updating subscription status for user {user_id}: {e}")
            return False
    
    async def get_user_subscription_status(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user's current subscription status from database."""
        try:
            async with get_db_connection() as conn:
                status = await conn.fetchrow(
                    "SELECT * FROM user_subscriptions WHERE user_id = $1",
                    user_id
                )
                return dict(status) if status else None
        
        except Exception as e:
            self.logger.error(f"Error getting subscription status for user {user_id}: {e}")
            return None
    
    async def get_subscription_stats(self) -> Dict[str, Any]:
        """Get subscription statistics."""
        try:
            async with get_db_connection() as conn:
                # Total users
                total_users = await conn.fetchval(
                    "SELECT COUNT(*) FROM users"
                )
                
                # Subscribed users
                subscribed_users = await conn.fetchval(
                    "SELECT COUNT(*) FROM user_subscriptions WHERE is_subscribed = true"
                )
                
                # Users checked in last 24 hours
                recent_checks = await conn.fetchval(
                    """
                    SELECT COUNT(*) FROM user_subscriptions 
                    WHERE last_checked > NOW() - INTERVAL '24 hours'
                    """
                )
                
                # Channel-wise subscription stats
                channel_stats = await conn.fetch(
                    """
                    SELECT rc.channel_name, rc.channel_id,
                           COUNT(CASE WHEN rc.channel_id = ANY(us.subscribed_channels) THEN 1 END) as subscribers
                    FROM required_channels rc
                    LEFT JOIN user_subscriptions us ON rc.channel_id = ANY(us.subscribed_channels)
                    WHERE rc.is_active = true
                    GROUP BY rc.channel_id, rc.channel_name
                    ORDER BY subscribers DESC
                    """
                )
                
                return {
                    'total_users': total_users or 0,
                    'subscribed_users': subscribed_users or 0,
                    'subscription_rate': (subscribed_users / total_users * 100) if total_users > 0 else 0,
                    'recent_checks': recent_checks or 0,
                    'channel_stats': [dict(stat) for stat in channel_stats]
                }
        
        except Exception as e:
            self.logger.error(f"Error getting subscription stats: {e}")
            return {
                'total_users': 0,
                'subscribed_users': 0,
                'subscription_rate': 0,
                'recent_checks': 0,
                'channel_stats': []
            }
    
    async def force_recheck_user(self, bot: Bot, user_id: int) -> Dict[str, Any]:
        """Force recheck user's subscription status."""
        try:
            # Clear cached status
            async with get_db_connection() as conn:
                await conn.execute(
                    "DELETE FROM user_subscriptions WHERE user_id = $1",
                    user_id
                )
            
            # Perform fresh verification
            return await self.verify_all_subscriptions(bot, user_id)
        
        except Exception as e:
            self.logger.error(f"Error force rechecking user {user_id}: {e}")
            return {
                'is_subscribed': False,
                'missing_channels': [],
                'subscribed_channels': [],
                'total_required': 0,
                'error': str(e)
            }
    
    async def check_user_subscription(self, user_id: int, bot = None, channel_id: str = None) -> bool:
        """Check if user is subscribed to a specific channel or all required channels."""
        try:
            # Get bot instance if not provided
            if bot is None:
                from bot.main import application
                if application and application.bot:
                    bot = application.bot
                else:
                    self.logger.error("Bot instance not available")
                    return False
            
            if channel_id:
                # Check specific channel
                try:
                    member = await bot.get_chat_member(channel_id, user_id)
                    return member.status in ['member', 'administrator', 'creator']
                except Exception as e:
                    self.logger.error(f"Error checking membership for user {user_id} in channel {channel_id}: {e}")
                    return False
            else:
                # Check all required channels
                verification_result = await self.verify_all_subscriptions(bot, user_id)
                return verification_result.get('is_subscribed', False)
                
        except Exception as e:
            self.logger.error(f"Error checking user subscription: {e}")
            return False
    
    def format_subscription_message(
        self, 
        verification_result: Dict[str, Any], 
        language: str = 'en'
    ) -> str:
        """Format subscription verification message for user."""
        try:
            missing_channels = verification_result.get('missing_channels', [])
            
            if verification_result.get('is_subscribed', False):
                return get_text('subscription.fully_subscribed', language)
            
            if not missing_channels:
                return get_text('subscription.error_checking', language)
            
            # Format missing channels message
            message = get_text('subscription.subscribe_prompt', language)
            for i, channel in enumerate(missing_channels, 1):
                message += get_text('subscription.channel_item', language).format(
                    i=i,
                    name=channel['channel_name'],
                    url=channel['channel_url']
                )
            message += get_text('subscription.after_subscribe', language)
            
            return message
            
        except Exception as e:
            self.logger.error(f"Error formatting subscription message: {e}")
            return get_text('subscription.error_formatting', language)


# Global instance
channel_service = ChannelService()