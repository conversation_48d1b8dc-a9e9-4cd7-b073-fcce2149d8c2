<?php

declare(strict_types=1);

namespace VpnBot\Models;

use VpnBot\Database\Connection;
use PDO;

class VpnAccount
{
    public ?int $id = null;
    public int $user_id;
    public int $vpn_panel_id;
    public ?int $plan_id = null;
    public string $username;
    public ?string $uuid = null;
    public int $data_limit;
    public int $used_data = 0;
    public ?string $expire_date = null;
    public string $status = 'active';
    public bool $is_active = true;
    public bool $is_trial = false;
    public ?string $created_at = null;
    public ?string $updated_at = null;
    public ?string $last_usage_check = null;
    public ?string $last_connection = null;
    public int $connection_count = 0;
    public ?array $config_data = null;

    public static function findByUserId(int $userId): array
    {
        $pdo = Connection::getInstance();
        $stmt = $pdo->prepare('SELECT * FROM vpn_accounts WHERE user_id = ? ORDER BY created_at DESC');
        $stmt->execute([$userId]);
        
        $accounts = [];
        while ($data = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $accounts[] = self::fromArray($data);
        }

        return $accounts;
    }

    public static function findById(int $id): ?VpnAccount
    {
        $pdo = Connection::getInstance();
        $stmt = $pdo->prepare('SELECT * FROM vpn_accounts WHERE id = ?');
        $stmt->execute([$id]);
        
        $data = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!$data) {
            return null;
        }

        return self::fromArray($data);
    }

    public static function findByUsername(string $username): ?VpnAccount
    {
        $pdo = Connection::getInstance();
        $stmt = $pdo->prepare('SELECT * FROM vpn_accounts WHERE username = ?');
        $stmt->execute([$username]);
        
        $data = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!$data) {
            return null;
        }

        return self::fromArray($data);
    }

    public function save(): bool
    {
        $pdo = Connection::getInstance();
        
        if ($this->id === null) {
            return $this->insert($pdo);
        } else {
            return $this->update($pdo);
        }
    }

    private function insert(PDO $pdo): bool
    {
        $sql = '
            INSERT INTO vpn_accounts (
                user_id, vpn_panel_id, plan_id, username, uuid, data_limit,
                used_data, expire_date, status, is_active, is_trial,
                last_usage_check, last_connection, connection_count,
                config_data, created_at, updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW()
            )
        ';

        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            $this->user_id,
            $this->vpn_panel_id,
            $this->plan_id,
            $this->username,
            $this->uuid,
            $this->data_limit,
            $this->used_data,
            $this->expire_date,
            $this->status,
            $this->is_active,
            $this->is_trial,
            $this->last_usage_check,
            $this->last_connection,
            $this->connection_count,
            $this->config_data ? json_encode($this->config_data) : null,
        ]);

        if ($result) {
            $this->id = (int)$pdo->lastInsertId();
        }

        return $result;
    }

    private function update(PDO $pdo): bool
    {
        $sql = '
            UPDATE vpn_accounts SET
                vpn_panel_id = ?, plan_id = ?, username = ?, uuid = ?,
                data_limit = ?, used_data = ?, expire_date = ?, status = ?,
                is_active = ?, is_trial = ?, last_usage_check = ?,
                last_connection = ?, connection_count = ?, config_data = ?,
                updated_at = NOW()
            WHERE id = ?
        ';

        $stmt = $pdo->prepare($sql);
        return $stmt->execute([
            $this->vpn_panel_id,
            $this->plan_id,
            $this->username,
            $this->uuid,
            $this->data_limit,
            $this->used_data,
            $this->expire_date,
            $this->status,
            $this->is_active,
            $this->is_trial,
            $this->last_usage_check,
            $this->last_connection,
            $this->connection_count,
            $this->config_data ? json_encode($this->config_data) : null,
            $this->id,
        ]);
    }

    public static function fromArray(array $data): VpnAccount
    {
        $account = new VpnAccount();
        $account->id = $data['id'] ?? null;
        $account->user_id = (int)$data['user_id'];
        $account->vpn_panel_id = (int)$data['vpn_panel_id'];
        $account->plan_id = $data['plan_id'] ? (int)$data['plan_id'] : null;
        $account->username = $data['username'];
        $account->uuid = $data['uuid'];
        $account->data_limit = (int)$data['data_limit'];
        $account->used_data = (int)$data['used_data'];
        $account->expire_date = $data['expire_date'];
        $account->status = $data['status'];
        $account->is_active = (bool)$data['is_active'];
        $account->is_trial = (bool)$data['is_trial'];
        $account->created_at = $data['created_at'];
        $account->updated_at = $data['updated_at'];
        $account->last_usage_check = $data['last_usage_check'];
        $account->last_connection = $data['last_connection'];
        $account->connection_count = (int)$data['connection_count'];
        $account->config_data = $data['config_data'] ? json_decode($data['config_data'], true) : null;

        return $account;
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'vpn_panel_id' => $this->vpn_panel_id,
            'plan_id' => $this->plan_id,
            'username' => $this->username,
            'uuid' => $this->uuid,
            'data_limit' => $this->data_limit,
            'used_data' => $this->used_data,
            'expire_date' => $this->expire_date,
            'status' => $this->status,
            'is_active' => $this->is_active,
            'is_trial' => $this->is_trial,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'last_usage_check' => $this->last_usage_check,
            'last_connection' => $this->last_connection,
            'connection_count' => $this->connection_count,
            'config_data' => $this->config_data,
        ];
    }

    public function getUsagePercentage(): float
    {
        if ($this->data_limit <= 0) {
            return 0.0;
        }
        return min(100.0, ($this->used_data / $this->data_limit) * 100);
    }

    public function isExpired(): bool
    {
        if ($this->expire_date === null) {
            return false;
        }
        return strtotime($this->expire_date) < time();
    }

    public function getDaysUntilExpiry(): ?int
    {
        if ($this->expire_date === null) {
            return null;
        }
        $diff = strtotime($this->expire_date) - time();
        return max(0, (int)ceil($diff / 86400));
    }

    public function formatDataLimit(): string
    {
        return $this->formatBytes($this->data_limit);
    }

    public function formatUsedData(): string
    {
        return $this->formatBytes($this->used_data);
    }

    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= (1 << (10 * $pow));
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
