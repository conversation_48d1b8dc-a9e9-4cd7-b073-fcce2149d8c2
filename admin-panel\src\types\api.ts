// API Response Types
export interface User {
  id: number;
  telegram_id: number;
  username?: string;
  first_name?: string;
  last_name?: string;
  language_code?: string;
  is_premium: boolean;
  has_used_trial: boolean;
  trial_count: number;
  last_trial_at?: string;
  premium_expires_at?: string;
  data_limit: number;
  data_used: number;
  created_at: string;
  updated_at: string;
  is_active: boolean;
  last_active: string;
  notification_enabled: boolean;
  total_data_used: number;
  command_count: number;
}

export interface VPNPanel {
  id: number;
  name: string;
  base_url: string;
  api_username: string;
  api_password: string;
  is_active: boolean;
  proxies?: Record<string, any>;
  inbounds?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface PremiumPlan {
  id: number;
  name: string;
  price: number;
  duration_days: number;
  data_limit: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface BotSettings {
  id: number;
  free_data_limit: number;
  free_duration_days: number;
  required_channels: string[];
  payment_provider_token?: string;
  trial_vpn_panel_id?: number;
  max_trials_per_user: number;
  trial_reset_days: number;
  auto_notify_trial_reset: boolean;
  created_at: string;
  updated_at: string;
}

export interface DashboardStats {
  totalUsers: number;
  activeUsers: number;
  totalPanels: number;
  totalPlans: number;
}

// API Request Types
export interface CreatePanelRequest {
  name: string;
  base_url: string;
  api_username: string;
  api_password: string;
  is_active?: boolean;
  proxies?: Record<string, any>;
  inbounds?: Record<string, any>;
}

export interface UpdatePanelRequest extends Partial<CreatePanelRequest> {}

export interface CreatePlanRequest {
  name: string;
  price: number;
  duration_days: number;
  data_limit: number;
  is_active?: boolean;
}

export interface UpdatePlanRequest extends Partial<CreatePlanRequest> {}

export interface UpdateSettingsRequest {
  free_data_limit?: number;
  free_duration_days?: number;
  required_channels?: string[];
  payment_provider_token?: string;
  trial_vpn_panel_id?: number;
  max_trials_per_user?: number;
  trial_reset_days?: number;
  auto_notify_trial_reset?: boolean;
}

export interface UpdateUserRequest {
  is_premium?: boolean;
  premium_expires_at?: string;
  data_limit?: number;
}