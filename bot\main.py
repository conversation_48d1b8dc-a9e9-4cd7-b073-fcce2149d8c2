import logging
import asyncio
from telegram.ext import (
    Application, CommandH<PERSON>ler,
    PreCheckout<PERSON>uery<PERSON><PERSON><PERSON>, MessageHandler, filters
)
from telegram import Update

from bot.config import settings
from bot.database import init_db
from bot.redis import init_redis

# Import middleware
from bot.middleware.auth import AuthMiddleware
from bot.middleware.rate_limit import RateLimitMiddleware, CommandRateLimitMiddleware
from bot.middleware.logging import LoggingMiddleware, ErrorLoggingMiddleware
from bot.middleware.subscription import ChannelSubscriptionMiddleware

# Import handlers
from bot.handlers.commands import CommandHandlers
from bot.handlers.payments import PaymentHandler
from bot.handlers.errors import ErrorHandlers
from bot.handlers.language_selection import LanguageSelectionHandler
from bot.tasks import app as celery_app

# Import services
from bot.services.auth_service import AuthService
from bot.services.channel_service import ChannelService

# Enable logging
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s", 
    level=logging.INFO,
    handlers=[
        logging.FileHandler('bot.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Global application instance for imports
application = None


class TelegramBot:
    """Main bot class with modular architecture."""
    
    def __init__(self):
        self.application = None
        self.auth_service = AuthService()
        self.channel_service = ChannelService()
        
        # Initialize handlers
        self.command_handlers = CommandHandlers()
        self.payment_handler = PaymentHandler()
        self.error_handlers = ErrorHandlers()
        self.language_selection_handler = LanguageSelectionHandler()
        
        # Initialize middleware
        self.auth_middleware = AuthMiddleware()
        self.rate_limit_middleware = RateLimitMiddleware()
        self.command_rate_limit_middleware = CommandRateLimitMiddleware()
        self.logging_middleware = LoggingMiddleware()
        self.error_logging_middleware = ErrorLoggingMiddleware()
        self.subscription_middleware = ChannelSubscriptionMiddleware(self.channel_service)
    
    async def post_init(self, application: Application):
        """Initialize database and services after application is built."""
        logger.info("Initializing bot components...")
        
        try:
            # Initialize database
            logger.info("Initializing database...")
            await init_db()
            logger.info("Database initialized successfully")
            
            # Initialize Redis
            logger.info("Initializing Redis...")
            await init_redis()
            logger.info("Redis initialized successfully")
            
            # Initialize services
            logger.info("Initializing services...")
            await self._initialize_services()
            logger.info("Services initialized successfully")
            
            logger.info("Bot initialization completed successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize bot: {e}", exc_info=True)
            raise
    
    async def _initialize_services(self):
        """Initialize all services."""
        # Load settings from database
        logger.info("Loading settings from database...")
        await settings.load_from_database()
        logger.info("Settings loaded from database successfully")
        
        # Add any additional service initialization logic here
        pass
    
    def _register_middleware(self):
        """Register middleware with the application."""
        # Note: python-telegram-bot doesn't have built-in middleware support
        # We'll implement middleware pattern in our handlers
        logger.info("Middleware pattern will be implemented in handlers")
    
    def _register_command_handlers(self):
        """Register command handlers."""
        logger.info("Registering command handlers...")
        
        # Basic commands
        self.application.add_handler(
            CommandHandler("start", self.command_handlers.start_command)
        )
        
        # Menu command
        self.application.add_handler(
            CommandHandler("menu", self.command_handlers.menu_command)
        )

        # Referral commands
        from bot.handlers.referral import referral_handler
        self.application.add_handler(
            CommandHandler("referral", referral_handler.handle_referral_command)
        )
        self.application.add_handler(
            CommandHandler("referral_link", referral_handler.handle_referral_link_command)
        )

        logger.info("Command handlers registered successfully")
    
    # Callback handlers removed - all navigation now uses reply keyboards
    
    def _register_payment_handlers(self):
        """Register payment handlers."""
        logger.info("Registering payment handlers...")
        
        # Pre-checkout handler
        self.application.add_handler(
            PreCheckoutQueryHandler(self.payment_handler.handle_pre_checkout_query)
        )

        # Successful payment handler
        self.application.add_handler(
            MessageHandler(
                filters.SUCCESSFUL_PAYMENT,
                self.payment_handler.handle_successful_payment
            )
        )
        
        logger.info("Payment handlers registered successfully")
    
    def _register_message_handlers(self):
        """Register message handlers."""
        logger.info("Registering message handlers...")
        
        # Reply keyboard message handler (for users using reply keyboards)
        self.application.add_handler(
            MessageHandler(
                filters.TEXT & ~filters.COMMAND,
                self.command_handlers.handle_reply_keyboard
            )
        )
        
        logger.info("Message handlers registered successfully")
    
    def _register_error_handlers(self):
        """Register error handlers."""
        logger.info("Registering error handlers...")
        
        # Global error handler
        self.application.add_error_handler(self.error_handlers.handle_error)
        
        logger.info("Error handlers registered successfully")

    def _configure_celery_beat(self):
        """Configure Celery beat schedules."""
        logger.info("Configuring Celery beat...")
        celery_app.conf.beat_schedule = {
            'check-user-subscriptions-hourly': {
                'task': 'bot.tasks.check_all_users_channel_subscriptions',
                'schedule': 3600.0,  # Run every hour
            },
        }
        celery_app.conf.timezone = 'UTC'
    
    def build_application(self):
        """Build and configure the Telegram application."""
        global application
        logger.info("Building Telegram application...")
        
        # Build application
        self.application = (
            Application.builder()
            .token(settings.BOT_TOKEN)
            .post_init(self.post_init)
            .build()
        )
        
        # Set global application for imports
        application = self.application
        
        # Register all handlers
        self._register_middleware()
        self._register_command_handlers()
        # Callback handlers removed - all navigation now uses reply keyboards
        self._configure_celery_beat()
        self._register_payment_handlers()
        self._register_message_handlers()
        self._register_error_handlers()
        
        logger.info("Telegram application built successfully")
        return self.application
    
    async def start(self):
        """Start the bot."""
        logger.info("Starting Telegram bot...")
        
        try:
            # Build application
            application = self.build_application()
            
            # Initialize the application
            await application.initialize()
            
            # Start the application
            await application.start()
            
            # Start polling for updates
            logger.info("Bot is starting to poll for updates...")
            await application.updater.start_polling(
                allowed_updates=Update.ALL_TYPES,
                drop_pending_updates=True
            )
            
            # Keep the bot running
            logger.info("Bot is now running. Press Ctrl+C to stop.")
            try:
                # Run indefinitely until interrupted
                while True:
                    await asyncio.sleep(1)
            except asyncio.CancelledError:
                logger.info("Bot polling cancelled")
            
        except Exception as e:
            logger.error(f"Failed to start bot: {e}", exc_info=True)
            raise
        finally:
            logger.info("Bot stopped")


async def main():
    """Main entry point."""
    try:
        bot = TelegramBot()
        await bot.start()
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Bot crashed: {e}", exc_info=True)
        raise


if __name__ == "__main__":
    # Run the bot
    try:
        # Check if there's already an event loop running
        try:
            loop = asyncio.get_running_loop()
            logger.info("Event loop already running, creating task...")
            # If we're in an existing loop, create a task instead of asyncio.run
            import nest_asyncio
            nest_asyncio.apply()
            # Use the existing loop instead of creating a new one
            task = loop.create_task(main())
            loop.run_until_complete(task)
        except RuntimeError:
            # No event loop running, safe to use asyncio.run
            logger.info("No event loop running, starting new one...")
            asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Failed to run bot: {e}", exc_info=True)