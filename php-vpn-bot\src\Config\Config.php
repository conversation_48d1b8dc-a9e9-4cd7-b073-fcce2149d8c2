<?php

declare(strict_types=1);

namespace VpnBot\Config;

use Dotenv\Dotenv;

class Config
{
    private static ?Config $instance = null;
    private array $config = [];

    private function __construct()
    {
        $this->loadEnvironment();
        $this->loadConfig();
    }

    public static function getInstance(): Config
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function loadEnvironment(): void
    {
        $dotenv = Dotenv::createImmutable(dirname(__DIR__, 2));
        $dotenv->safeLoad();
    }

    private function loadConfig(): void
    {
        $this->config = [
            'bot' => [
                'token' => $_ENV['BOT_TOKEN'] ?? '',
                'username' => $_ENV['BOT_USERNAME'] ?? '',
                'webhook_url' => $_ENV['WEBHOOK_URL'] ?? '',
                'admin_user_id' => (int)($_ENV['ADMIN_USER_ID'] ?? 0),
            ],
            'database' => [
                'host' => $_ENV['POSTGRES_HOST'] ?? 'localhost',
                'port' => (int)($_ENV['POSTGRES_PORT'] ?? 5432),
                'database' => $_ENV['POSTGRES_DATABASE'] ?? 'telegram_vpn_bot',
                'username' => $_ENV['POSTGRES_USER'] ?? 'vpn_bot',
                'password' => $_ENV['POSTGRES_PASSWORD'] ?? '',
            ],
            'redis' => [
                'host' => $_ENV['REDIS_HOST'] ?? 'localhost',
                'port' => (int)($_ENV['REDIS_PORT'] ?? 6379),
                'database' => (int)($_ENV['REDIS_DB'] ?? 0),
                'password' => $_ENV['REDIS_PASSWORD'] ?? null,
            ],
            'vpn' => [
                'free_data_limit' => (int)($_ENV['FREE_DATA_LIMIT'] ?? **********),
                'free_duration_days' => (int)($_ENV['FREE_DURATION_DAYS'] ?? 7),
                'trial_data_limit' => (int)($_ENV['TRIAL_DATA_LIMIT'] ?? **********),
                'trial_duration_days' => (int)($_ENV['TRIAL_DURATION_DAYS'] ?? 1),
            ],
            'payments' => [
                'provider_token' => $_ENV['PAYMENT_PROVIDER_TOKEN'] ?? '',
                'nowpayments' => [
                    'api_key' => $_ENV['NOWPAYMENTS_API_KEY'] ?? '',
                    'ipn_secret' => $_ENV['NOWPAYMENTS_IPN_SECRET'] ?? '',
                ],
                'ton' => [
                    'master_wallet' => $_ENV['TON_MASTER_WALLET'] ?? '',
                    'private_key' => $_ENV['TON_MASTER_PRIVATE_KEY'] ?? '',
                    'network' => $_ENV['TON_NETWORK'] ?? 'mainnet',
                    'api_key' => $_ENV['TON_API_KEY'] ?? '',
                    'timeout_minutes' => (int)($_ENV['TON_PAYMENT_TIMEOUT_MINUTES'] ?? 30),
                ],
            ],
            'marzban' => [
                'url' => $_ENV['MARZBAN_URL'] ?? '',
                'username' => $_ENV['MARZBAN_USERNAME'] ?? '',
                'password' => $_ENV['MARZBAN_PASSWORD'] ?? '',
                'token' => $_ENV['MARZBAN_TOKEN'] ?? '',
                'timeout' => (int)($_ENV['MARZBAN_API_TIMEOUT'] ?? 30),
            ],
            'admin' => [
                'username' => $_ENV['ADMIN_PANEL_USERNAME'] ?? 'admin',
                'password' => $_ENV['ADMIN_PANEL_PASSWORD'] ?? '',
            ],
            'app' => [
                'env' => $_ENV['APP_ENV'] ?? 'production',
                'debug' => filter_var($_ENV['APP_DEBUG'] ?? false, FILTER_VALIDATE_BOOLEAN),
                'log_level' => $_ENV['LOG_LEVEL'] ?? 'info',
                'timezone' => $_ENV['TIMEZONE'] ?? 'UTC',
            ],
            'security' => [
                'jwt_secret' => $_ENV['JWT_SECRET'] ?? '',
                'encryption_key' => $_ENV['ENCRYPTION_KEY'] ?? '',
            ],
        ];
    }

    public function get(string $key, mixed $default = null): mixed
    {
        $keys = explode('.', $key);
        $value = $this->config;

        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                return $default;
            }
            $value = $value[$k];
        }

        return $value;
    }

    public function set(string $key, mixed $value): void
    {
        $keys = explode('.', $key);
        $config = &$this->config;

        foreach ($keys as $k) {
            if (!isset($config[$k])) {
                $config[$k] = [];
            }
            $config = &$config[$k];
        }

        $config = $value;
    }

    public function all(): array
    {
        return $this->config;
    }
}
