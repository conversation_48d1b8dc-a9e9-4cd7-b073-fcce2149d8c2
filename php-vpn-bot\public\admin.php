<?php

declare(strict_types=1);

require_once __DIR__ . '/../vendor/autoload.php';

use VpnBot\Config\Config;
use VpnBot\Utils\Logger;
use VpnBot\Database\Connection;

// Initialize configuration
$config = Config::getInstance();
$logger = Logger::getInstance();
Connection::setLogger($logger);

// Simple admin panel placeholder
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VPN Bot Admin Panel</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #007bff;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .status.active {
            background: #d4edda;
            color: #155724;
        }
        .status.inactive {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 VPN Bot Admin Panel</h1>
            <p>Manage your Telegram VPN bot</p>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">0</div>
                <div class="stat-label">Total Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">0</div>
                <div class="stat-label">Active VPN Accounts</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">0</div>
                <div class="stat-label">Premium Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">$0.00</div>
                <div class="stat-label">Total Revenue</div>
            </div>
        </div>

        <div class="section">
            <h2>🔧 System Status</h2>
            <p><strong>Bot Status:</strong> <span class="status active">Active</span></p>
            <p><strong>Database:</strong> <span class="status active">Connected</span></p>
            <p><strong>Redis:</strong> <span class="status active">Connected</span></p>
            <p><strong>Webhook:</strong> <span class="status active">Set</span></p>
        </div>

        <div class="section">
            <h2>📊 Recent Activity</h2>
            <p>No recent activity to display.</p>
        </div>

        <div class="section">
            <h2>⚙️ Quick Actions</h2>
            <p>
                <a href="/set-webhook" style="color: #007bff; text-decoration: none;">🔗 Set Webhook</a> |
                <a href="/health" style="color: #007bff; text-decoration: none;">❤️ Health Check</a> |
                <a href="/" style="color: #007bff; text-decoration: none;">🏠 Bot Home</a>
            </p>
        </div>

        <div class="section">
            <h2>📝 Configuration</h2>
            <p><strong>Bot Username:</strong> @<?= htmlspecialchars($config->get('bot.username', 'not_set')) ?></p>
            <p><strong>Environment:</strong> <?= htmlspecialchars($config->get('app.env', 'production')) ?></p>
            <p><strong>Debug Mode:</strong> <?= $config->get('app.debug', false) ? 'Enabled' : 'Disabled' ?></p>
            <p><strong>Log Level:</strong> <?= htmlspecialchars($config->get('app.log_level', 'info')) ?></p>
        </div>

        <div class="section">
            <h2>🔍 Logs</h2>
            <p>Check application logs for detailed information:</p>
            <ul>
                <li>Bot logs: <code>/var/www/html/logs/bot.log</code></li>
                <li>PHP errors: <code>/var/www/html/logs/php_errors.log</code></li>
                <li>Nginx logs: <code>/var/log/nginx/</code></li>
            </ul>
        </div>
    </div>
</body>
</html>
