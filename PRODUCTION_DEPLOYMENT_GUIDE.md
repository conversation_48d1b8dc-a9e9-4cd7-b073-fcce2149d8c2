# Production Deployment Guide

## 🚀 Production Readiness Checklist

### ✅ Critical Fixes Completed
- [x] **Syntax Errors Fixed**: Payment service `elif` statement syntax error resolved
- [x] **Import Errors Fixed**: All missing imports added (get_text in payment handlers)
- [x] **Localization Complete**: All hardcoded strings replaced with get_text() calls
- [x] **Database Schema**: PostgreSQL-compatible schema implemented
- [x] **Code Quality**: All Python files pass syntax validation

### 🔧 Environment Setup

#### 1. System Requirements
```bash
# Minimum Requirements
- Python 3.9+
- PostgreSQL 12+
- Redis 6+
- 2GB RAM
- 20GB Storage
- SSL Certificate

# Recommended for Production
- Python 3.11+
- PostgreSQL 14+
- Redis 7+
- 4GB RAM
- 50GB Storage
- Load Balancer
```

#### 2. Environment Variables
Create a `.env` file with all required variables:

```bash
# Core Bot Configuration
BOT_TOKEN=your_telegram_bot_token_here
ADMIN_USER_ID=your_telegram_user_id

# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_USER=vpn_bot_user
POSTGRES_PASSWORD=secure_random_password_here
POSTGRES_DATABASE=telegram_vpn_bot
POSTGRES_PORT=5432

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=redis_password_if_needed

# VPN Panel Configuration (Marzban)
MARZBAN_API_URL=https://your-marzban-panel.com
MARZBAN_USERNAME=admin
MARZBAN_PASSWORD=admin_password

# Payment Configuration
PAYMENT_PROVIDER_TOKEN=your_telegram_payment_provider_token
TON_API_KEY=your_ton_api_key
NOWPAYMENTS_API_KEY=your_nowpayments_api_key
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key

# Security
SECRET_KEY=your_secret_key_for_encryption
WEBHOOK_SECRET=your_webhook_secret_key

# Logging
LOG_LEVEL=INFO
LOG_FILE=/var/log/vpn_bot/bot.log

# Production Settings
DEBUG=False
ENVIRONMENT=production
```

### 🗄️ Database Setup

#### 1. PostgreSQL Installation
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# Start PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

#### 2. Database Creation
```bash
# Switch to postgres user
sudo -u postgres psql

# Create database and user
CREATE DATABASE telegram_vpn_bot;
CREATE USER vpn_bot_user WITH PASSWORD 'secure_random_password_here';
GRANT ALL PRIVILEGES ON DATABASE telegram_vpn_bot TO vpn_bot_user;
\q
```

#### 3. Schema Initialization
```bash
# Apply database schema
psql -U vpn_bot_user -d telegram_vpn_bot -f database_schema.sql
```

### 🔧 Redis Setup

#### 1. Redis Installation
```bash
# Ubuntu/Debian
sudo apt install redis-server

# Configure Redis
sudo nano /etc/redis/redis.conf

# Set password (uncomment and modify)
requirepass your_redis_password

# Restart Redis
sudo systemctl restart redis-server
sudo systemctl enable redis-server
```

### 🐳 Docker Deployment

#### 1. Docker Compose Configuration
```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: telegram_vpn_bot
      POSTGRES_USER: vpn_bot_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database_schema.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
    ports:
      - "6379:6379"
    restart: unless-stopped

  bot:
    build: .
    environment:
      - BOT_TOKEN=${BOT_TOKEN}
      - POSTGRES_HOST=postgres
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DATABASE=${POSTGRES_DATABASE}
      - REDIS_HOST=redis
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - MARZBAN_API_URL=${MARZBAN_API_URL}
      - MARZBAN_USERNAME=${MARZBAN_USERNAME}
      - MARZBAN_PASSWORD=${MARZBAN_PASSWORD}
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs

  admin:
    build: .
    command: python -m admin.main
    environment:
      - POSTGRES_HOST=postgres
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DATABASE=${POSTGRES_DATABASE}
    ports:
      - "8000:8000"
    depends_on:
      - postgres
    restart: unless-stopped

volumes:
  postgres_data:
```

#### 2. Dockerfile Optimization
```dockerfile
# Dockerfile
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create logs directory
RUN mkdir -p logs

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# Create non-root user
RUN useradd -m -u 1000 botuser && chown -R botuser:botuser /app
USER botuser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:8000/health')" || exit 1

# Default command
CMD ["python", "-m", "bot.main"]
```

### 🔒 Security Configuration

#### 1. Firewall Setup
```bash
# UFW Configuration
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 5432/tcp  # PostgreSQL (restrict to local network)
sudo ufw allow 6379/tcp  # Redis (restrict to local network)
```

#### 2. SSL Certificate
```bash
# Using Certbot for Let's Encrypt
sudo apt install certbot
sudo certbot certonly --standalone -d yourdomain.com
```

#### 3. Nginx Configuration
```nginx
# /etc/nginx/sites-available/vpn-bot
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name yourdomain.com;

    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;

    # Admin Panel
    location /admin {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Webhook endpoints
    location /webhook {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 📊 Monitoring and Logging

#### 1. Logging Configuration
```python
# bot/config.py - Enhanced logging
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'detailed': {
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        },
        'simple': {
            'format': '%(levelname)s - %(message)s'
        }
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/app/logs/bot.log',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5,
            'formatter': 'detailed'
        },
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'simple'
        }
    },
    'loggers': {
        '': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': False
        }
    }
}
```

#### 2. Health Check Endpoint
```python
# admin/health.py
from fastapi import APIRouter
from bot.database import get_db_connection
from bot.redis import redis_client

router = APIRouter()

@router.get("/health")
async def health_check():
    try:
        # Check database
        async with get_db_connection() as conn:
            await conn.fetchval("SELECT 1")
        
        # Check Redis
        await redis_client.ping()
        
        return {"status": "healthy", "timestamp": datetime.utcnow()}
    except Exception as e:
        return {"status": "unhealthy", "error": str(e)}
```

### 🚀 Deployment Steps

#### 1. Pre-deployment Testing
```bash
# Run comprehensive tests
python debug_and_test.py

# Check syntax of all files
find . -name "*.py" -exec python -m py_compile {} \;

# Test database connection
python -c "from bot.database import get_db_connection; import asyncio; asyncio.run(get_db_connection().__aenter__())"
```

#### 2. Production Deployment
```bash
# 1. Clone repository
git clone https://github.com/yourusername/telegram-vpn-bot.git
cd telegram-vpn-bot

# 2. Set up environment
cp .env.example .env
nano .env  # Configure all variables

# 3. Build and start services
docker-compose -f docker-compose.prod.yml up -d

# 4. Check logs
docker-compose logs -f bot

# 5. Test bot functionality
# Send /start to your bot on Telegram
```

#### 3. Post-deployment Verification
```bash
# Check all services are running
docker-compose ps

# Test database connection
docker-compose exec postgres psql -U vpn_bot_user -d telegram_vpn_bot -c "SELECT COUNT(*) FROM users;"

# Test Redis connection
docker-compose exec redis redis-cli ping

# Check bot logs
docker-compose logs bot | tail -50

# Test admin panel
curl https://yourdomain.com/admin/health
```

### 🔧 Maintenance and Updates

#### 1. Regular Maintenance
```bash
# Update dependencies
pip install --upgrade -r requirements.txt

# Database backup
pg_dump -U vpn_bot_user telegram_vpn_bot > backup_$(date +%Y%m%d).sql

# Log rotation
logrotate /etc/logrotate.d/vpn-bot

# Monitor disk space
df -h
```

#### 2. Update Procedure
```bash
# 1. Backup current version
docker-compose exec postgres pg_dump -U vpn_bot_user telegram_vpn_bot > backup_pre_update.sql

# 2. Pull latest changes
git pull origin main

# 3. Rebuild containers
docker-compose -f docker-compose.prod.yml build

# 4. Update services
docker-compose -f docker-compose.prod.yml up -d

# 5. Verify functionality
python debug_and_test.py
```

### 📞 Troubleshooting

#### Common Issues and Solutions

1. **Bot Not Responding**
   ```bash
   # Check bot logs
   docker-compose logs bot
   
   # Verify token
   curl https://api.telegram.org/bot${BOT_TOKEN}/getMe
   ```

2. **Database Connection Error**
   ```bash
   # Check PostgreSQL status
   docker-compose exec postgres pg_isready
   
   # Test connection
   docker-compose exec postgres psql -U vpn_bot_user -d telegram_vpn_bot
   ```

3. **Payment Issues**
   ```bash
   # Check payment provider status
   # Verify webhook endpoints
   # Review payment logs
   ```

### 📈 Performance Optimization

#### 1. Database Optimization
```sql
-- Add indexes for better performance
CREATE INDEX CONCURRENTLY idx_users_telegram_id ON users(telegram_id);
CREATE INDEX CONCURRENTLY idx_vpn_accounts_user_active ON vpn_accounts(user_id, is_active);
```

#### 2. Redis Caching
```python
# Implement caching for frequently accessed data
@cache_result(ttl=300)  # 5 minutes
async def get_user_data(user_id: int):
    # Cached user data retrieval
    pass
```

#### 3. Connection Pooling
```python
# Optimize database connections
DATABASE_CONFIG = {
    'min_size': 10,
    'max_size': 20,
    'max_queries': 50000,
    'max_inactive_connection_lifetime': 300.0,
}
```

This production deployment guide ensures your VPN bot is secure, scalable, and maintainable in a production environment.
