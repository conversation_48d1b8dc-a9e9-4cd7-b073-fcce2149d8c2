import asyncio
import logging
import json
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import aiohttp
from bot.config import settings
from bot.database import get_db_connection

logger = logging.getLogger(__name__)

class TONService:
    """Service for handling TON Connect integration and payments."""
    
    def __init__(self):
        self.logger = logger
        self.ton_api_url = "https://toncenter.com/api/v2"
        self.ton_api_key = getattr(settings, 'TON_API_KEY', None)
        self.manifest_url = getattr(settings, 'TON_CONNECT_MANIFEST_URL', None)
        
    async def get_ton_price_usd(self) -> Optional[float]:
        """Get current TON price in USD."""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    "https://api.coingecko.com/api/v3/simple/price?ids=the-open-network&vs_currencies=usd"
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get('the-open-network', {}).get('usd')
            return None
        except Exception as e:
            self.logger.error(f"Error fetching TON price: {e}")
            return None
    
    async def calculate_ton_amount(self, usd_amount: float) -> Optional[float]:
        """Calculate TON amount for given USD amount."""
        try:
            ton_price = await self.get_ton_price_usd()
            if ton_price:
                return round(usd_amount / ton_price, 6)
            return None
        except Exception as e:
            self.logger.error(f"Error calculating TON amount: {e}")
            return None
    
    async def generate_payment_address(self, user_id: int, plan_id: int) -> Optional[Dict[str, Any]]:
        """Generate a unique payment address for TON payment."""
        try:
            # For now, we'll use a simple approach with a master wallet
            # In production, you should use a proper wallet generation system
            master_wallet = getattr(settings, 'TON_MASTER_WALLET', None)
            if not master_wallet:
                self.logger.error("TON master wallet not configured")
                return None
            
            # Create a unique payment identifier
            payment_id = f"ton_{user_id}_{plan_id}_{int(datetime.now().timestamp())}"
            
            return {
                'payment_id': payment_id,
                'wallet_address': master_wallet,
                'memo': payment_id  # Use payment_id as memo for identification
            }
        except Exception as e:
            self.logger.error(f"Error generating payment address: {e}")
            return None
    
    async def create_ton_payment(self, user_id: int, plan_id: int) -> Optional[Dict[str, Any]]:
        """Create a TON payment for a premium plan."""
        try:
            # Get plan details
            async with get_db_connection() as conn:
                plan = await conn.fetchrow(
                    "SELECT * FROM premium_plans WHERE id = $1", plan_id
                )
                
                if not plan:
                    return None
            
            # Calculate TON amount
            ton_amount = await self.calculate_ton_amount(plan['price'])
            if not ton_amount:
                return None
            
            # Generate payment address
            payment_data = await self.generate_payment_address(user_id, plan_id)
            if not payment_data:
                return None
            
            # Store payment in database
            async with get_db_connection() as conn:
                payment_record = await conn.fetchrow(
                    """
                    INSERT INTO ton_payments (
                        payment_id, user_id, plan_id, wallet_address, 
                        ton_amount, usd_amount, memo, status, expires_at, created_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW())
                    RETURNING *
                    """,
                    payment_data['payment_id'],
                    user_id,
                    plan_id,
                    payment_data['wallet_address'],
                    ton_amount,
                    plan['price'],
                    payment_data['memo'],
                    'waiting',
                    datetime.now() + timedelta(hours=1)  # 1 hour expiry
                )
            
            return {
                'payment_id': payment_data['payment_id'],
                'wallet_address': payment_data['wallet_address'],
                'ton_amount': ton_amount,
                'usd_amount': plan['price'],
                'memo': payment_data['memo'],
                'expires_at': payment_record['expires_at'],
                'plan_name': plan['name']
            }
            
        except Exception as e:
            self.logger.error(f"Error creating TON payment: {e}")
            return None
    
    async def verify_ton_transaction(self, wallet_address: str, memo: str, expected_amount: float) -> Optional[Dict[str, Any]]:
        """Verify TON transaction by checking wallet transactions."""
        try:
            if not self.ton_api_key:
                self.logger.error("TON API key not configured")
                return None
            
            async with aiohttp.ClientSession() as session:
                # Get wallet transactions
                params = {
                    'address': wallet_address,
                    'limit': 50,
                    'api_key': self.ton_api_key
                }
                
                async with session.get(
                    f"{self.ton_api_url}/getTransactions",
                    params=params
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        transactions = data.get('result', [])
                        
                        # Look for matching transaction
                        for tx in transactions:
                            # Check if transaction has the expected memo and amount
                            if self._check_transaction_match(tx, memo, expected_amount):
                                return {
                                    'transaction_hash': tx.get('transaction_id', {}).get('hash'),
                                    'amount': tx.get('in_msg', {}).get('value', 0),
                                    'timestamp': tx.get('utime'),
                                    'confirmed': True
                                }
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error verifying TON transaction: {e}")
            return None
    
    def _check_transaction_match(self, transaction: Dict[str, Any], memo: str, expected_amount: float) -> bool:
        """Check if transaction matches expected memo and amount."""
        try:
            # Check if transaction is incoming
            in_msg = transaction.get('in_msg')
            if not in_msg:
                return False
            
            # Check amount (convert from nanotons)
            tx_amount = float(in_msg.get('value', 0)) / 1e9
            amount_match = abs(tx_amount - expected_amount) < 0.001  # Allow small difference
            
            # Check memo in message body
            message_body = in_msg.get('message', '')
            memo_match = memo in message_body
            
            return amount_match and memo_match
            
        except Exception as e:
            self.logger.error(f"Error checking transaction match: {e}")
            return False
    
    async def check_ton_payment_status(self, payment_id: str) -> Optional[Dict[str, Any]]:
        """Check TON payment status and verify transaction."""
        try:
            async with get_db_connection() as conn:
                payment = await conn.fetchrow(
                    "SELECT * FROM ton_payments WHERE payment_id = $1",
                    payment_id
                )
                
                if not payment:
                    return None
                
                # If already confirmed, return status
                if payment['status'] == 'confirmed':
                    return {
                        'status': 'confirmed',
                        'payment_id': payment_id,
                        'transaction_hash': payment.get('transaction_hash')
                    }
                
                # Check if payment expired
                if payment['expires_at'] < datetime.now():
                    await conn.execute(
                        "UPDATE ton_payments SET status = $1 WHERE payment_id = $2",
                        'expired', payment_id
                    )
                    return {'status': 'expired', 'payment_id': payment_id}
                
                # Verify transaction
                transaction = await self.verify_ton_transaction(
                    payment['wallet_address'],
                    payment['memo'],
                    payment['ton_amount']
                )
                
                if transaction:
                    # Update payment status
                    await conn.execute(
                        """
                        UPDATE ton_payments 
                        SET status = $1, transaction_hash = $2, confirmed_at = NOW()
                        WHERE payment_id = $3
                        """,
                        'confirmed', transaction['transaction_hash'], payment_id
                    )
                    
                    # Process successful payment
                    await self._process_ton_payment_success(
                        payment['user_id'], 
                        payment['plan_id'], 
                        payment
                    )
                    
                    return {
                        'status': 'confirmed',
                        'payment_id': payment_id,
                        'transaction_hash': transaction['transaction_hash']
                    }
                
                return {'status': 'waiting', 'payment_id': payment_id}
                
        except Exception as e:
            self.logger.error(f"Error checking TON payment status: {e}")
            return None
    
    async def _process_ton_payment_success(self, user_id: int, plan_id: int, payment_record: Dict[str, Any]) -> None:
        """Process successful TON payment."""
        try:
            async with get_db_connection() as conn:
                # Get plan details
                plan = await conn.fetchrow(
                    "SELECT * FROM premium_plans WHERE id = $1", plan_id
                )
                
                if not plan:
                    return
                
                # Create premium subscription
                subscription = await conn.fetchrow(
                    """
                    INSERT INTO premium_subscriptions 
                    (user_id, plan_id, ton_payment_id, expires_at, created_at)
                    VALUES ($1, $2, $3, $4, NOW())
                    RETURNING *
                    """,
                    user_id, plan_id, payment_record['id'],
                    datetime.now() + timedelta(days=plan['duration_days'])
                )
                
                # Create VPN account
                from .vpn_service import VPNService
                vpn_service = VPNService()
                vpn_account = await vpn_service.create_premium_vpn(
                    user_id=user_id,
                    plan_id=plan_id,
                    duration_days=plan['duration_days'],
                    data_limit_gb=plan['data_limit_gb']
                )
                
                if vpn_account:
                    self.logger.info(
                        f"Processed TON payment for user {user_id}, plan {plan_id}, "
                        f"payment {payment_record['payment_id']}"
                    )
                    
        except Exception as e:
            self.logger.error(f"Error processing TON payment success: {e}")
    
    async def get_user_ton_payments(self, user_id: int) -> List[Dict[str, Any]]:
        """Get user's TON payment history."""
        try:
            async with get_db_connection() as conn:
                payments = await conn.fetch(
                    """
                    SELECT tp.*, pp.name as plan_name
                    FROM ton_payments tp
                    JOIN premium_plans pp ON tp.plan_id = pp.id
                    WHERE tp.user_id = $1
                    ORDER BY tp.created_at DESC
                    """,
                    user_id
                )
                
                return [dict(payment) for payment in payments]
                
        except Exception as e:
            self.logger.error(f"Error getting user TON payments: {e}")
            return []

# Global instance
ton_service = TONService()