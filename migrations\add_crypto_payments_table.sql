-- Migration: Add crypto_payments table for NowPayments integration
-- Date: 2025-01-26
-- Description: Creates table to track cryptocurrency payments via NowPayments

CREATE TABLE IF NOT EXISTS crypto_payments (
    id SERIAL PRIMARY KEY,
    payment_id VARCHAR(255) UNIQUE NOT NULL,
    user_id BIGINT NOT NULL,
    plan_id INTEGER NOT NULL,
    order_id VARCHAR(255) UNIQUE NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    currency VARCHAR(10) NOT NULL,
    pay_currency VARCHAR(10),
    pay_amount DECIMAL(20, 8),
    status VARCHAR(50) NOT NULL DEFAULT 'waiting',
    payment_url TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    
    CONSTRAINT fk_crypto_payments_user_id 
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_crypto_payments_plan_id 
        FOREIGN KEY (plan_id) REFERENCES premium_plans(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_crypto_payments_user_id ON crypto_payments(user_id);
CREATE INDEX IF NOT EXISTS idx_crypto_payments_status ON crypto_payments(status);
CREATE INDEX IF NOT EXISTS idx_crypto_payments_payment_id ON crypto_payments(payment_id);
CREATE INDEX IF NOT EXISTS idx_crypto_payments_order_id ON crypto_payments(order_id);
CREATE INDEX IF NOT EXISTS idx_crypto_payments_created_at ON crypto_payments(created_at);

-- Add crypto_payment_id column to premium_subscriptions table
ALTER TABLE premium_subscriptions 
ADD COLUMN crypto_payment_id INTEGER REFERENCES crypto_payments(id);

-- Add index for crypto_payment_id
CREATE INDEX idx_premium_subscriptions_crypto_payment_id ON premium_subscriptions(crypto_payment_id);

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_crypto_payments_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_crypto_payments_updated_at
    BEFORE UPDATE ON crypto_payments
    FOR EACH ROW
    EXECUTE FUNCTION update_crypto_payments_updated_at();

-- Add comments for documentation
COMMENT ON TABLE crypto_payments IS 'Tracks cryptocurrency payments via NowPayments';
COMMENT ON COLUMN crypto_payments.payment_id IS 'NowPayments payment ID';
COMMENT ON COLUMN crypto_payments.order_id IS 'Unique order identifier';
COMMENT ON COLUMN crypto_payments.status IS 'Payment status: waiting, confirming, confirmed, sending, partially_paid, finished, failed, refunded, expired';
COMMENT ON COLUMN crypto_payments.pay_currency IS 'Cryptocurrency used for payment (e.g., btc, eth)';
COMMENT ON COLUMN crypto_payments.pay_amount IS 'Amount in cryptocurrency';