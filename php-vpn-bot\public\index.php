<?php

declare(strict_types=1);

require_once __DIR__ . '/../vendor/autoload.php';

use VpnBot\Config\Config;
use VpnBot\Utils\Logger;
use VpnBot\Bot\TelegramBot;
use VpnBot\Database\Connection;

// Initialize configuration
$config = Config::getInstance();

// Initialize logger
$logger = Logger::getInstance();
Connection::setLogger($logger);

// Set timezone
date_default_timezone_set($config->get('app.timezone', 'UTC'));

// Error handling
set_error_handler(function ($severity, $message, $file, $line) use ($logger) {
    if (!(error_reporting() & $severity)) {
        return false;
    }
    $logger->error("PHP Error: $message in $file:$line");
    return true;
});

set_exception_handler(function (Throwable $exception) use ($logger) {
    $logger->error('Uncaught exception: ' . $exception->getMessage(), [
        'file' => $exception->getFile(),
        'line' => $exception->getLine(),
        'trace' => $exception->getTraceAsString()
    ]);
});

try {
    // Initialize bot
    $bot = new TelegramBot($config, $logger);
    
    // Handle webhook or set webhook
    $requestMethod = $_SERVER['REQUEST_METHOD'] ?? 'GET';
    $requestUri = $_SERVER['REQUEST_URI'] ?? '/';
    
    if ($requestMethod === 'POST' && $requestUri === '/webhook') {
        // Handle webhook
        $input = file_get_contents('php://input');
        if ($input) {
            $bot->handleWebhook($input);
        }
    } elseif ($requestMethod === 'GET' && $requestUri === '/set-webhook') {
        // Set webhook
        $result = $bot->setWebhook();
        header('Content-Type: application/json');
        echo json_encode($result);
    } elseif ($requestMethod === 'GET' && $requestUri === '/health') {
        // Health check
        header('Content-Type: application/json');
        echo json_encode(['status' => 'ok', 'timestamp' => time()]);
    } else {
        // Default response
        http_response_code(404);
        echo 'Not Found';
    }
} catch (Throwable $e) {
    $logger->error('Application error: ' . $e->getMessage(), [
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ]);
    
    http_response_code(500);
    echo 'Internal Server Error';
}
