#!/bin/bash

# PHP VPN Bot Deployment Script
# This script helps deploy the PHP VPN Telegram bot

set -e

echo "🚀 PHP VPN Bot Deployment Script"
echo "================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root for security reasons"
   exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    echo "Run: curl -fsSL https://get.docker.com -o get-docker.sh && sh get-docker.sh"
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    echo "Run: sudo curl -L \"https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-\$(uname -s)-\$(uname -m)\" -o /usr/local/bin/docker-compose"
    echo "Then: sudo chmod +x /usr/local/bin/docker-compose"
    exit 1
fi

print_step "Checking prerequisites..."
print_status "Docker: $(docker --version)"
print_status "Docker Compose: $(docker-compose --version)"

# Check if .env file exists
if [ ! -f .env ]; then
    print_step "Creating .env file from template..."
    if [ -f .env.example ]; then
        cp .env.example .env
        print_status ".env file created from .env.example"
        print_warning "Please edit .env file with your configuration before continuing"
        echo ""
        echo "Required configuration:"
        echo "- BOT_TOKEN: Your Telegram bot token"
        echo "- BOT_USERNAME: Your bot username"
        echo "- WEBHOOK_URL: Your domain URL"
        echo "- ADMIN_USER_ID: Your Telegram user ID"
        echo "- MARZBAN_URL: Your Marzban panel URL"
        echo "- MARZBAN_USERNAME: Marzban admin username"
        echo "- MARZBAN_PASSWORD: Marzban admin password"
        echo ""
        read -p "Press Enter after configuring .env file..."
    else
        print_error ".env.example file not found"
        exit 1
    fi
fi

# Validate required environment variables
print_step "Validating configuration..."
source .env

required_vars=("BOT_TOKEN" "BOT_USERNAME" "WEBHOOK_URL")
missing_vars=()

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -ne 0 ]; then
    print_error "Missing required environment variables:"
    for var in "${missing_vars[@]}"; do
        echo "  - $var"
    done
    exit 1
fi

print_status "Configuration validated"

# Create necessary directories
print_step "Creating directories..."
mkdir -p logs
chmod 755 logs
print_status "Directories created"

# Build and start services
print_step "Building and starting services..."
docker-compose down --remove-orphans
docker-compose build --no-cache
docker-compose up -d

print_status "Services started"

# Wait for services to be ready
print_step "Waiting for services to be ready..."
sleep 10

# Check if services are running
if ! docker-compose ps | grep -q "Up"; then
    print_error "Some services failed to start"
    docker-compose logs
    exit 1
fi

print_status "All services are running"

# Run database migrations
print_step "Running database migrations..."
sleep 5  # Wait a bit more for PostgreSQL to be fully ready

# Check if PostgreSQL is ready
max_attempts=30
attempt=1
while [ $attempt -le $max_attempts ]; do
    if docker-compose exec -T postgres pg_isready -U vpn_bot -d telegram_vpn_bot; then
        print_status "PostgreSQL is ready"
        break
    fi
    print_warning "Waiting for PostgreSQL... (attempt $attempt/$max_attempts)"
    sleep 2
    ((attempt++))
done

if [ $attempt -gt $max_attempts ]; then
    print_error "PostgreSQL failed to start within expected time"
    exit 1
fi

# Run migrations
if docker-compose exec -T postgres psql -U vpn_bot -d telegram_vpn_bot -f /docker-entrypoint-initdb.d/001_create_tables.sql; then
    print_status "Database migrations completed"
else
    print_warning "Database migrations may have already been run"
fi

# Install PHP dependencies
print_step "Installing PHP dependencies..."
docker-compose exec -T app composer install --no-dev --optimize-autoloader --no-interaction
print_status "PHP dependencies installed"

# Set webhook
print_step "Setting Telegram webhook..."
webhook_response=$(curl -s -o /dev/null -w "%{http_code}" "${WEBHOOK_URL}/set-webhook")

if [ "$webhook_response" = "200" ]; then
    print_status "Webhook set successfully"
else
    print_warning "Failed to set webhook (HTTP $webhook_response)"
    print_warning "You may need to set it manually later"
fi

# Test health endpoint
print_step "Testing health endpoint..."
health_response=$(curl -s -o /dev/null -w "%{http_code}" "${WEBHOOK_URL}/health")

if [ "$health_response" = "200" ]; then
    print_status "Health check passed"
else
    print_warning "Health check failed (HTTP $health_response)"
fi

# Display final status
echo ""
echo "🎉 Deployment completed!"
echo "======================="
print_status "Bot URL: ${WEBHOOK_URL}"
print_status "Admin Panel: ${WEBHOOK_URL}/admin"
print_status "Health Check: ${WEBHOOK_URL}/health"
echo ""
print_status "Services running:"
docker-compose ps

echo ""
print_status "To view logs: docker-compose logs -f"
print_status "To stop services: docker-compose down"
print_status "To restart services: docker-compose restart"

echo ""
print_warning "Next steps:"
echo "1. Test your bot by sending /start to @${BOT_USERNAME}"
echo "2. Configure your channels in the admin panel"
echo "3. Set up your payment providers"
echo "4. Configure your Marzban panel integration"

echo ""
print_status "Deployment completed successfully! 🚀"
