"""Payment verification service for tracking and validating payments."""

import asyncio
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from bot.database import get_db_connection
from bot.services.payment_service import payment_service
from bot.services.nowpayments_service import nowpayments_service
from bot.services.ton_service import ton_service


class PaymentVerificationService:
    """Service for verifying and tracking payment statuses."""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def verify_pending_payments(self) -> Dict[str, Any]:
        """Verify all pending payments and update their statuses."""
        try:
            results = {
                'total_checked': 0,
                'completed': 0,
                'failed': 0,
                'still_pending': 0,
                'errors': 0,
                'details': []
            }
            
            # Get pending payments from different sources
            crypto_payments = await self._get_pending_crypto_payments()
            ton_payments = await self._get_pending_ton_payments()
            
            # Verify crypto payments
            for payment in crypto_payments:
                results['total_checked'] += 1
                try:
                    status = await self._verify_crypto_payment(payment)
                    results['details'].append({
                        'payment_id': payment['payment_id'],
                        'type': 'crypto',
                        'status': status,
                        'user_id': payment['user_id']
                    })
                    
                    if status == 'completed':
                        results['completed'] += 1
                    elif status == 'failed':
                        results['failed'] += 1
                    else:
                        results['still_pending'] += 1
                        
                except Exception as e:
                    results['errors'] += 1
                    self.logger.error(f"Error verifying crypto payment {payment['payment_id']}: {e}")
            
            # Verify TON payments
            for payment in ton_payments:
                results['total_checked'] += 1
                try:
                    status = await self._verify_ton_payment(payment)
                    results['details'].append({
                        'payment_id': payment['payment_id'],
                        'type': 'ton',
                        'status': status,
                        'user_id': payment['user_id']
                    })
                    
                    if status == 'completed':
                        results['completed'] += 1
                    elif status == 'failed':
                        results['failed'] += 1
                    else:
                        results['still_pending'] += 1
                        
                except Exception as e:
                    results['errors'] += 1
                    self.logger.error(f"Error verifying TON payment {payment['payment_id']}: {e}")
            
            return results
        
        except Exception as e:
            self.logger.error(f"Error verifying pending payments: {e}")
            return {
                'total_checked': 0,
                'completed': 0,
                'failed': 0,
                'still_pending': 0,
                'errors': 1,
                'error': str(e)
            }
    
    async def _get_pending_crypto_payments(self) -> List[Dict[str, Any]]:
        """Get pending crypto payments from database."""
        try:
            async with get_db_connection() as conn:
                payments = await conn.fetch(
                    """
                    SELECT * FROM crypto_payments 
                    WHERE status IN ('waiting', 'confirming') 
                    AND created_at > NOW() - INTERVAL '24 hours'
                    ORDER BY created_at ASC
                    """
                )
                return [dict(payment) for payment in payments]
        except Exception as e:
            self.logger.error(f"Error getting pending crypto payments: {e}")
            return []
    
    async def _get_pending_ton_payments(self) -> List[Dict[str, Any]]:
        """Get pending TON payments from database."""
        try:
            async with get_db_connection() as conn:
                payments = await conn.fetch(
                    """
                    SELECT * FROM ton_payments 
                    WHERE status IN ('waiting', 'confirming') 
                    AND created_at > NOW() - INTERVAL '24 hours'
                    ORDER BY created_at ASC
                    """
                )
                return [dict(payment) for payment in payments]
        except Exception as e:
            self.logger.error(f"Error getting pending TON payments: {e}")
            return []
    
    async def _verify_crypto_payment(self, payment: Dict[str, Any]) -> str:
        """Verify a specific crypto payment."""
        try:
            payment_id = payment['payment_id']
            status_data = await nowpayments_service.get_payment_status(payment_id)
            
            if not status_data:
                return 'unknown'
            
            status = status_data.get('payment_status', '').lower()
            
            if status in ['finished', 'partially_paid']:
                # Payment completed, process it
                await self._process_completed_crypto_payment(payment, status_data)
                return 'completed'
            elif status in ['failed', 'refunded', 'expired']:
                # Payment failed
                await self._mark_payment_failed(payment['id'], 'crypto', status)
                return 'failed'
            else:
                # Still pending
                return 'pending'
        
        except Exception as e:
            self.logger.error(f"Error verifying crypto payment {payment.get('payment_id')}: {e}")
            return 'error'
    
    async def _verify_ton_payment(self, payment: Dict[str, Any]) -> str:
        """Verify a specific TON payment."""
        try:
            payment_id = payment['payment_id']
            status_data = await ton_service.check_ton_payment_status(payment_id)
            
            if not status_data:
                return 'unknown'
            
            status = status_data.get('status', '').lower()
            
            if status == 'completed':
                # Payment completed, process it
                await self._process_completed_ton_payment(payment, status_data)
                return 'completed'
            elif status in ['failed', 'expired']:
                # Payment failed
                await self._mark_payment_failed(payment['id'], 'ton', status)
                return 'failed'
            else:
                # Still pending
                return 'pending'
        
        except Exception as e:
            self.logger.error(f"Error verifying TON payment {payment.get('payment_id')}: {e}")
            return 'error'
    
    async def _process_completed_crypto_payment(self, payment: Dict[str, Any], status_data: Dict[str, Any]) -> None:
        """Process a completed crypto payment."""
        try:
            # Update payment status
            async with get_db_connection() as conn:
                await conn.execute(
                    "UPDATE crypto_payments SET status = 'completed', updated_at = NOW() WHERE id = $1",
                    payment['id']
                )
            
            # Process the payment through payment service
            payment_data = {
                'plan_id': payment['plan_id'],
                'payment_method': 'crypto',
                'amount': status_data.get('actually_paid', payment.get('amount')),
                'currency': status_data.get('pay_currency', payment.get('currency')),
                'payment_id': payment['payment_id']
            }
            
            result = await payment_service.process_successful_payment(
                payment['user_id'],
                payment_data,
                'crypto'
            )
            
            if result and result.get('success'):
                self.logger.info(f"Successfully processed completed crypto payment {payment['payment_id']}")
            else:
                self.logger.error(f"Failed to process completed crypto payment {payment['payment_id']}")
        
        except Exception as e:
            self.logger.error(f"Error processing completed crypto payment {payment['payment_id']}: {e}")
    
    async def _process_completed_ton_payment(self, payment: Dict[str, Any], status_data: Dict[str, Any]) -> None:
        """Process a completed TON payment."""
        try:
            # Update payment status
            async with get_db_connection() as conn:
                await conn.execute(
                    "UPDATE ton_payments SET status = 'completed', updated_at = NOW() WHERE id = $1",
                    payment['id']
                )
            
            # Process the payment through payment service
            payment_data = {
                'plan_id': payment['plan_id'],
                'payment_method': 'ton',
                'amount': payment['usd_amount'],
                'currency': 'USD',
                'payment_id': payment['payment_id']
            }
            
            result = await payment_service.process_successful_payment(
                payment['user_id'],
                payment_data,
                'ton'
            )
            
            if result and result.get('success'):
                self.logger.info(f"Successfully processed completed TON payment {payment['payment_id']}")
            else:
                self.logger.error(f"Failed to process completed TON payment {payment['payment_id']}")
        
        except Exception as e:
            self.logger.error(f"Error processing completed TON payment {payment['payment_id']}: {e}")
    
    async def _mark_payment_failed(self, payment_db_id: int, payment_type: str, reason: str) -> None:
        """Mark a payment as failed in the database."""
        try:
            async with get_db_connection() as conn:
                if payment_type == 'crypto':
                    await conn.execute(
                        "UPDATE crypto_payments SET status = 'failed', failure_reason = $1, updated_at = NOW() WHERE id = $2",
                        reason, payment_db_id
                    )
                elif payment_type == 'ton':
                    await conn.execute(
                        "UPDATE ton_payments SET status = 'failed', failure_reason = $1, updated_at = NOW() WHERE id = $2",
                        reason, payment_db_id
                    )
        
        except Exception as e:
            self.logger.error(f"Error marking {payment_type} payment {payment_db_id} as failed: {e}")
    
    async def cleanup_expired_payments(self) -> Dict[str, Any]:
        """Clean up expired payments that are no longer valid."""
        try:
            results = {
                'crypto_cleaned': 0,
                'ton_cleaned': 0,
                'total_cleaned': 0
            }
            
            async with get_db_connection() as conn:
                # Clean up expired crypto payments
                crypto_result = await conn.execute(
                    """
                    UPDATE crypto_payments 
                    SET status = 'expired', updated_at = NOW() 
                    WHERE status IN ('waiting', 'confirming') 
                    AND created_at < NOW() - INTERVAL '2 hours'
                    """
                )
                results['crypto_cleaned'] = crypto_result.rowcount if hasattr(crypto_result, 'rowcount') else 0
                
                # Clean up expired TON payments
                ton_result = await conn.execute(
                    """
                    UPDATE ton_payments 
                    SET status = 'expired', updated_at = NOW() 
                    WHERE status IN ('waiting', 'confirming') 
                    AND expires_at < NOW()
                    """
                )
                results['ton_cleaned'] = ton_result.rowcount if hasattr(ton_result, 'rowcount') else 0
                
                results['total_cleaned'] = results['crypto_cleaned'] + results['ton_cleaned']
            
            return results
        
        except Exception as e:
            self.logger.error(f"Error cleaning up expired payments: {e}")
            return {
                'crypto_cleaned': 0,
                'ton_cleaned': 0,
                'total_cleaned': 0,
                'error': str(e)
            }


# Global instance
payment_verification_service = PaymentVerificationService()
