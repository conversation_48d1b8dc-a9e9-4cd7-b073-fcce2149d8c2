# Security Guide

This guide covers security best practices, authentication, authorization, data protection, and threat mitigation for the VPN Telegram Bot project.

## Table of Contents

1. [Security Overview](#security-overview)
2. [Authentication & Authorization](#authentication--authorization)
3. [Data Protection](#data-protection)
4. [Input Validation & Sanitization](#input-validation--sanitization)
5. [API Security](#api-security)
6. [Database Security](#database-security)
7. [Infrastructure Security](#infrastructure-security)
8. [Monitoring & Incident Response](#monitoring--incident-response)
9. [Compliance & Privacy](#compliance--privacy)
10. [Security Checklist](#security-checklist)

## Security Overview

### Security Principles

Our security approach follows these core principles:

- **Defense in Depth**: Multiple layers of security controls
- **Least Privilege**: Minimal access rights for users and services
- **Zero Trust**: Never trust, always verify
- **Security by Design**: Security built into the architecture
- **Continuous Monitoring**: Real-time threat detection and response

### Threat Model

Key threats we protect against:

- **Unauthorized Access**: Preventing unauthorized bot usage
- **Data Breaches**: Protecting user and payment data
- **API Abuse**: Rate limiting and input validation
- **Injection Attacks**: SQL injection, command injection
- **Man-in-the-Middle**: Encrypted communications
- **DDoS Attacks**: Rate limiting and traffic filtering
- **Social Engineering**: User education and verification

## Authentication & Authorization

### User Authentication

```python
# bot/security/auth.py
import hashlib
import hmac
import time
import jwt
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from bot.config import settings
from bot.utils.logging import LoggerMixin
from bot.utils.validators import DataValidator

class AuthenticationError(Exception):
    """Authentication related errors."""
    pass

class AuthorizationError(Exception):
    """Authorization related errors."""
    pass

class TelegramAuth(LoggerMixin):
    """Telegram-specific authentication."""
    
    def __init__(self, bot_token: str):
        self.bot_token = bot_token
        self.validator = DataValidator()
    
    def verify_telegram_data(self, init_data: str) -> Dict[str, Any]:
        """Verify Telegram WebApp init data."""
        try:
            # Parse init data
            data_dict = self._parse_init_data(init_data)
            
            # Extract and verify hash
            received_hash = data_dict.pop('hash', None)
            if not received_hash:
                raise AuthenticationError("Missing hash in init data")
            
            # Create data check string
            data_check_string = '\n'.join(
                f"{k}={v}" for k, v in sorted(data_dict.items())
            )
            
            # Calculate expected hash
            secret_key = hmac.new(
                b"WebAppData",
                self.bot_token.encode(),
                hashlib.sha256
            ).digest()
            
            expected_hash = hmac.new(
                secret_key,
                data_check_string.encode(),
                hashlib.sha256
            ).hexdigest()
            
            # Verify hash
            if not hmac.compare_digest(received_hash, expected_hash):
                raise AuthenticationError("Invalid hash")
            
            # Check timestamp (data should be recent)
            auth_date = int(data_dict.get('auth_date', 0))
            if time.time() - auth_date > 86400:  # 24 hours
                raise AuthenticationError("Init data is too old")
            
            self.log_with_context(
                logging.INFO,
                "Telegram data verified successfully",
                user_id=data_dict.get('user', {}).get('id'),
                action="telegram_auth_success"
            )
            
            return data_dict
            
        except Exception as e:
            self.log_with_context(
                logging.ERROR,
                "Telegram data verification failed",
                error=str(e),
                action="telegram_auth_failed"
            )
            raise AuthenticationError(f"Verification failed: {e}")
    
    def _parse_init_data(self, init_data: str) -> Dict[str, Any]:
        """Parse Telegram init data string."""
        import urllib.parse
        import json
        
        data_dict = {}
        
        for item in init_data.split('&'):
            key, value = item.split('=', 1)
            key = urllib.parse.unquote(key)
            value = urllib.parse.unquote(value)
            
            # Parse JSON fields
            if key in ['user', 'chat', 'chat_instance']:
                try:
                    data_dict[key] = json.loads(value)
                except json.JSONDecodeError:
                    data_dict[key] = value
            else:
                data_dict[key] = value
        
        return data_dict

class JWTAuth(LoggerMixin):
    """JWT-based authentication for admin panel."""
    
    def __init__(self):
        self.secret_key = settings.JWT_SECRET_KEY
        self.algorithm = 'HS256'
        self.access_token_expire = timedelta(hours=1)
        self.refresh_token_expire = timedelta(days=7)
    
    def create_access_token(self, user_id: int, permissions: list = None) -> str:
        """Create JWT access token."""
        payload = {
            'user_id': user_id,
            'permissions': permissions or [],
            'type': 'access',
            'exp': datetime.utcnow() + self.access_token_expire,
            'iat': datetime.utcnow(),
            'jti': self._generate_jti()
        }
        
        token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
        
        self.log_with_context(
            logging.INFO,
            "Access token created",
            user_id=user_id,
            permissions=permissions,
            action="token_created"
        )
        
        return token
    
    def create_refresh_token(self, user_id: int) -> str:
        """Create JWT refresh token."""
        payload = {
            'user_id': user_id,
            'type': 'refresh',
            'exp': datetime.utcnow() + self.refresh_token_expire,
            'iat': datetime.utcnow(),
            'jti': self._generate_jti()
        }
        
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
    
    def verify_token(self, token: str) -> Dict[str, Any]:
        """Verify and decode JWT token."""
        try:
            payload = jwt.decode(
                token, self.secret_key, algorithms=[self.algorithm]
            )
            
            # Check if token is blacklisted
            if await self._is_token_blacklisted(payload.get('jti')):
                raise AuthenticationError("Token is blacklisted")
            
            return payload
            
        except jwt.ExpiredSignatureError:
            raise AuthenticationError("Token has expired")
        except jwt.InvalidTokenError as e:
            raise AuthenticationError(f"Invalid token: {e}")
    
    def refresh_access_token(self, refresh_token: str) -> str:
        """Create new access token from refresh token."""
        payload = self.verify_token(refresh_token)
        
        if payload.get('type') != 'refresh':
            raise AuthenticationError("Invalid token type")
        
        # Get user permissions
        user_id = payload['user_id']
        permissions = await self._get_user_permissions(user_id)
        
        return self.create_access_token(user_id, permissions)
    
    def revoke_token(self, token: str):
        """Add token to blacklist."""
        try:
            payload = jwt.decode(
                token, self.secret_key, algorithms=[self.algorithm],
                options={"verify_exp": False}
            )
            jti = payload.get('jti')
            exp = payload.get('exp')
            
            if jti and exp:
                await self._blacklist_token(jti, exp)
                
        except jwt.InvalidTokenError:
            pass  # Invalid tokens don't need to be blacklisted
    
    def _generate_jti(self) -> str:
        """Generate unique token ID."""
        import uuid
        return str(uuid.uuid4())
    
    async def _is_token_blacklisted(self, jti: str) -> bool:
        """Check if token is blacklisted."""
        from bot.redis import get_redis
        redis = await get_redis()
        return await redis.exists(f"blacklist:{jti}")
    
    async def _blacklist_token(self, jti: str, exp: int):
        """Add token to blacklist."""
        from bot.redis import get_redis
        redis = await get_redis()
        ttl = exp - int(time.time())
        if ttl > 0:
            await redis.setex(f"blacklist:{jti}", ttl, "1")
    
    async def _get_user_permissions(self, user_id: int) -> list:
        """Get user permissions from database."""
        from bot.database import get_db
        async with get_db() as db:
            result = await db.fetchrow(
                "SELECT permissions FROM admin_users WHERE user_id = $1",
                user_id
            )
            return result['permissions'] if result else []

class PermissionManager:
    """Manage user permissions and roles."""
    
    # Define permission levels
    PERMISSIONS = {
        'user.read': 'View user information',
        'user.write': 'Modify user information',
        'user.delete': 'Delete users',
        'vpn.read': 'View VPN accounts',
        'vpn.write': 'Modify VPN accounts',
        'vpn.delete': 'Delete VPN accounts',
        'payment.read': 'View payment information',
        'payment.write': 'Process payments',
        'admin.read': 'View admin panel',
        'admin.write': 'Modify system settings',
        'analytics.read': 'View analytics',
        'logs.read': 'View system logs'
    }
    
    # Define roles
    ROLES = {
        'viewer': ['user.read', 'vpn.read', 'payment.read', 'analytics.read'],
        'operator': ['user.read', 'user.write', 'vpn.read', 'vpn.write', 'payment.read'],
        'admin': ['user.*', 'vpn.*', 'payment.*', 'admin.read', 'analytics.read', 'logs.read'],
        'super_admin': ['*']
    }
    
    @classmethod
    def has_permission(cls, user_permissions: list, required_permission: str) -> bool:
        """Check if user has required permission."""
        # Check for wildcard permissions
        if '*' in user_permissions:
            return True
        
        # Check for exact permission
        if required_permission in user_permissions:
            return True
        
        # Check for wildcard in permission category
        permission_parts = required_permission.split('.')
        if len(permission_parts) == 2:
            wildcard_permission = f"{permission_parts[0]}.*"
            if wildcard_permission in user_permissions:
                return True
        
        return False
    
    @classmethod
    def get_role_permissions(cls, role: str) -> list:
        """Get permissions for a role."""
        return cls.ROLES.get(role, [])
    
    @classmethod
    def expand_permissions(cls, permissions: list) -> list:
        """Expand wildcard permissions to specific permissions."""
        expanded = set()
        
        for permission in permissions:
            if permission == '*':
                expanded.update(cls.PERMISSIONS.keys())
            elif permission.endswith('.*'):
                category = permission[:-2]
                expanded.update([
                    perm for perm in cls.PERMISSIONS.keys()
                    if perm.startswith(f"{category}.")
                ])
            else:
                expanded.add(permission)
        
        return list(expanded)
```

### Authorization Decorators

```python
# bot/security/decorators.py
from functools import wraps
from typing import List, Union
from bot.security.auth import AuthenticationError, AuthorizationError, PermissionManager
from bot.utils.logging import LoggerMixin

class SecurityDecorator(LoggerMixin):
    """Base class for security decorators."""
    pass

def require_auth(permissions: Union[str, List[str]] = None):
    """Require authentication and optional permissions."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract context (this would depend on your framework)
            context = kwargs.get('context') or (args[1] if len(args) > 1 else None)
            
            if not context or not hasattr(context, 'user_data'):
                raise AuthenticationError("Authentication required")
            
            user_data = context.user_data
            user_permissions = user_data.get('permissions', [])
            
            # Check permissions if specified
            if permissions:
                required_perms = permissions if isinstance(permissions, list) else [permissions]
                
                for perm in required_perms:
                    if not PermissionManager.has_permission(user_permissions, perm):
                        raise AuthorizationError(f"Permission '{perm}' required")
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator

def require_admin(func):
    """Require admin privileges."""
    return require_auth(['admin.read'])(func)

def require_super_admin(func):
    """Require super admin privileges."""
    return require_auth(['*'])(func)

def rate_limit_user(max_requests: int = 10, window_seconds: int = 60):
    """Rate limit per user."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            from bot.redis import get_redis
            import time
            
            # Extract user ID
            context = kwargs.get('context') or (args[1] if len(args) > 1 else None)
            if not context or not hasattr(context, 'user_data'):
                raise AuthenticationError("Authentication required")
            
            user_id = context.user_data.get('user_id')
            if not user_id:
                raise AuthenticationError("User ID not found")
            
            # Check rate limit
            redis = await get_redis()
            key = f"rate_limit:user:{user_id}:{func.__name__}"
            
            current_time = int(time.time())
            window_start = current_time - window_seconds
            
            # Remove old entries
            await redis.zremrangebyscore(key, 0, window_start)
            
            # Count current requests
            current_requests = await redis.zcard(key)
            
            if current_requests >= max_requests:
                raise AuthorizationError("Rate limit exceeded")
            
            # Add current request
            await redis.zadd(key, {str(current_time): current_time})
            await redis.expire(key, window_seconds)
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator
```

## Data Protection

### Encryption

```python
# bot/security/encryption.py
import base64
import hashlib
import secrets
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from typing import str, bytes
from bot.config import settings
from bot.utils.logging import LoggerMixin

class EncryptionManager(LoggerMixin):
    """Handle data encryption and decryption."""
    
    def __init__(self):
        self.master_key = settings.ENCRYPTION_KEY.encode()
    
    def generate_key(self, password: str, salt: bytes = None) -> bytes:
        """Generate encryption key from password."""
        if salt is None:
            salt = secrets.token_bytes(16)
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return key, salt
    
    def encrypt_data(self, data: str, key: bytes = None) -> str:
        """Encrypt sensitive data."""
        try:
            if key is None:
                key = self.master_key
            
            f = Fernet(key)
            encrypted_data = f.encrypt(data.encode())
            
            return base64.urlsafe_b64encode(encrypted_data).decode()
            
        except Exception as e:
            self.logger.error(f"Encryption failed: {e}")
            raise
    
    def decrypt_data(self, encrypted_data: str, key: bytes = None) -> str:
        """Decrypt sensitive data."""
        try:
            if key is None:
                key = self.master_key
            
            f = Fernet(key)
            decoded_data = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = f.decrypt(decoded_data)
            
            return decrypted_data.decode()
            
        except Exception as e:
            self.logger.error(f"Decryption failed: {e}")
            raise
    
    def hash_password(self, password: str) -> str:
        """Hash password with salt."""
        import bcrypt
        
        salt = bcrypt.gensalt()
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        
        return hashed.decode('utf-8')
    
    def verify_password(self, password: str, hashed: str) -> bool:
        """Verify password against hash."""
        import bcrypt
        
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    
    def generate_secure_token(self, length: int = 32) -> str:
        """Generate cryptographically secure token."""
        return secrets.token_urlsafe(length)
    
    def hash_data(self, data: str, algorithm: str = 'sha256') -> str:
        """Hash data with specified algorithm."""
        if algorithm == 'sha256':
            return hashlib.sha256(data.encode()).hexdigest()
        elif algorithm == 'sha512':
            return hashlib.sha512(data.encode()).hexdigest()
        else:
            raise ValueError(f"Unsupported algorithm: {algorithm}")

class PIIProtection:
    """Protect Personally Identifiable Information."""
    
    def __init__(self, encryption_manager: EncryptionManager):
        self.encryption = encryption_manager
    
    def mask_email(self, email: str) -> str:
        """Mask email address for logging."""
        if '@' not in email:
            return email
        
        local, domain = email.split('@', 1)
        if len(local) <= 2:
            masked_local = '*' * len(local)
        else:
            masked_local = local[0] + '*' * (len(local) - 2) + local[-1]
        
        return f"{masked_local}@{domain}"
    
    def mask_phone(self, phone: str) -> str:
        """Mask phone number for logging."""
        if len(phone) <= 4:
            return '*' * len(phone)
        
        return phone[:2] + '*' * (len(phone) - 4) + phone[-2:]
    
    def mask_ip(self, ip: str) -> str:
        """Mask IP address for logging."""
        parts = ip.split('.')
        if len(parts) == 4:
            return f"{parts[0]}.{parts[1]}.*.* "
        return ip
    
    def encrypt_pii(self, data: dict) -> dict:
        """Encrypt PII fields in data."""
        pii_fields = ['email', 'phone', 'first_name', 'last_name', 'address']
        encrypted_data = data.copy()
        
        for field in pii_fields:
            if field in encrypted_data and encrypted_data[field]:
                encrypted_data[field] = self.encryption.encrypt_data(
                    str(encrypted_data[field])
                )
        
        return encrypted_data
    
    def decrypt_pii(self, data: dict) -> dict:
        """Decrypt PII fields in data."""
        pii_fields = ['email', 'phone', 'first_name', 'last_name', 'address']
        decrypted_data = data.copy()
        
        for field in pii_fields:
            if field in decrypted_data and decrypted_data[field]:
                try:
                    decrypted_data[field] = self.encryption.decrypt_data(
                        decrypted_data[field]
                    )
                except Exception:
                    # Field might not be encrypted
                    pass
        
        return decrypted_data
```

### Data Anonymization

```python
# bot/security/anonymization.py
import hashlib
import random
from typing import Dict, Any, List
from datetime import datetime, timedelta

class DataAnonymizer:
    """Anonymize sensitive data for analytics and testing."""
    
    def __init__(self, salt: str = None):
        self.salt = salt or "default_salt"
    
    def anonymize_user_id(self, user_id: int) -> str:
        """Create anonymous but consistent user identifier."""
        data = f"{user_id}{self.salt}"
        return hashlib.sha256(data.encode()).hexdigest()[:16]
    
    def anonymize_ip(self, ip: str) -> str:
        """Anonymize IP address while preserving network information."""
        parts = ip.split('.')
        if len(parts) == 4:
            # Keep first two octets, anonymize last two
            return f"{parts[0]}.{parts[1]}.0.0"
        return "0.0.0.0"
    
    def anonymize_email(self, email: str) -> str:
        """Create anonymous email while preserving domain."""
        if '@' not in email:
            return "<EMAIL>"
        
        local, domain = email.split('@', 1)
        anonymous_local = hashlib.md5(f"{local}{self.salt}".encode()).hexdigest()[:8]
        
        return f"{anonymous_local}@{domain}"
    
    def anonymize_dataset(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Anonymize entire dataset."""
        anonymized = []
        
        for record in data:
            anonymized_record = record.copy()
            
            # Anonymize common PII fields
            if 'user_id' in anonymized_record:
                anonymized_record['user_id'] = self.anonymize_user_id(
                    anonymized_record['user_id']
                )
            
            if 'email' in anonymized_record:
                anonymized_record['email'] = self.anonymize_email(
                    anonymized_record['email']
                )
            
            if 'ip_address' in anonymized_record:
                anonymized_record['ip_address'] = self.anonymize_ip(
                    anonymized_record['ip_address']
                )
            
            # Remove direct identifiers
            sensitive_fields = ['first_name', 'last_name', 'phone', 'address']
            for field in sensitive_fields:
                if field in anonymized_record:
                    del anonymized_record[field]
            
            # Add noise to numerical data
            if 'amount' in anonymized_record:
                anonymized_record['amount'] = self._add_noise(
                    anonymized_record['amount']
                )
            
            anonymized.append(anonymized_record)
        
        return anonymized
    
    def _add_noise(self, value: float, noise_factor: float = 0.1) -> float:
        """Add random noise to numerical values."""
        noise = random.uniform(-noise_factor, noise_factor) * value
        return round(value + noise, 2)
    
    def create_synthetic_data(self, template: Dict[str, Any], count: int) -> List[Dict[str, Any]]:
        """Generate synthetic data based on template."""
        synthetic_data = []
        
        for i in range(count):
            record = {}
            
            for field, value in template.items():
                if field == 'user_id':
                    record[field] = f"synthetic_{i+1}"
                elif field == 'email':
                    record[field] = f"user{i+1}@example.com"
                elif field == 'created_at':
                    # Random date within last year
                    days_ago = random.randint(0, 365)
                    record[field] = datetime.now() - timedelta(days=days_ago)
                elif isinstance(value, (int, float)):
                    # Generate random value within reasonable range
                    record[field] = random.uniform(value * 0.5, value * 1.5)
                elif isinstance(value, str):
                    record[field] = f"synthetic_{field}_{i+1}"
                else:
                    record[field] = value
            
            synthetic_data.append(record)
        
        return synthetic_data
```

## Input Validation & Sanitization

### Advanced Input Validation

```python
# bot/security/validation.py
import re
import html
import urllib.parse
from typing import Any, Dict, List, Union
from bot.utils.validators import BaseValidator
from bot.utils.logging import LoggerMixin

class SecurityValidator(BaseValidator, LoggerMixin):
    """Security-focused input validation."""
    
    # Common attack patterns
    SQL_INJECTION_PATTERNS = [
        r"('|(\-\-)|(;)|(\||\|)|(\*|\*))",
        r"(union|select|insert|delete|update|drop|create|alter|exec|execute)",
        r"(script|javascript|vbscript|onload|onerror|onclick)"
    ]
    
    XSS_PATTERNS = [
        r"<script[^>]*>.*?</script>",
        r"javascript:",
        r"on\w+\s*=",
        r"<iframe[^>]*>.*?</iframe>",
        r"<object[^>]*>.*?</object>",
        r"<embed[^>]*>.*?</embed>"
    ]
    
    COMMAND_INJECTION_PATTERNS = [
        r"[;&|`$(){}\[\]]",
        r"(rm|cat|ls|ps|kill|chmod|chown|sudo|su)",
        r"(\.\./|\.\.\\)"
    ]
    
    def validate_and_sanitize(self, value: Any, validation_type: str) -> Any:
        """Validate and sanitize input based on type."""
        if value is None:
            return None
        
        # Convert to string for validation
        str_value = str(value)
        
        # Check for malicious patterns
        self._check_security_patterns(str_value)
        
        # Type-specific validation and sanitization
        if validation_type == 'html':
            return self._sanitize_html(str_value)
        elif validation_type == 'sql':
            return self._sanitize_sql(str_value)
        elif validation_type == 'url':
            return self._sanitize_url(str_value)
        elif validation_type == 'filename':
            return self._sanitize_filename(str_value)
        elif validation_type == 'command':
            return self._sanitize_command(str_value)
        else:
            return self._sanitize_general(str_value)
    
    def _check_security_patterns(self, value: str):
        """Check for common attack patterns."""
        value_lower = value.lower()
        
        # Check SQL injection patterns
        for pattern in self.SQL_INJECTION_PATTERNS:
            if re.search(pattern, value_lower, re.IGNORECASE):
                self.logger.warning(
                    f"Potential SQL injection detected: {pattern}",
                    extra={'input_value': value[:100]}
                )
                raise ValueError("Invalid input detected")
        
        # Check XSS patterns
        for pattern in self.XSS_PATTERNS:
            if re.search(pattern, value_lower, re.IGNORECASE):
                self.logger.warning(
                    f"Potential XSS detected: {pattern}",
                    extra={'input_value': value[:100]}
                )
                raise ValueError("Invalid input detected")
        
        # Check command injection patterns
        for pattern in self.COMMAND_INJECTION_PATTERNS:
            if re.search(pattern, value, re.IGNORECASE):
                self.logger.warning(
                    f"Potential command injection detected: {pattern}",
                    extra={'input_value': value[:100]}
                )
                raise ValueError("Invalid input detected")
    
    def _sanitize_html(self, value: str) -> str:
        """Sanitize HTML content."""
        # HTML escape
        sanitized = html.escape(value)
        
        # Remove potentially dangerous tags
        dangerous_tags = ['script', 'iframe', 'object', 'embed', 'form']
        for tag in dangerous_tags:
            pattern = f"<{tag}[^>]*>.*?</{tag}>"
            sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE | re.DOTALL)
        
        return sanitized
    
    def _sanitize_sql(self, value: str) -> str:
        """Sanitize SQL input (though parameterized queries are preferred)."""
        # Escape single quotes
        sanitized = value.replace("'", "''")
        
        # Remove SQL comments
        sanitized = re.sub(r'--.*$', '', sanitized, flags=re.MULTILINE)
        sanitized = re.sub(r'/\*.*?\*/', '', sanitized, flags=re.DOTALL)
        
        return sanitized
    
    def _sanitize_url(self, value: str) -> str:
        """Sanitize URL input."""
        # URL encode
        sanitized = urllib.parse.quote(value, safe=':/?#[]@!$&\'()*+,;=')
        
        # Check for valid URL scheme
        if '://' in sanitized:
            scheme = sanitized.split('://')[0].lower()
            if scheme not in ['http', 'https', 'ftp', 'ftps']:
                raise ValueError("Invalid URL scheme")
        
        return sanitized
    
    def _sanitize_filename(self, value: str) -> str:
        """Sanitize filename input."""
        # Remove path traversal attempts
        sanitized = value.replace('..', '').replace('/', '').replace('\\', '')
        
        # Remove dangerous characters
        sanitized = re.sub(r'[<>:"|?*]', '', sanitized)
        
        # Limit length
        sanitized = sanitized[:255]
        
        return sanitized
    
    def _sanitize_command(self, value: str) -> str:
        """Sanitize command input (should be avoided if possible)."""
        # Remove dangerous characters
        sanitized = re.sub(r'[;&|`$(){}\[\]]', '', value)
        
        # Remove path traversal
        sanitized = sanitized.replace('..', '')
        
        return sanitized
    
    def _sanitize_general(self, value: str) -> str:
        """General sanitization."""
        # Remove null bytes
        sanitized = value.replace('\x00', '')
        
        # Normalize whitespace
        sanitized = re.sub(r'\s+', ' ', sanitized).strip()
        
        # Limit length
        sanitized = sanitized[:10000]
        
        return sanitized
    
    def validate_file_upload(self, filename: str, content: bytes, max_size: int = 10485760) -> bool:
        """Validate file upload."""
        # Check file size
        if len(content) > max_size:
            raise ValueError(f"File too large: {len(content)} bytes")
        
        # Check filename
        safe_filename = self._sanitize_filename(filename)
        if not safe_filename:
            raise ValueError("Invalid filename")
        
        # Check file extension
        allowed_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.txt']
        file_ext = '.' + filename.split('.')[-1].lower() if '.' in filename else ''
        
        if file_ext not in allowed_extensions:
            raise ValueError(f"File type not allowed: {file_ext}")
        
        # Check file signature (magic bytes)
        if not self._validate_file_signature(content, file_ext):
            raise ValueError("File signature mismatch")
        
        return True
    
    def _validate_file_signature(self, content: bytes, expected_ext: str) -> bool:
        """Validate file signature matches extension."""
        if len(content) < 4:
            return False
        
        # Common file signatures
        signatures = {
            '.jpg': [b'\xff\xd8\xff'],
            '.jpeg': [b'\xff\xd8\xff'],
            '.png': [b'\x89\x50\x4e\x47'],
            '.gif': [b'\x47\x49\x46\x38'],
            '.pdf': [b'\x25\x50\x44\x46']
        }
        
        if expected_ext in signatures:
            for signature in signatures[expected_ext]:
                if content.startswith(signature):
                    return True
            return False
        
        return True  # Allow unknown types for now
```

## API Security

### Rate Limiting

```python
# bot/security/rate_limiting.py
import time
import asyncio
from typing import Dict, Optional
from dataclasses import dataclass
from bot.redis import get_redis
from bot.utils.logging import LoggerMixin

@dataclass
class RateLimitConfig:
    """Rate limit configuration."""
    max_requests: int
    window_seconds: int
    burst_limit: int = None
    block_duration: int = 300  # 5 minutes

class RateLimiter(LoggerMixin):
    """Advanced rate limiting with multiple strategies."""
    
    def __init__(self):
        self.configs = {
            'default': RateLimitConfig(60, 60),  # 60 requests per minute
            'auth': RateLimitConfig(5, 300),     # 5 auth attempts per 5 minutes
            'payment': RateLimitConfig(10, 3600), # 10 payments per hour
            'admin': RateLimitConfig(1000, 60),   # 1000 requests per minute for admin
        }
    
    async def check_rate_limit(self, 
                              identifier: str, 
                              limit_type: str = 'default',
                              cost: int = 1) -> bool:
        """Check if request is within rate limit."""
        config = self.configs.get(limit_type, self.configs['default'])
        redis = await get_redis()
        
        current_time = int(time.time())
        window_start = current_time - config.window_seconds
        
        # Check if identifier is blocked
        block_key = f"rate_limit:block:{identifier}:{limit_type}"
        if await redis.exists(block_key):
            self.logger.warning(
                f"Rate limit blocked request",
                extra={
                    'identifier': identifier,
                    'limit_type': limit_type,
                    'action': 'rate_limit_blocked'
                }
            )
            return False
        
        # Sliding window rate limiting
        key = f"rate_limit:{identifier}:{limit_type}"
        
        # Remove old entries
        await redis.zremrangebyscore(key, 0, window_start)
        
        # Count current requests
        current_requests = await redis.zcard(key)
        
        # Check if adding this request would exceed limit
        if current_requests + cost > config.max_requests:
            # Block the identifier for block_duration
            await redis.setex(block_key, config.block_duration, "1")
            
            self.logger.warning(
                f"Rate limit exceeded",
                extra={
                    'identifier': identifier,
                    'limit_type': limit_type,
                    'current_requests': current_requests,
                    'max_requests': config.max_requests,
                    'action': 'rate_limit_exceeded'
                }
            )
            return False
        
        # Add current request(s)
        pipeline = redis.pipeline()
        for i in range(cost):
            pipeline.zadd(key, {f"{current_time}_{i}": current_time})
        pipeline.expire(key, config.window_seconds)
        await pipeline.execute()
        
        return True
    
    async def get_rate_limit_status(self, 
                                   identifier: str, 
                                   limit_type: str = 'default') -> Dict[str, int]:
        """Get current rate limit status."""
        config = self.configs.get(limit_type, self.configs['default'])
        redis = await get_redis()
        
        current_time = int(time.time())
        window_start = current_time - config.window_seconds
        
        key = f"rate_limit:{identifier}:{limit_type}"
        
        # Remove old entries
        await redis.zremrangebyscore(key, 0, window_start)
        
        # Count current requests
        current_requests = await redis.zcard(key)
        
        # Check if blocked
        block_key = f"rate_limit:block:{identifier}:{limit_type}"
        block_ttl = await redis.ttl(block_key)
        
        return {
            'current_requests': current_requests,
            'max_requests': config.max_requests,
            'window_seconds': config.window_seconds,
            'remaining_requests': max(0, config.max_requests - current_requests),
            'reset_time': current_time + config.window_seconds,
            'blocked': block_ttl > 0,
            'block_remaining': max(0, block_ttl)
        }
    
    async def reset_rate_limit(self, identifier: str, limit_type: str = 'default'):
        """Reset rate limit for identifier."""
        redis = await get_redis()
        
        key = f"rate_limit:{identifier}:{limit_type}"
        block_key = f"rate_limit:block:{identifier}:{limit_type}"
        
        await redis.delete(key, block_key)
        
        self.logger.info(
            f"Rate limit reset",
            extra={
                'identifier': identifier,
                'limit_type': limit_type,
                'action': 'rate_limit_reset'
            }
        )

class IPRateLimiter(RateLimiter):
    """IP-based rate limiting."""
    
    def __init__(self):
        super().__init__()
        # More restrictive limits for IP-based limiting
        self.configs.update({
            'ip_default': RateLimitConfig(100, 60),    # 100 requests per minute per IP
            'ip_strict': RateLimitConfig(10, 60),      # 10 requests per minute per IP
            'ip_auth': RateLimitConfig(3, 300),        # 3 auth attempts per 5 minutes per IP
        })
    
    async def check_ip_rate_limit(self, ip_address: str, limit_type: str = 'ip_default') -> bool:
        """Check rate limit for IP address."""
        # Anonymize IP for privacy
        ip_hash = hashlib.sha256(f"{ip_address}:{settings.RATE_LIMIT_SALT}".encode()).hexdigest()[:16]
        
        return await self.check_rate_limit(ip_hash, limit_type)

class AdaptiveRateLimiter(RateLimiter):
    """Adaptive rate limiting based on system load."""
    
    def __init__(self):
        super().__init__()
        self.load_factor = 1.0
        self.last_load_check = 0
    
    async def check_adaptive_rate_limit(self, 
                                       identifier: str, 
                                       limit_type: str = 'default') -> bool:
        """Check rate limit with adaptive scaling."""
        # Update load factor periodically
        current_time = time.time()
        if current_time - self.last_load_check > 60:  # Check every minute
            await self._update_load_factor()
            self.last_load_check = current_time
        
        # Adjust rate limit based on load
        config = self.configs.get(limit_type, self.configs['default'])
        adjusted_limit = int(config.max_requests * self.load_factor)
        
        # Create temporary config with adjusted limit
        temp_config = RateLimitConfig(
            max_requests=adjusted_limit,
            window_seconds=config.window_seconds,
            block_duration=config.block_duration
        )
        
        # Temporarily replace config
        original_config = self.configs[limit_type]
        self.configs[limit_type] = temp_config
        
        try:
            result = await self.check_rate_limit(identifier, limit_type)
        finally:
            # Restore original config
            self.configs[limit_type] = original_config
        
        return result
    
    async def _update_load_factor(self):
        """Update load factor based on system metrics."""
        try:
            import psutil
            
            # Get system metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            memory_percent = psutil.virtual_memory().percent
            
            # Calculate load factor (lower load = higher limits)
            cpu_factor = max(0.1, 1.0 - (cpu_percent / 100))
            memory_factor = max(0.1, 1.0 - (memory_percent / 100))
            
            self.load_factor = min(cpu_factor, memory_factor)
            
            self.logger.info(
                f"Updated adaptive rate limit factor",
                extra={
                    'load_factor': self.load_factor,
                    'cpu_percent': cpu_percent,
                    'memory_percent': memory_percent,
                    'action': 'adaptive_rate_limit_update'
                }
            )
            
        except Exception as e:
            self.logger.error(f"Failed to update load factor: {e}")
            self.load_factor = 0.5  # Conservative fallback

# Global rate limiter instances
rate_limiter = RateLimiter()
ip_rate_limiter = IPRateLimiter()
adaptive_rate_limiter = AdaptiveRateLimiter()
```

## Database Security

### SQL Injection Prevention

```python
# bot/security/database.py
import asyncpg
from typing import Any, Dict, List, Union
from bot.utils.logging import LoggerMixin

class SecureDatabase(LoggerMixin):
    """Database wrapper with security features."""
    
    def __init__(self, pool: asyncpg.Pool):
        self.pool = pool
    
    async def execute_query(self, 
                           query: str, 
                           params: tuple = None,
                           allow_multiple: bool = False) -> Any:
        """Execute query with security checks."""
        # Validate query
        self._validate_query(query, allow_multiple)
        
        # Log query (without sensitive data)
        self._log_query(query, params)
        
        async with self.pool.acquire() as conn:
            try:
                if params:
                    result = await conn.fetch(query, *params)
                else:
                    result = await conn.fetch(query)
                
                return result
                
            except Exception as e:
                self.logger.error(
                    f"Database query failed",
                    extra={
                        'query': query[:100],
                        'error': str(e),
                        'action': 'database_error'
                    }
                )
                raise
    
    def _validate_query(self, query: str, allow_multiple: bool):
        """Validate query for security issues."""
        query_lower = query.lower().strip()
        
        # Check for multiple statements
        if not allow_multiple and ';' in query_lower[:-1]:  # Allow trailing semicolon
            raise ValueError("Multiple statements not allowed")
        
        # Check for dangerous operations
        dangerous_operations = [
            'drop table', 'drop database', 'truncate table',
            'alter table', 'create table', 'create database',
            'grant', 'revoke', 'shutdown', 'exec', 'execute'
        ]
        
        for operation in dangerous_operations:
            if operation in query_lower:
                raise ValueError(f"Dangerous operation detected: {operation}")
        
        # Check for SQL injection patterns
        injection_patterns = [
            r"union\s+select", r"or\s+1\s*=\s*1", r"and\s+1\s*=\s*1",
            r"'\s*or\s*'.*'\s*=\s*'", r"--", r"/\*", r"\*/"
        ]
        
        import re
        for pattern in injection_patterns:
            if re.search(pattern, query_lower):
                self.logger.warning(
                    f"Potential SQL injection detected",
                    extra={
                        'pattern': pattern,
                        'query': query[:100],
                        'action': 'sql_injection_attempt'
                    }
                )
                raise ValueError("Suspicious query pattern detected")
    
    def _log_query(self, query: str, params: tuple):
        """Log query execution (without sensitive data)."""
        # Mask sensitive parameters
        masked_params = self._mask_sensitive_params(params) if params else None
        
        self.logger.debug(
            f"Executing database query",
            extra={
                'query': query[:200],
                'param_count': len(params) if params else 0,
                'action': 'database_query'
            }
        )
    
    def _mask_sensitive_params(self, params: tuple) -> tuple:
        """Mask sensitive parameters in logs."""
        masked = []
        
        for param in params:
            if isinstance(param, str) and len(param) > 10:
                # Potentially sensitive string
                masked.append(f"{param[:3]}***{param[-3:]}")
            else:
                masked.append(param)
        
        return tuple(masked)
    
    async def execute_transaction(self, queries: List[Dict[str, Any]]) -> List[Any]:
        """Execute multiple queries in a transaction."""
        async with self.pool.acquire() as conn:
            async with conn.transaction():
                results = []
                
                for query_info in queries:
                    query = query_info['query']
                    params = query_info.get('params')
                    
                    self._validate_query(query, allow_multiple=False)
                    
                    if params:
                        result = await conn.fetch(query, *params)
                    else:
                        result = await conn.fetch(query)
                    
                    results.append(result)
                
                return results
    
    async def get_connection_info(self) -> Dict[str, Any]:
        """Get database connection information for monitoring."""
        return {
            'pool_size': self.pool.get_size(),
            'pool_free': self.pool.get_idle_size(),
            'pool_used': self.pool.get_size() - self.pool.get_idle_size()
        }
```

## Infrastructure Security

### Docker Security

```dockerfile
# Dockerfile.secure
FROM python:3.11-slim as builder

# Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir --user -r requirements.txt

# Production stage
FROM python:3.11-slim

# Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Copy Python dependencies
COPY --from=builder /root/.local /home/<USER>/.local

# Set PATH
ENV PATH=/home/<USER>/.local/bin:$PATH

# Create app directory
WORKDIR /app

# Copy application code
COPY --chown=appuser:appuser . .

# Remove unnecessary files
RUN find . -name "*.pyc" -delete && \
    find . -name "__pycache__" -delete && \
    rm -rf .git .gitignore README.md docs/ tests/

# Set security options
RUN chmod -R 755 /app && \
    chmod 644 /app/*.py

# Switch to non-root user
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:8000/health')"

# Run application
CMD ["python", "main.py"]
```

```yaml
# docker-compose.secure.yml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.secure
    restart: unless-stopped
    read_only: true
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE
    security_opt:
      - no-new-privileges:true
      - apparmor:docker-default
    ulimits:
      nproc: 65535
      nofile:
        soft: 65535
        hard: 65535
    environment:
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
    networks:
      - app-network
    depends_on:
      - db
      - redis

  db:
    image: postgres:15-alpine
    restart: unless-stopped
    read_only: true
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
      - /var/run/postgresql:noexec,nosuid,size=100m
    volumes:
      - postgres_data:/var/lib/postgresql/data:Z
    environment:
      POSTGRES_DB: ${DB_NAME}
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    security_opt:
      - no-new-privileges:true
    networks:
      - app-network

  redis:
    image: redis:7-alpine
    restart: unless-stopped
    read_only: true
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
    volumes:
      - redis_data:/data:Z
    command: >
      redis-server
      --requirepass ${REDIS_PASSWORD}
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
    security_opt:
      - no-new-privileges:true
    networks:
      - app-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  app-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

## Security Checklist

### Development Security Checklist

- [ ] **Authentication & Authorization**
  - [ ] Implement strong authentication mechanisms
  - [ ] Use JWT tokens with proper expiration
  - [ ] Implement role-based access control (RBAC)
  - [ ] Validate Telegram WebApp data
  - [ ] Implement session management

- [ ] **Input Validation**
  - [ ] Validate all user inputs
  - [ ] Sanitize data before processing
  - [ ] Implement file upload validation
  - [ ] Check for injection attacks
  - [ ] Validate file signatures

- [ ] **Data Protection**
  - [ ] Encrypt sensitive data at rest
  - [ ] Use HTTPS for all communications
  - [ ] Implement proper key management
  - [ ] Hash passwords with salt
  - [ ] Anonymize logs and analytics data

- [ ] **API Security**
  - [ ] Implement rate limiting
  - [ ] Use API versioning
  - [ ] Validate request signatures
  - [ ] Implement CORS properly
  - [ ] Use security headers

- [ ] **Database Security**
  - [ ] Use parameterized queries
  - [ ] Implement connection pooling
  - [ ] Encrypt database connections
  - [ ] Regular security updates
  - [ ] Database access logging

- [ ] **Infrastructure Security**
  - [ ] Use non-root containers
  - [ ] Implement network segmentation
  - [ ] Regular security scans
  - [ ] Secure configuration management
  - [ ] Monitor for vulnerabilities

- [ ] **Monitoring & Logging**
  - [ ] Implement security event logging
  - [ ] Set up intrusion detection
  - [ ] Monitor for anomalies
  - [ ] Implement alerting
  - [ ] Regular security audits

### Production Security Checklist

- [ ] **Environment Security**
  - [ ] Secure environment variables
  - [ ] Network firewall configuration
  - [ ] SSL/TLS certificates
  - [ ] Regular security updates
  - [ ] Backup encryption

- [ ] **Access Control**
  - [ ] Principle of least privilege
  - [ ] Multi-factor authentication
  - [ ] Regular access reviews
  - [ ] Secure key storage
  - [ ] Admin access logging

- [ ] **Compliance**
  - [ ] GDPR compliance
  - [ ] Data retention policies
  - [ ] Privacy policy
  - [ ] Terms of service
  - [ ] Regular compliance audits

This security guide provides comprehensive protection for the VPN Telegram Bot project. Implement these security measures to protect against common threats and ensure data privacy.