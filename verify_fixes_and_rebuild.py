#!/usr/bin/env python3
"""
Comprehensive fix verification and Docker rebuild script.
This script verifies all fixes are applied correctly and provides Docker rebuild instructions.
"""

import asyncio
import logging
import sys
import os
import subprocess
from typing import Dict, Any, List
import traceback

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fix_verification.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class FixVerifier:
    """Comprehensive fix verification class."""
    
    def __init__(self):
        self.results = {}
        self.errors = []
    
    async def run_all_verifications(self):
        """Run all fix verifications."""
        logger.info("🔍 Starting comprehensive fix verification...")
        
        verifications = [
            ("Import Fix Verification", self.verify_import_fixes),
            ("Syntax Error Fixes", self.verify_syntax_fixes),
            ("Localization Completeness", self.verify_localization),
            ("Payment Service Fix", self.verify_payment_service),
            ("Referral Handler Fix", self.verify_referral_handler),
            ("Database Schema Validation", self.verify_database_schema),
            ("Configuration Validation", self.verify_configuration),
            ("Docker Readiness", self.verify_docker_readiness),
        ]
        
        for test_name, test_func in verifications:
            logger.info(f"🔍 Running: {test_name}")
            try:
                result = await test_func()
                self.results[test_name] = result
                if result.get('success', False):
                    logger.info(f"✅ {test_name}: PASSED")
                else:
                    logger.error(f"❌ {test_name}: FAILED - {result.get('error', 'Unknown error')}")
                    self.errors.append(f"{test_name}: {result.get('error', 'Unknown error')}")
            except Exception as e:
                error_msg = f"Exception in {test_name}: {str(e)}"
                logger.error(f"💥 {error_msg}")
                self.errors.append(error_msg)
                self.results[test_name] = {'success': False, 'error': str(e)}
        
        # Generate summary report
        await self.generate_report()
        
        # Provide Docker rebuild instructions
        await self.provide_docker_instructions()
    
    async def verify_import_fixes(self) -> Dict[str, Any]:
        """Verify all import fixes are applied correctly."""
        try:
            # Test critical imports that were fixed
            import_tests = [
                ('bot.handlers.referral', 'Referral handler import'),
                ('bot.services.advertising_service', 'Advertising service import'),
                ('bot.utils.helpers', 'Helpers module import'),
                ('bot.utils.buttons', 'Buttons module import'),
            ]
            
            successful_imports = []
            failed_imports = []
            
            for module_name, description in import_tests:
                try:
                    module = __import__(module_name, fromlist=[''])
                    
                    # Verify specific fixes
                    if module_name == 'bot.handlers.referral':
                        # Check if get_text is properly imported
                        if hasattr(module, 'get_text') or 'get_text' in str(module.__dict__):
                            successful_imports.append(f"{description} - get_text import fixed")
                        else:
                            failed_imports.append(f"{description} - get_text import not found")
                    
                    if module_name == 'bot.utils.buttons':
                        # Check if ReplyKeyboardBuilder exists
                        if hasattr(module, 'ReplyKeyboardBuilder'):
                            successful_imports.append(f"{description} - ReplyKeyboardBuilder available")
                        else:
                            failed_imports.append(f"{description} - ReplyKeyboardBuilder not found")
                    
                    successful_imports.append(f"{description} - module loads successfully")
                    
                except Exception as e:
                    failed_imports.append(f"{description}: {str(e)}")
            
            return {
                'success': len(failed_imports) == 0,
                'details': {
                    'successful_imports': successful_imports,
                    'failed_imports': failed_imports
                },
                'error': f"Failed imports: {failed_imports}" if failed_imports else None
            }
        except Exception as e:
            return {'success': False, 'error': f"Import verification error: {str(e)}"}
    
    async def verify_syntax_fixes(self) -> Dict[str, Any]:
        """Verify all syntax errors are fixed."""
        try:
            # Test files that had syntax errors
            syntax_tests = [
                ('bot/services/payment_service.py', 'Payment service syntax'),
                ('bot/handlers/referral.py', 'Referral handler syntax'),
                ('bot/services/advertising_service.py', 'Advertising service syntax'),
                ('bot/main.py', 'Main bot file syntax'),
            ]
            
            successful_syntax = []
            failed_syntax = []
            
            for file_path, description in syntax_tests:
                try:
                    # Try to compile the file
                    result = subprocess.run(
                        [sys.executable, '-m', 'py_compile', file_path],
                        capture_output=True,
                        text=True,
                        timeout=10
                    )
                    
                    if result.returncode == 0:
                        successful_syntax.append(f"{description} - compiles successfully")
                    else:
                        failed_syntax.append(f"{description} - compilation error: {result.stderr}")
                        
                except subprocess.TimeoutExpired:
                    failed_syntax.append(f"{description} - compilation timeout")
                except Exception as e:
                    failed_syntax.append(f"{description} - error: {str(e)}")
            
            return {
                'success': len(failed_syntax) == 0,
                'details': {
                    'successful_syntax': successful_syntax,
                    'failed_syntax': failed_syntax
                },
                'error': f"Syntax errors: {failed_syntax}" if failed_syntax else None
            }
        except Exception as e:
            return {'success': False, 'error': f"Syntax verification error: {str(e)}"}
    
    async def verify_localization(self) -> Dict[str, Any]:
        """Verify localization completeness."""
        try:
            from bot.utils.helpers import get_text
            
            # Test new localization keys that were added
            new_keys = [
                'vpn.use_account_details',
                'vpn.use_keyboard_navigation',
                'payment.price',
                'payment.duration',
                'payment.days',
                'dashboard.user_id',
                'dashboard.active_accounts',
                'dashboard.total_spent',
                'dashboard.member_since',
                'help.commands_title',
                'payment_successful_message',
                'qr_code_caption',
                'unknown'
            ]
            
            languages = ['en', 'fa', 'ru', 'zh']
            successful_keys = []
            missing_keys = []
            
            for lang in languages:
                for key in new_keys:
                    try:
                        text = get_text(key, lang)
                        if text and not text.startswith('Translation error'):
                            successful_keys.append(f"{lang}.{key}")
                        else:
                            missing_keys.append(f"{lang}.{key}")
                    except Exception as e:
                        missing_keys.append(f"{lang}.{key}: {str(e)}")
            
            return {
                'success': len(missing_keys) == 0,
                'details': {
                    'successful_keys': len(successful_keys),
                    'missing_keys': missing_keys,
                    'languages_tested': languages,
                    'keys_tested': new_keys
                },
                'error': f"Missing localization keys: {missing_keys}" if missing_keys else None
            }
        except Exception as e:
            return {'success': False, 'error': f"Localization verification error: {str(e)}"}
    
    async def verify_payment_service(self) -> Dict[str, Any]:
        """Verify payment service fixes."""
        try:
            from bot.services.payment_service import payment_service
            
            # Check if payment service has required methods
            required_methods = [
                'create_invoice',
                'process_successful_payment',
                'validate_payment_data',
                'parse_invoice_payload'
            ]
            
            available_methods = []
            missing_methods = []
            
            for method in required_methods:
                if hasattr(payment_service, method):
                    available_methods.append(method)
                else:
                    missing_methods.append(method)
            
            # Test if the syntax error is fixed by checking the file content
            syntax_fixed = True
            try:
                with open('bot/services/payment_service.py', 'r') as f:
                    content = f.read()
                    # Check if the problematic elif structure is fixed
                    if 'elif payment_method == \'crypto\':' in content:
                        # Check if it's properly indented (not inside an else block)
                        lines = content.split('\n')
                        for i, line in enumerate(lines):
                            if 'elif payment_method == \'crypto\':' in line:
                                # Check indentation level
                                indent_level = len(line) - len(line.lstrip())
                                if indent_level <= 12:  # Proper elif indentation
                                    syntax_fixed = True
                                    break
                                else:
                                    syntax_fixed = False
            except Exception:
                syntax_fixed = False
            
            return {
                'success': len(missing_methods) == 0 and syntax_fixed,
                'details': {
                    'available_methods': available_methods,
                    'missing_methods': missing_methods,
                    'syntax_fixed': syntax_fixed
                },
                'error': f"Issues: missing methods {missing_methods}, syntax fixed: {syntax_fixed}" if missing_methods or not syntax_fixed else None
            }
        except Exception as e:
            return {'success': False, 'error': f"Payment service verification error: {str(e)}"}
    
    async def verify_referral_handler(self) -> Dict[str, Any]:
        """Verify referral handler fixes."""
        try:
            from bot.handlers.referral import referral_handler
            
            # Check if referral handler has required methods
            required_methods = [
                'handle_referral_command',
                'handle_referral_link_command',
                'process_referral_start'
            ]
            
            available_methods = []
            missing_methods = []
            
            for method in required_methods:
                if hasattr(referral_handler, method):
                    available_methods.append(method)
                else:
                    missing_methods.append(method)
            
            # Verify import fixes
            import_fixed = True
            try:
                with open('bot/handlers/referral.py', 'r') as f:
                    content = f.read()
                    # Check if imports are correct
                    if 'from bot.utils.helpers import get_text' in content and \
                       'from bot.utils.buttons import ReplyKeyboardBuilder' in content:
                        import_fixed = True
                    else:
                        import_fixed = False
            except Exception:
                import_fixed = False
            
            return {
                'success': len(missing_methods) == 0 and import_fixed,
                'details': {
                    'available_methods': available_methods,
                    'missing_methods': missing_methods,
                    'import_fixed': import_fixed
                },
                'error': f"Issues: missing methods {missing_methods}, imports fixed: {import_fixed}" if missing_methods or not import_fixed else None
            }
        except Exception as e:
            return {'success': False, 'error': f"Referral handler verification error: {str(e)}"}
    
    async def verify_database_schema(self) -> Dict[str, Any]:
        """Verify database schema is PostgreSQL compatible."""
        try:
            schema_issues = []
            schema_features = []
            
            with open('database_schema.sql', 'r') as f:
                content = f.read()
                
                # Check for PostgreSQL features
                if 'CREATE TYPE' in content:
                    schema_features.append('PostgreSQL ENUM types defined')
                else:
                    schema_issues.append('Missing PostgreSQL ENUM types')
                
                if 'SERIAL' in content or 'BIGSERIAL' in content:
                    schema_features.append('PostgreSQL SERIAL types used')
                else:
                    schema_issues.append('Missing PostgreSQL SERIAL types')
                
                if 'JSONB' in content:
                    schema_features.append('PostgreSQL JSONB type used')
                else:
                    schema_issues.append('Missing PostgreSQL JSONB type')
                
                if 'CREATE TRIGGER' in content:
                    schema_features.append('PostgreSQL triggers defined')
                else:
                    schema_issues.append('Missing PostgreSQL triggers')
                
                # Check for MySQL incompatible syntax
                if 'AUTO_INCREMENT' in content:
                    schema_issues.append('MySQL AUTO_INCREMENT found (should be SERIAL)')
                
                if 'JSON' in content and 'JSONB' not in content:
                    schema_issues.append('MySQL JSON found (should be JSONB)')
            
            return {
                'success': len(schema_issues) == 0,
                'details': {
                    'schema_features': schema_features,
                    'schema_issues': schema_issues
                },
                'error': f"Schema issues: {schema_issues}" if schema_issues else None
            }
        except Exception as e:
            return {'success': False, 'error': f"Database schema verification error: {str(e)}"}
    
    async def verify_configuration(self) -> Dict[str, Any]:
        """Verify configuration completeness."""
        try:
            from bot.config import settings
            
            # Check critical configuration
            config_items = []
            config_issues = []
            
            if hasattr(settings, 'BOT_TOKEN'):
                config_items.append('BOT_TOKEN configured')
            else:
                config_issues.append('BOT_TOKEN missing')
            
            if hasattr(settings, 'POSTGRES_HOST'):
                config_items.append('PostgreSQL configuration present')
            else:
                config_issues.append('PostgreSQL configuration missing')
            
            if hasattr(settings, 'REDIS_HOST'):
                config_items.append('Redis configuration present')
            else:
                config_issues.append('Redis configuration missing')
            
            return {
                'success': len(config_issues) == 0,
                'details': {
                    'config_items': config_items,
                    'config_issues': config_issues
                },
                'error': f"Configuration issues: {config_issues}" if config_issues else None
            }
        except Exception as e:
            return {'success': False, 'error': f"Configuration verification error: {str(e)}"}
    
    async def verify_docker_readiness(self) -> Dict[str, Any]:
        """Verify Docker deployment readiness."""
        try:
            docker_items = []
            docker_issues = []
            
            # Check for Docker files
            if os.path.exists('Dockerfile'):
                docker_items.append('Dockerfile exists')
            else:
                docker_issues.append('Dockerfile missing')
            
            if os.path.exists('docker-compose.yml') or os.path.exists('docker-compose.prod.yml'):
                docker_items.append('Docker Compose configuration exists')
            else:
                docker_issues.append('Docker Compose configuration missing')
            
            if os.path.exists('requirements.txt'):
                docker_items.append('Requirements file exists')
            else:
                docker_issues.append('Requirements file missing')
            
            if os.path.exists('.env.example'):
                docker_items.append('Environment template exists')
            else:
                docker_issues.append('Environment template missing')
            
            return {
                'success': len(docker_issues) == 0,
                'details': {
                    'docker_items': docker_items,
                    'docker_issues': docker_issues
                },
                'error': f"Docker issues: {docker_issues}" if docker_issues else None
            }
        except Exception as e:
            return {'success': False, 'error': f"Docker readiness verification error: {str(e)}"}
    
    async def generate_report(self):
        """Generate a comprehensive verification report."""
        logger.info("\n" + "="*80)
        logger.info("🔍 COMPREHENSIVE FIX VERIFICATION REPORT")
        logger.info("="*80)
        
        total_verifications = len(self.results)
        passed_verifications = sum(1 for r in self.results.values() if r.get('success', False))
        failed_verifications = total_verifications - passed_verifications
        
        logger.info(f"📊 SUMMARY: {passed_verifications}/{total_verifications} verifications passed")
        
        if failed_verifications > 0:
            logger.error(f"❌ FAILED VERIFICATIONS ({failed_verifications}):")
            for error in self.errors:
                logger.error(f"   • {error}")
        
        logger.info("\n📋 DETAILED RESULTS:")
        for test_name, result in self.results.items():
            status = "✅ PASS" if result.get('success', False) else "❌ FAIL"
            logger.info(f"   {status} {test_name}")
            if result.get('details'):
                for key, value in result['details'].items():
                    if isinstance(value, list) and len(value) > 3:
                        logger.info(f"      {key}: {len(value)} items")
                    else:
                        logger.info(f"      {key}: {value}")
        
        logger.info("\n" + "="*80)
        
        if failed_verifications == 0:
            logger.info("🎉 ALL VERIFICATIONS PASSED! Ready for Docker rebuild and deployment.")
        else:
            logger.error(f"⚠️  {failed_verifications} ISSUES FOUND. Please fix before deployment.")
        
        logger.info("="*80)
    
    async def provide_docker_instructions(self):
        """Provide Docker rebuild and deployment instructions."""
        logger.info("\n" + "🐳 DOCKER REBUILD AND DEPLOYMENT INSTRUCTIONS")
        logger.info("="*80)
        
        if len(self.errors) == 0:
            logger.info("✅ All fixes verified! Follow these steps to rebuild and deploy:")
            logger.info("")
            logger.info("1. STOP CURRENT CONTAINERS:")
            logger.info("   docker-compose down")
            logger.info("")
            logger.info("2. REBUILD WITH LATEST CHANGES:")
            logger.info("   docker-compose build --no-cache")
            logger.info("")
            logger.info("3. START WITH NEW BUILD:")
            logger.info("   docker-compose up -d")
            logger.info("")
            logger.info("4. CHECK LOGS:")
            logger.info("   docker-compose logs -f bot")
            logger.info("")
            logger.info("5. VERIFY BOT IS RUNNING:")
            logger.info("   docker-compose ps")
            logger.info("")
            logger.info("6. TEST BOT FUNCTIONALITY:")
            logger.info("   Send /start to your bot on Telegram")
            logger.info("")
            logger.info("🎯 PRODUCTION DEPLOYMENT:")
            logger.info("   docker-compose -f docker-compose.prod.yml up -d --build")
            logger.info("")
        else:
            logger.error("❌ Fix verification failed! Please resolve issues before rebuilding Docker.")
        
        logger.info("="*80)


async def main():
    """Main function to run the fix verification."""
    verifier = FixVerifier()
    await verifier.run_all_verifications()


if __name__ == "__main__":
    asyncio.run(main())
