"""User dashboard service for account management and VPN information."""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from bot.database import get_db_connection
from bot.services.vpn_service import vpn_service
from bot.services.payment_service import payment_service
from bot.services.channel_service import channel_service
from bot.services.qr_service import qr_service

logger = logging.getLogger(__name__)


class DashboardService:
    """Service for user dashboard and account management."""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def get_user_dashboard_data(self, user_id: int) -> Dict[str, Any]:
        """Get comprehensive dashboard data for user."""
        try:
            # Get user basic info
            user_info = await self._get_user_info(user_id)
            
            # Get VPN accounts
            vpn_accounts = await vpn_service.get_user_vpn_accounts(user_id)
            
            # Get subscription status
            subscription_status = await channel_service.get_user_subscription_status(user_id)
            
            # Get premium subscriptions
            premium_subscriptions = await payment_service.get_user_subscriptions(
                user_id, active_only=True
            )
            
            # Get payment history
            payment_history = await payment_service.get_user_payments(user_id)
            
            # Get usage statistics
            usage_stats = await self._get_usage_statistics(user_id)
            
            # Calculate dashboard summary data
            total_accounts = len(vpn_accounts)
            active_accounts = len([acc for acc in vpn_accounts if acc.get('is_active')])
            total_spent = sum([payment.get('amount', 0) for payment in payment_history if payment.get('status') == 'completed'])
            
            # Handle member_since date formatting
            member_since = 'Unknown'
            if user_info and user_info.get('created_at'):
                created_at = user_info.get('created_at')
                if isinstance(created_at, datetime):
                    member_since = created_at.strftime('%Y-%m-%d')
                elif isinstance(created_at, str):
                    member_since = created_at.split('T')[0] if 'T' in created_at else created_at[:10]
                else:
                    member_since = str(created_at)[:10]
            
            return {
                # Legacy format for handlers
                'total_accounts': total_accounts,
                'active_accounts': active_accounts,
                'total_spent': total_spent,
                'member_since': member_since,
                
                # Detailed data
                'user_info': user_info,
                'vpn_accounts': vpn_accounts,
                'subscription_status': subscription_status,
                'premium_subscriptions': premium_subscriptions,
                'payment_history': payment_history[:5],  # Last 5 payments
                'usage_stats': usage_stats,
                'dashboard_generated_at': datetime.now().isoformat()
            }
        
        except Exception as e:
            self.logger.error(f"Error getting dashboard data for user {user_id}: {e}")
            return {
                'error': str(e),
                'total_accounts': 0,
                'active_accounts': 0,
                'total_spent': 0.0,
                'member_since': 'Unknown',
                'user_info': None,
                'vpn_accounts': [],
                'subscription_status': None,
                'premium_subscriptions': [],
                'payment_history': [],
                'usage_stats': {},
                'dashboard_generated_at': datetime.now().isoformat()
            }
    
    async def _get_user_info(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get basic user information."""
        try:
            async with get_db_connection() as conn:
                user = await conn.fetchrow(
                    """
                    SELECT id, telegram_id, username, first_name, last_name, 
                           language_code, is_active, is_admin, created_at, last_seen
                    FROM users WHERE telegram_id = $1
                    """,
                    user_id
                )
                return dict(user) if user else None
        
        except Exception as e:
            self.logger.error(f"Error getting user info for {user_id}: {e}")
            return None
    
    async def _get_usage_statistics(self, user_id: int) -> Dict[str, Any]:
        """Get user's usage statistics."""
        try:
            async with get_db_connection() as conn:
                # Get total data usage
                total_usage = await conn.fetchval(
                    """
                    SELECT COALESCE(SUM(data_used), 0) 
                    FROM vpn_usage_logs 
                    WHERE user_id = $1
                    """,
                    user_id
                )
                
                # Get usage in last 30 days
                monthly_usage = await conn.fetchval(
                    """
                    SELECT COALESCE(SUM(data_used), 0) 
                    FROM vpn_usage_logs 
                    WHERE user_id = $1 AND created_at > NOW() - INTERVAL '30 days'
                    """,
                    user_id
                )
                
                # Get connection count
                total_connections = await conn.fetchval(
                    """
                    SELECT COUNT(*) 
                    FROM vpn_usage_logs 
                    WHERE user_id = $1
                    """,
                    user_id
                )
                
                # Get active VPN accounts count
                active_vpn_count = await conn.fetchval(
                    """
                    SELECT COUNT(*) 
                    FROM vpn_accounts 
                    WHERE user_id = $1 AND is_active = true
                    """,
                    user_id
                )
                
                # Get trial usage
                trial_used = await conn.fetchval(
                    """
                    SELECT CASE WHEN has_used_trial THEN 1 ELSE 0 END
                    FROM users 
                    WHERE telegram_id = $1
                    """,
                    user_id
                )
                
                return {
                    'total_data_used_gb': round((total_usage or 0) / (1024**3), 2),
                    'monthly_data_used_gb': round((monthly_usage or 0) / (1024**3), 2),
                    'total_connections': total_connections or 0,
                    'active_vpn_accounts': active_vpn_count or 0,
                    'trial_accounts_used': trial_used or 0
                }
        
        except Exception as e:
            self.logger.error(f"Error getting usage stats for user {user_id}: {e}")
            return {
                'total_data_used_gb': 0,
                'monthly_data_used_gb': 0,
                'total_connections': 0,
                'active_vpn_accounts': 0,
                'trial_accounts_used': 0
            }
    
    def format_dashboard_message(
        self, 
        dashboard_data: Dict[str, Any], 
        language: str = 'en'
    ) -> str:
        """Format dashboard data into user-friendly message."""
        try:
            if dashboard_data.get('error'):
                if language == 'fa':
                    return fget_text('error.dashboard_loading_error', language, error=dashboard_data['error'])
                else:
                    return fget_text('error.dashboard_loading_error', language, error=dashboard_data['error'])
            
            user_info = dashboard_data.get('user_info', {})
            vpn_accounts = dashboard_data.get('vpn_accounts', [])
            usage_stats = dashboard_data.get('usage_stats', {})
            premium_subs = dashboard_data.get('premium_subscriptions', [])
            
            if language == 'fa':
                message = get_text('dashboard.title', language) + '\n\n'
                
                # User info
                if user_info:
                    message += f"{get_text('dashboard.user_info_title', language)}\n"
                    message += f"{get_text('dashboard.user_name', language, first_name=user_info.get('first_name', get_text('common.unknown', language)), last_name=user_info.get('last_name', ''))}\n"
                    if user_info.get('username'):
                        message += f"{get_text('dashboard.user_username', language, username=user_info['username'])}\n"
                    message += f"{get_text('dashboard.member_since', language, date=user_info.get('created_at', get_text('common.unknown', language))[:10])}\n\n"
                
                # VPN accounts
                message += f"{get_text('dashboard.vpn_accounts_title', language, count=len(vpn_accounts))}\n"
                active_accounts = [acc for acc in vpn_accounts if acc.get('is_active')]
                message += f"{get_text('dashboard.active_accounts', language, count=len(active_accounts))}\n"
                
                if active_accounts:
                    message += '\n' + get_text('dashboard.active_accounts_list_title', language) + '\n'
                    for i, acc in enumerate(active_accounts[:3], 1):  # Show max 3
                        status = "🟢" if acc.get('is_active') else "🔴"
                        acc_type = get_text('common.premium', language) if acc.get('account_type') == 'premium' else get_text('common.trial', language)
                        message += f"{i}. {status} {acc.get('username', get_text('common.unknown', language))} ({acc_type})\n"
                        if acc.get('expires_at'):
                            message += f"   📅 {get_text('common.expires', language)}: {acc['expires_at'][:10]}\n"
                
                # Usage stats
                message += f'\n{get_text('dashboard.usage_stats_title', language)}\n'
                message += f"{get_text('dashboard.total_data_used', language, usage=usage_stats.get('total_data_used_gb', 0))}\n"
                message += f"{get_text('dashboard.monthly_usage', language, usage=usage_stats.get('monthly_data_used_gb', 0))}\n"
                message += f"{get_text('dashboard.total_connections', language, count=usage_stats.get('total_connections', 0))}\n"
                
                # Premium subscriptions
                if premium_subs:
                    message += f'\n' + get_text('dashboard.premium_subs_title', language, count=len(premium_subs)) + '\n'
                    for sub in premium_subs[:2]:  # Show max 2
                        message += f"• {sub.get('plan_name', get_text('common.unknown', language))} - {get_text('common.expires', language)}: {sub.get('expires_at', get_text('common.unknown', language))[:10]}\n"
            
            else:
                message = get_text('dashboard.title', language) + '\n\n'
                
                # User info
                if user_info:
                    message += fget_text('dashboard.user_info_title', language) + '\n'
                    message += fget_text('dashboard.user_name', language, first_name=user_info.get('first_name', get_text('common.unknown', language)), last_name=user_info.get('last_name', '')) + '\n'
                    if user_info.get('username'):
                        message += fget_text('dashboard.user_username', language, username=user_info['username']) + '\n'
                    message += fget_text('dashboard.member_since', language, date=user_info.get('created_at', get_text('common.unknown', language))[:10]) + '\n\n'
                
                # VPN accounts
                message += fget_text('dashboard.vpn_accounts_title', language, count=len(vpn_accounts)) + '\n'
                active_accounts = [acc for acc in vpn_accounts if acc.get('is_active')]
                message += fget_text('dashboard.active_accounts', language, count=len(active_accounts)) + '\n'
                
                if active_accounts:
                    message += '\n' + get_text('dashboard.active_accounts_list_title', language) + '\n'
                    for i, acc in enumerate(active_accounts[:3], 1):  # Show max 3
                        status = "🟢" if acc.get('is_active') else "🔴"
                        acc_type = get_text('common.premium', language) if acc.get('account_type') == 'premium' else get_text('common.trial', language)
                        message += f"{i}. {status} {acc.get('username', get_text('common.unknown', language))} ({acc_type})\n"
                        if acc.get('expires_at'):
                            message += f"   📅 {get_text('common.expires', language)}: {acc['expires_at'][:10]}\n"
                
                # Usage stats
                message += f'\n' + get_text('dashboard.usage_stats_title', language) + '\n'
                message += fget_text('dashboard.total_data_used', language, usage=usage_stats.get('total_data_used_gb', 0)) + '\n'
                message += fget_text('dashboard.monthly_usage', language, usage=usage_stats.get('monthly_data_used_gb', 0)) + '\n'
                message += fget_text('dashboard.total_connections', language, count=usage_stats.get('total_connections', 0)) + '\n'
                
                # Premium subscriptions
                if premium_subs:
                    message += f'\n{get_text('dashboard.premium_subs_title', language, count=len(premium_subs))}\n'
                    for sub in premium_subs[:2]:  # Show max 2
                        message += f"• {sub.get('plan_name', get_text('common.unknown', language))} - {get_text('common.expires', language)}: {sub.get('expires_at', get_text('common.unknown', language))[:10]}\n"
            
            return message
        
        except Exception as e:
            self.logger.error(f"Error formatting dashboard message: {e}")
            if language == 'fa':
                return get_text('error.dashboard_display_error', language)
            else:
                return get_text('error.dashboard_display_error', language)
    
    async def get_vpn_account_details(
        self, 
        user_id: int, 
        account_username: str
    ) -> Optional[Dict[str, Any]]:
        """Get detailed information about a specific VPN account."""
        try:
            # Get account from database
            async with get_db_connection() as conn:
                account = await conn.fetchrow(
                    """
                    SELECT * FROM vpn_accounts 
                    WHERE user_id = $1 AND username = $2
                    """,
                    user_id, account_username
                )
                
                if not account:
                    return None
                
                account_dict = dict(account)
                
                # Get usage data from Marzban
                marzban_data = await vpn_service.get_account_usage(account_username)
                if marzban_data:
                    account_dict.update(marzban_data)
                
                # Generate QR code for subscription URL
                if account_dict.get('subscription_url'):
                    qr_code = await qr_service.generate_qr_code(
                        account_dict['subscription_url'],
                        return_base64=True
                    )
                    account_dict['qr_code_base64'] = qr_code
                
                return account_dict
        
        except Exception as e:
            self.logger.error(f"Error getting VPN account details: {e}")
            return None
    
    def format_vpn_account_message(
        self, 
        account_data: Dict[str, Any], 
        language: str = 'en'
    ) -> str:
        """Format VPN account details into user message."""
        try:
            if language == 'fa':
                message = f"{get_text('vpn_account.details_title', language)}\n\n"
                message += f"**{get_text('common.username', language)}:** `{account_data.get('username', get_text('common.unknown', language))}`\n"
                message += f"**{get_text('common.account_type', language)}:** {account_data.get('account_type', get_text('common.unknown', language))}\n"
                message += f"**{get_text('common.status', language)}:** {'🟢 ' + get_text('common.active', language) if account_data.get('is_active') else '🔴 ' + get_text('common.inactive', language)}\n"
                
                if account_data.get('expires_at'):
                    message += f"**{get_text('common.expires', language)}:** {account_data['expires_at'][:10]}\n"
                
                if account_data.get('data_limit'):
                    message += f"**{get_text('common.data_limit', language)}:** {account_data['data_limit']} GB\n"
                
                if account_data.get('used_traffic'):
                    used_gb = account_data['used_traffic'] / (1024**3)
                    message += f"**{get_text('common.data_used', language)}:** {used_gb:.2f} GB\n"
                
                if account_data.get('subscription_url'):
                    message += f"\n**{get_text('common.subscription_url', language)}:**\n`{account_data['subscription_url']}`\n"
                    message += f"\n💡 {get_text('common.subscription_url_tip', language)}"
            
            else:
                message = f"{get_text('vpn_account.details_title', language)}\n\n"
                message += f"**{get_text('common.username', language)}:** `{account_data.get('username', get_text('common.unknown', language))}`\n"
                message += f"**{get_text('common.account_type', language)}:** {account_data.get('account_type', get_text('common.unknown', language))}\n"
                message += f"**{get_text('common.status', language)}:** {'🟢 ' + get_text('common.active', language) if account_data.get('is_active') else '🔴 ' + get_text('common.inactive', language)}\n"
                
                if account_data.get('expires_at'):
                    message += f"**{get_text('common.expires', language)}:** {account_data['expires_at'][:10]}\n"
                
                if account_data.get('data_limit'):
                    message += f"**{get_text('common.data_limit', language)}:** {account_data['data_limit']} GB\n"
                
                if account_data.get('used_traffic'):
                    used_gb = account_data['used_traffic'] / (1024**3)
                    message += f"**{get_text('common.data_used', language)}:** {used_gb:.2f} GB\n"
                
                if account_data.get('subscription_url'):
                    message += f"\n**{get_text('common.subscription_url', language)}:**\n`{account_data['subscription_url']}`\n"
                    message += f"\n💡 {get_text('common.subscription_url_tip', language)}"
            
            return message
        
        except Exception as e:
            self.logger.error(f"Error formatting VPN account message: {e}")
            if language == 'fa':
                return get_text('error.account_details_display_error', language)
            else:
                return get_text('error.account_details_display_error', language)
    
    async def generate_account_qr_code(self, user_id: int, account_username: str) -> Optional[str]:
        """Generate QR code for VPN account subscription URL."""
        try:
            account_details = await self.get_vpn_account_details(user_id, account_username)
            
            if not account_details or not account_details.get('subscription_url'):
                return None
            
            return await qr_service.generate_qr_code(
                account_details['subscription_url'],
                return_base64=True
            )
        
        except Exception as e:
            self.logger.error(f"Error generating QR code for account {account_username}: {e}")
            return None


# Global instance
dashboard_service = DashboardService()