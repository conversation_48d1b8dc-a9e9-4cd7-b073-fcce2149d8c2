@echo off
echo Starting database schema fixes and Docker restart...

:: Stop all containers
echo Stopping Docker containers...
docker-compose down

:: Wait a moment
timeout /t 3 /nobreak >nul

:: Start database first
echo Starting database...
docker-compose up -d postgres

:: Wait for database to be ready
echo Waiting for database to be ready...
timeout /t 15 /nobreak >nul

:: Copy SQL file to container
echo Copying SQL file to container...
docker cp fix_database_schema.sql vpn-postgres:/tmp/fix_database_schema.sql

:: Apply database schema fixes
echo Applying database schema fixes...
docker-compose exec -T postgres psql -U postgres -d telegram_vpn_bot -f /tmp/fix_database_schema.sql

if %ERRORLEVEL% NEQ 0 (
    echo Error applying database fixes!
    echo Trying alternative method...
    docker-compose exec postgres psql -U postgres -d telegram_vpn_bot -c "\i /tmp/fix_database_schema.sql"
)

echo Database fixes applied successfully!

:: Start all services
echo Starting all services...
docker-compose up -d

:: Wait for services to start
timeout /t 5 /nobreak >nul

:: Show logs
echo Showing recent logs...
docker-compose logs --tail=50

echo.
echo All services restarted with database fixes!
echo Check the logs above for any errors.
pause