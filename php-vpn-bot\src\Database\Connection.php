<?php

declare(strict_types=1);

namespace VpnBot\Database;

use PDO;
use PDOException;
use VpnBot\Config\Config;
use Psr\Log\LoggerInterface;

class Connection
{
    private static ?PDO $instance = null;
    private static ?LoggerInterface $logger = null;

    public static function setLogger(LoggerInterface $logger): void
    {
        self::$logger = $logger;
    }

    public static function getInstance(): PDO
    {
        if (self::$instance === null) {
            self::$instance = self::createConnection();
        }
        return self::$instance;
    }

    private static function createConnection(): PDO
    {
        $config = Config::getInstance();
        
        $host = $config->get('database.host');
        $port = $config->get('database.port');
        $database = $config->get('database.database');
        $username = $config->get('database.username');
        $password = $config->get('database.password');

        $dsn = "pgsql:host={$host};port={$port};dbname={$database}";

        try {
            $pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::ATTR_PERSISTENT => true,
            ]);

            if (self::$logger) {
                self::$logger->info('Database connection established successfully');
            }

            return $pdo;
        } catch (PDOException $e) {
            if (self::$logger) {
                self::$logger->error('Database connection failed: ' . $e->getMessage());
            }
            throw new \RuntimeException('Database connection failed: ' . $e->getMessage());
        }
    }

    public static function beginTransaction(): bool
    {
        return self::getInstance()->beginTransaction();
    }

    public static function commit(): bool
    {
        return self::getInstance()->commit();
    }

    public static function rollback(): bool
    {
        return self::getInstance()->rollBack();
    }

    public static function prepare(string $statement): \PDOStatement
    {
        return self::getInstance()->prepare($statement);
    }

    public static function query(string $statement): \PDOStatement
    {
        return self::getInstance()->query($statement);
    }

    public static function exec(string $statement): int
    {
        return self::getInstance()->exec($statement);
    }

    public static function lastInsertId(?string $name = null): string
    {
        return self::getInstance()->lastInsertId($name);
    }
}
