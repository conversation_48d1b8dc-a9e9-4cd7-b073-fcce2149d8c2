"""Channel advertising service for the Telegram bot."""

import logging
import random
from typing import List, Dict, Any, Optional
from telegram import Bo<PERSON>, Update
from telegram.ext import ContextTypes
from bot.database import get_db_connection
from bot.services.channel_service import ChannelService
from bot.utils.helpers import get_text


class AdvertisingService:
    """Service for managing channel advertising and promotion."""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.channel_service = ChannelService()
    
    async def get_random_advertisement(self, language: str = 'en') -> Optional[Dict[str, Any]]:
        """Get a random channel advertisement."""
        try:
            advertising_channels = await self.channel_service.get_advertising_channels()
            
            if not advertising_channels:
                return None
            
            # Select a random channel for advertising
            channel = random.choice(advertising_channels)
            
            # Create advertisement message
            ad_message = await self._create_advertisement_message(channel, language)
            
            return {
                'channel': channel,
                'message': ad_message,
                'type': 'channel_promotion'
            }
        
        except Exception as e:
            self.logger.error(f"Error getting random advertisement: {e}")
            return None
    
    async def _create_advertisement_message(self, channel: Dict[str, Any], language: str) -> str:
        """Create an advertisement message for a channel."""
        try:
            # Use custom advertising message if available
            if channel.get('advertising_message'):
                message = channel['advertising_message']
            else:
                # Create default advertising message
                message = f"🌟 **{get_text('advertising.join_channel', language)}**\n\n"
                message += f"📢 **{channel['channel_name']}**\n"
                
                if channel.get('description'):
                    message += f"📝 {channel['description']}\n\n"
                
                message += f"👥 {get_text('advertising.subscribers', language)}: {channel.get('subscriber_count', 'N/A')}\n\n"
                message += f"🔗 {get_text('advertising.join_now', language)}: "
                
                if channel.get('invite_link'):
                    message += channel['invite_link']
                elif channel.get('channel_url'):
                    message += channel['channel_url']
                else:
                    message += f"@{channel['channel_id'].replace('@', '')}"
            
            return message
        
        except Exception as e:
            self.logger.error(f"Error creating advertisement message: {e}")
            return f"📢 Join our channel: {channel.get('channel_name', 'Unknown')}"
    
    async def send_advertisement(
        self, 
        update: Update, 
        context: ContextTypes.DEFAULT_TYPE, 
        language: str = 'en'
    ) -> bool:
        """Send a random channel advertisement to the user."""
        try:
            advertisement = await self.get_random_advertisement(language)
            
            if not advertisement:
                return False
            
            await update.message.reply_text(
                advertisement['message'],
                parse_mode='Markdown',
                disable_web_page_preview=False
            )
            
            # Log advertisement sent
            await self._log_advertisement_sent(
                update.effective_user.id,
                advertisement['channel']['id']
            )
            
            return True
        
        except Exception as e:
            self.logger.error(f"Error sending advertisement: {e}")
            return False
    
    async def _log_advertisement_sent(self, user_id: int, channel_id: int) -> None:
        """Log that an advertisement was sent to a user."""
        try:
            async with get_db_connection() as conn:
                await conn.execute(
                    """
                    INSERT INTO advertisement_logs (user_id, channel_id, sent_at)
                    VALUES ($1, $2, NOW())
                    """,
                    user_id, channel_id
                )
        
        except Exception as e:
            self.logger.error(f"Error logging advertisement: {e}")
    
    async def should_show_advertisement(self, user_id: int) -> bool:
        """Determine if an advertisement should be shown to the user."""
        try:
            # Check if user has seen an advertisement recently
            async with get_db_connection() as conn:
                recent_ads = await conn.fetchval(
                    """
                    SELECT COUNT(*) FROM advertisement_logs 
                    WHERE user_id = $1 AND sent_at > NOW() - INTERVAL '1 hour'
                    """,
                    user_id
                )
                
                # Show advertisement if user hasn't seen one in the last hour
                return recent_ads == 0
        
        except Exception as e:
            self.logger.error(f"Error checking advertisement eligibility: {e}")
            return False
    
    async def get_channel_promotion_stats(self, channel_id: int) -> Dict[str, Any]:
        """Get promotion statistics for a specific channel."""
        try:
            async with get_db_connection() as conn:
                # Get total advertisements sent
                total_ads = await conn.fetchval(
                    "SELECT COUNT(*) FROM advertisement_logs WHERE channel_id = $1",
                    channel_id
                )
                
                # Get advertisements sent today
                today_ads = await conn.fetchval(
                    """
                    SELECT COUNT(*) FROM advertisement_logs 
                    WHERE channel_id = $1 AND sent_at > CURRENT_DATE
                    """,
                    channel_id
                )
                
                # Get unique users reached
                unique_users = await conn.fetchval(
                    "SELECT COUNT(DISTINCT user_id) FROM advertisement_logs WHERE channel_id = $1",
                    channel_id
                )
                
                return {
                    'total_advertisements': total_ads or 0,
                    'today_advertisements': today_ads or 0,
                    'unique_users_reached': unique_users or 0
                }
        
        except Exception as e:
            self.logger.error(f"Error getting channel promotion stats: {e}")
            return {
                'total_advertisements': 0,
                'today_advertisements': 0,
                'unique_users_reached': 0
            }
    
    async def update_channel_subscriber_count(self, channel_id: str, subscriber_count: int) -> bool:
        """Update the subscriber count for a channel."""
        try:
            async with get_db_connection() as conn:
                await conn.execute(
                    "UPDATE channels SET subscriber_count = $1, updated_at = NOW() WHERE channel_id = $2",
                    subscriber_count, channel_id
                )
                return True
        
        except Exception as e:
            self.logger.error(f"Error updating channel subscriber count: {e}")
            return False


# Global instance
advertising_service = AdvertisingService()
