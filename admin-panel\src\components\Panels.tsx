import React, { useState } from 'react';
import { usePanels, useCreatePanel, useUpdatePanel, useDeletePanel } from '../services/api';
import { VPNPanel, CreatePanelRequest } from '../types/api';

const Panels: React.FC = () => {
  const [success, setSuccess] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [editingPanel, setEditingPanel] = useState<VPNPanel | null>(null);
  const [formData, setFormData] = useState<CreatePanelRequest>({
    name: '',
    base_url: '',
    api_username: '',
    api_password: '',
    is_active: true,
    proxies: {},
    inbounds: {}
  });

  const { data: panels = [], isLoading, error } = usePanels();
  const createPanelMutation = useCreatePanel();
  const updatePanelMutation = useUpdatePanel();
  const deletePanelMutation = useDeletePanel();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (editingPanel) {
        await updatePanelMutation.mutateAsync({ id: editingPanel.id, data: formData });
        setSuccess('Panel updated successfully');
      } else {
        await createPanelMutation.mutateAsync(formData);
        setSuccess('Panel created successfully');
      }
      setShowModal(false);
      setEditingPanel(null);
      resetForm();
    } catch (err) {
      console.error('Panel save error:', err);
    }
  };

  const handleEdit = (panel: VPNPanel) => {
    setEditingPanel(panel);
    setFormData({
      name: panel.name,
      base_url: panel.base_url,
      api_username: panel.api_username,
      api_password: panel.api_password,
      is_active: panel.is_active,
      proxies: panel.proxies || {},
      inbounds: panel.inbounds || {}
    });
    setShowModal(true);
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this panel?')) {
      try {
        await deletePanelMutation.mutateAsync(id);
        setSuccess('Panel deleted successfully');
      } catch (err) {
        console.error('Panel delete error:', err);
      }
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      base_url: '',
      api_username: '',
      api_password: '',
      is_active: true,
      proxies: {},
      inbounds: {}
    });
  };

  const openCreateModal = () => {
    setEditingPanel(null);
    resetForm();
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setEditingPanel(null);
    resetForm();
  };

  if (isLoading) {
    return <div className="loading">Loading panels...</div>;
  }

  return (
    <div>
      <div className="page-header">
        <h1>VPN Panels</h1>
        <p>Manage your VPN panel configurations</p>
      </div>

      {error && <div className="error">Failed to load panels</div>}
      {success && <div className="success">{success}</div>}

      <div className="card">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
          <h3>Panel List</h3>
          <button className="btn btn-primary" onClick={openCreateModal}>
            Add New Panel
          </button>
        </div>

        {panels.length === 0 ? (
          <p>No panels configured yet. Add your first panel to get started.</p>
        ) : (
          <table className="table">
            <thead>
              <tr>
                <th>Name</th>
                <th>Base URL</th>
                <th>Username</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {panels.map((panel) => (
                <tr key={panel.id}>
                  <td>{panel.name}</td>
                  <td>{panel.base_url}</td>
                  <td>{panel.api_username}</td>
                  <td>
                    <span style={{ 
                      padding: '0.25rem 0.5rem', 
                      borderRadius: '4px', 
                      fontSize: '0.875rem',
                      backgroundColor: panel.is_active ? '#d4edda' : '#f8d7da',
                      color: panel.is_active ? '#155724' : '#721c24'
                    }}>
                      {panel.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td>
                    <div className="actions">
                      <button 
                        className="btn btn-warning btn-sm" 
                        onClick={() => handleEdit(panel)}
                      >
                        Edit
                      </button>
                      <button 
                        className="btn btn-danger btn-sm" 
                        onClick={() => handleDelete(panel.id)}
                      >
                        Delete
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>

      {showModal && (
        <div className="modal">
          <div className="modal-content">
            <div className="modal-header">
              <h3>{editingPanel ? 'Edit Panel' : 'Add New Panel'}</h3>
              <button className="close-btn" onClick={closeModal}>&times;</button>
            </div>
            
            <form onSubmit={handleSubmit}>
              <div className="form-group">
                <label>Panel Name</label>
                <input
                  type="text"
                  className="form-control"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  required
                />
              </div>
              
              <div className="form-group">
                <label>Base URL</label>
                <input
                  type="url"
                  className="form-control"
                  value={formData.base_url}
                  onChange={(e) => setFormData({...formData, base_url: e.target.value})}
                  placeholder="https://panel.example.com"
                  required
                />
              </div>
              
              <div className="form-row">
                <div className="form-group">
                  <label>API Username</label>
                  <input
                    type="text"
                    className="form-control"
                    value={formData.api_username}
                    onChange={(e) => setFormData({...formData, api_username: e.target.value})}
                    required
                  />
                </div>
                
                <div className="form-group">
                  <label>API Password</label>
                  <input
                    type="password"
                    className="form-control"
                    value={formData.api_password}
                    onChange={(e) => setFormData({...formData, api_password: e.target.value})}
                    required
                  />
                </div>
              </div>
              
              <div className="form-group">
                <label>
                  <input
                    type="checkbox"
                    checked={formData.is_active}
                    onChange={(e) => setFormData({...formData, is_active: e.target.checked})}
                    style={{ marginRight: '0.5rem' }}
                  />
                  Active
                </label>
              </div>
              
              <div style={{ display: 'flex', gap: '1rem', justifyContent: 'flex-end' }}>
                <button type="button" className="btn" onClick={closeModal} style={{ backgroundColor: '#6c757d', color: 'white' }}>
                  Cancel
                </button>
                <button type="submit" className="btn btn-primary">
                  {editingPanel ? 'Update' : 'Create'} Panel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default Panels;