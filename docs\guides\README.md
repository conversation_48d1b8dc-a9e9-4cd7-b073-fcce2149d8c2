# VPN Bot Documentation

This directory contains comprehensive documentation for the VPN Telegram Bot project.

## Documentation Structure

### Setup and Installation
- [Installation Guide](./installation.md) - Complete setup instructions
- [Configuration Guide](./configuration.md) - Environment and bot configuration
- [Database Setup](./database-setup.md) - Database initialization and migration

### Development
- [Architecture Overview](./architecture.md) - System design and component structure
- [Development Guide](./development.md) - Development workflow and best practices
- [API Documentation](./api-documentation.md) - Bot handlers and service APIs

### Deployment
- [Docker Deployment](./docker-deployment.md) - Containerized deployment guide
- [Production Setup](./production-setup.md) - Production environment configuration

### Features
- [Bot Features](./bot-features.md) - Complete feature documentation
- [Payment Integration](./payment-integration.md) - Telegram payments setup
- [VPN Management](./vpn-management.md) - Marzban API integration

### Troubleshooting
- [Common Issues](./troubleshooting.md) - Solutions to common problems
- [FAQ](./faq.md) - Frequently asked questions

## Quick Start

1. Follow the [Installation Guide](./installation.md)
2. Configure your environment using the [Configuration Guide](./configuration.md)
3. Set up the database with [Database Setup](./database-setup.md)
4. Start development with the [Development Guide](./development.md)

## Project Overview

This is a comprehensive Telegram VPN bot that provides:

- **Trial VPN Accounts**: Free trial accounts for new users
- **Premium Subscriptions**: Paid VPN plans with Telegram payments
- **Channel Subscription**: Mandatory channel membership verification
- **Multi-language Support**: English and Persian language support
- **Admin Panel**: Web-based administration interface
- **Analytics**: User statistics and payment tracking

## Technology Stack

- **Backend**: Python 3.11+ with python-telegram-bot
- **Database**: PostgreSQL with asyncpg
- **Cache**: Redis for session management
- **VPN Backend**: Marzban API integration
- **Admin Panel**: React with TypeScript
- **Deployment**: Docker and Docker Compose

## Support

For issues and questions:
1. Check the [Troubleshooting Guide](./troubleshooting.md)
2. Review the [FAQ](./faq.md)
3. Create an issue in the project repository