"""Payment service for handling premium subscriptions."""

import asyncio
import logging
import json
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from telegram import LabeledPrice
from bot.database import get_db_connection
from bot.config import settings
from bot.services.vpn_service import vpn_service
from bot.services.nowpayments_service import nowpayments_service
from bot.services.ton_service import ton_service

logger = logging.getLogger(__name__)


class PaymentService:
    """Service for handling payments and premium subscriptions."""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def get_premium_plans(self, is_active: bool = True) -> List[Dict[str, Any]]:
        """Get all premium plans."""
        try:
            async with get_db_connection() as conn:
                query = "SELECT * FROM premium_plans"
                params = []
                
                if is_active:
                    query += " WHERE is_active = $1"
                    params.append(True)
                
                query += " ORDER BY price ASC"
                
                plans = await conn.fetch(query, *params)
                result = []
                for plan in plans:
                    plan_dict = dict(plan)
                    # Convert data_limit from bytes to GB for display
                    # Ensure minimum 1 GB if data_limit is less than 1 GB
                    try:
                        data_limit = plan_dict.get('data_limit', 0)
                        if data_limit is None:
                            data_limit = 0
                        data_limit_gb = data_limit / (1024 * 1024 * 1024)
                        plan_dict['data_limit_gb'] = max(1, int(data_limit_gb)) if data_limit_gb > 0 else 1
                    except Exception as e:
                        self.logger.error(f"Error calculating data_limit_gb for plan {plan_dict.get('id')}: {e}")
                        plan_dict['data_limit_gb'] = 1  # Default to 1 GB
                    result.append(plan_dict)
                return result
        
        except Exception as e:
            self.logger.error(f"Error getting premium plans: {e}")
            return []
    
    async def get_available_plans(self, is_active: bool = True) -> List[Dict[str, Any]]:
        """Get available premium plans (alias for get_premium_plans)."""
        return await self.get_premium_plans(is_active)
    
    async def get_plan_by_id(self, plan_id: int) -> Optional[Dict[str, Any]]:
        """Get premium plan by ID."""
        try:
            async with get_db_connection() as conn:
                plan = await conn.fetchrow(
                    "SELECT * FROM premium_plans WHERE id = $1", plan_id
                )
                if plan:
                    plan_dict = dict(plan)
                    # Convert data_limit from bytes to GB for display
                    # Ensure minimum 1 GB if data_limit is less than 1 GB
                    try:
                        data_limit = plan_dict.get('data_limit', 0)
                        if data_limit is None:
                            data_limit = 0
                        data_limit_gb = data_limit / (1024 * 1024 * 1024)
                        plan_dict['data_limit_gb'] = max(1, int(data_limit_gb)) if data_limit_gb > 0 else 1
                    except Exception as e:
                        self.logger.error(f"Error calculating data_limit_gb for plan {plan_dict.get('id')}: {e}")
                        plan_dict['data_limit_gb'] = 1  # Default to 1 GB
                    return plan_dict
                return None
        
        except Exception as e:
            self.logger.error(f"Error getting plan {plan_id}: {e}")
            return None

    async def get_user_payments(self, user_id: int, limit: int = 10, offset: int = 0) -> List[Dict[str, Any]]:
        """Get user's payment history."""
        try:
            async with get_db_connection() as conn:
                query = """
                    SELECT p.*, pp.name as plan_name
                    FROM payments p
                    JOIN premium_plans pp ON p.plan_id = pp.id
                    WHERE p.user_id = $1
                    ORDER BY p.created_at DESC
                    LIMIT $2 OFFSET $3
                """
                payments = await conn.fetch(query, user_id, limit, offset)
                return [dict(payment) for payment in payments]
        except Exception as e:
            self.logger.error(f"Error getting user payments for user {user_id}: {e}")
            return []

    async def cancel_subscription(self, subscription_id: int, user_id: int) -> bool:
        """Cancel a user's subscription."""
        try:
            async with get_db_connection() as conn:
                # First, verify that the subscription belongs to the user
                subscription = await conn.fetchrow(
                    "SELECT * FROM subscriptions WHERE id = $1 AND user_id = $2",
                    subscription_id, user_id
                )
                
                if not subscription:
                    self.logger.warning(f"User {user_id} tried to cancel subscription {subscription_id} which does not belong to them or does not exist.")
                    return False

                # Deactivate the subscription
                await conn.execute(
                    "UPDATE subscriptions SET is_active = FALSE, updated_at = NOW() WHERE id = $1",
                    subscription_id
                )

                self.logger.info(f"Subscription {subscription_id} for user {user_id} has been canceled.")
                return True
        except Exception as e:
            self.logger.error(f"Error canceling subscription {subscription_id} for user {user_id}: {e}")
            return False
    
    def create_invoice_payload(self, user_id: int, plan_id: int) -> str:
        """Create invoice payload for payment."""
        payload_data = {
            'user_id': user_id,
            'plan_id': plan_id,
            'timestamp': int(datetime.now().timestamp())
        }
        return json.dumps(payload_data)
    
    def parse_invoice_payload(self, payload: str) -> Optional[Dict[str, Any]]:
        """Parse invoice payload from payment."""
        try:
            return json.loads(payload)
        except Exception as e:
            self.logger.error(f"Error parsing invoice payload: {e}")
            return None

    async def validate_payment_data(self, payment_data: Dict[str, Any]) -> bool:
        """Validate payment data for completeness and correctness."""
        try:
            # Check required fields
            required_fields = ['user_id', 'plan_id', 'amount']
            for field in required_fields:
                if field not in payment_data:
                    self.logger.error(f"Missing required field: {field}")
                    return False

            # Validate user exists
            user_id = payment_data['user_id']
            if not isinstance(user_id, int) or user_id <= 0:
                self.logger.error(f"Invalid user_id: {user_id}")
                return False

            # Validate plan exists
            plan_id = payment_data['plan_id']
            if not isinstance(plan_id, int) or plan_id <= 0:
                self.logger.error(f"Invalid plan_id: {plan_id}")
                return False

            # Validate amount
            amount = payment_data['amount']
            if not isinstance(amount, (int, float)) or amount <= 0:
                self.logger.error(f"Invalid amount: {amount}")
                return False

            return True

        except Exception as e:
            self.logger.error(f"Error validating payment data: {e}")
            return False
    
    async def create_invoice(
        self,
        plan_id: int,
        user_id: int,
        payment_method: str = 'telegram_payment',
        amount: Optional[float] = None
    ) -> Optional[Dict[str, Any]]:
        """Create invoice for premium plan purchase with proper transaction logging."""
        try:
            plan = await self.get_plan_by_id(plan_id)
            if not plan:
                raise ValueError("Plan not found")

            # Use provided amount or plan price
            final_amount = amount if amount is not None else plan['price']

            # Handle different payment methods
            if payment_method == 'telegram_stars':
                return await self.create_stars_invoice(plan_id, user_id)
            elif payment_method == 'crypto':
                return await self.create_crypto_invoice(user_id, plan_id)
            elif payment_method == 'ton':
                return await self.create_ton_invoice(user_id, plan_id)
            elif payment_method == 'card':
                # For card payments, create a pending transaction record
                transaction_id = f"card_{user_id}_{plan_id}_{int(datetime.now().timestamp())}"

                # Log transaction creation
                async with get_db_connection() as conn:
                    await conn.execute(
                        """
                        INSERT INTO payments
                        (user_id, plan_id, amount, currency, payment_method, transaction_id, status, created_at)
                        VALUES ($1, $2, $3, $4, $5, $6, $7, NOW())
                        """,
                        user_id, plan_id, final_amount, 'USD', payment_method, transaction_id, 'pending'
                    )

                self.logger.info(f"Created card payment transaction {transaction_id} for user {user_id}, plan {plan_id}")

                # Return payment URL (placeholder for now)
                return {
                    'payment_url': f"https://payment-provider.com/pay/{transaction_id}",
                    'transaction_id': transaction_id,
                    'amount': final_amount
                }
            else:
                # Default Telegram payment
                payload = self.create_invoice_payload(user_id, plan_id)

                # Create labeled prices
                prices = [
                    LabeledPrice(
                        label=plan['name'],
                        amount=int(final_amount * 100)  # Convert to cents
                    )
                ]

                # Log transaction creation
                transaction_id = f"telegram_{user_id}_{plan_id}_{int(datetime.now().timestamp())}"
                async with get_db_connection() as conn:
                    await conn.execute(
                        """
                        INSERT INTO payments
                        (user_id, plan_id, amount, currency, payment_method, transaction_id, status, payload, created_at)
                        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW())
                        """,
                        user_id, plan_id, final_amount, 'USD', payment_method, transaction_id, 'pending', payload
                    )

                self.logger.info(f"Created telegram payment transaction {transaction_id} for user {user_id}, plan {plan_id}")

                invoice_data = {
                    'title': f"Premium VPN - {plan['name']}",
                    'description': plan['description'],
                    'payload': payload,
                    'provider_token': settings.PAYMENT_PROVIDER_TOKEN,
                    'currency': 'USD',
                    'prices': prices,
                    'start_parameter': f"premium_{plan_id}",
                    'transaction_id': transaction_id
                }

                return invoice_data

        except Exception as e:
            self.logger.error(f"Error creating invoice for plan {plan_id}, payment method {payment_method}: {e}")
            return None
    
    async def create_stars_invoice(
        self, 
        plan_id: int, 
        user_id: int
    ) -> Optional[Dict[str, Any]]:
        """Create Telegram Stars invoice for premium plan purchase."""
        try:
            plan = await self.get_plan_by_id(plan_id)
            if not plan:
                raise ValueError("Plan not found")
            
            # Create invoice payload
            payload = self.create_invoice_payload(user_id, plan_id)
            
            # Convert USD price to Stars (1 USD ≈ 1 Star for simplicity)
            stars_amount = int(plan['price'])

            # Log transaction creation
            transaction_id = f"stars_{user_id}_{plan_id}_{int(datetime.now().timestamp())}"
            async with get_db_connection() as conn:
                await conn.execute(
                    """
                    INSERT INTO payments
                    (user_id, plan_id, amount, currency, payment_method, transaction_id, status, payload, created_at)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW())
                    """,
                    user_id, plan_id, stars_amount, 'XTR', 'telegram_stars', transaction_id, 'pending', payload
                )

            self.logger.info(f"Created Stars payment transaction {transaction_id} for user {user_id}, plan {plan_id}")

            # Create labeled prices for Stars
            prices = [
                LabeledPrice(
                    label=plan['name'],
                    amount=stars_amount  # Stars amount (no conversion needed)
                )
            ]
            
            invoice_data = {
                'title': f"⭐ Premium VPN - {plan['name']}",
                'description': f"{plan['description']}\n\n💫 Pay with Telegram Stars",
                'payload': payload,
                'provider_token': "",  # Empty for Stars payments
                'currency': 'XTR',  # Telegram Stars currency
                'prices': prices,
                'max_tip_amount': 0,
                'suggested_tip_amounts': [],
                'start_parameter': f"stars_{plan_id}",
                'transaction_id': transaction_id
            }
            
            return invoice_data
        
        except Exception as e:
            self.logger.error(f"Error creating Stars invoice for plan {plan_id}: {e}")
            return None
    
    async def process_successful_payment(
        self,
        user_id: int,
        payment_data: Dict[str, Any],
        payment_method: str = 'telegram_payment',
        retry_count: int = 0
    ) -> Optional[Dict[str, Any]]:
        """Process successful payment and create VPN account with retry mechanism."""
        max_retries = 3

        try:
            if payment_method in ['telegram_payment', 'telegram_stars']:
                # Parse payload for Telegram payments
                payload = self.parse_invoice_payload(payment_data.get('invoice_payload', ''))
                if not payload:
                    raise ValueError("Invalid payment payload")

                plan_id = payload.get('plan_id')
                if not plan_id:
                    raise ValueError("Plan ID not found in payload")

            elif payment_method == 'crypto':
                # Handle crypto payment
                plan_id = payment_data.get('plan_id')
                if not plan_id:
                    raise ValueError("Plan ID not found in payment data")

            elif payment_method == 'ton':
                # Handle TON payment
                plan_id = payment_data.get('plan_id')
                if not plan_id:
                    raise ValueError("Plan ID not found in payment data")

            else:
                # For other payment methods, plan_id should be in payment_data
                plan_id = payment_data.get('plan_id')
                if not plan_id:
                    raise ValueError("Plan ID not found in payment data")

                # Determine payment method and amount based on currency
                currency = payment_data.get('currency', 'USD')
                if currency == 'XTR':
                    # Telegram Stars payment
                    payment_method = 'telegram_stars'
                else:
                    # Traditional payment
                    payment_method = 'telegram_payment'
            
            # Get plan details
            plan = await self.get_plan_by_id(plan_id)
            if not plan:
                raise ValueError("Plan not found")
            
            amount = plan['price']
            
            async with get_db_connection() as conn:
                # Record payment based on method
                if payment_method in ['telegram_payment', 'telegram_stars']:
                    currency = payment_data.get('currency', 'USD')
                    payment_record = await conn.fetchrow(
                        """
                        INSERT INTO payments 
                        (user_id, plan_id, amount, currency, telegram_payment_charge_id, 
                         provider_payment_charge_id, payload, payment_method, created_at)
                        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW())
                        RETURNING *
                        """,
                        user_id, plan_id, amount, currency,
                        payment_data.get('telegram_payment_charge_id'),
                        payment_data.get('provider_payment_charge_id'),
                        payment_data.get('invoice_payload'),
                        payment_method
                    )
                
                elif payment_method == 'crypto':
                    payment_record = await conn.fetchrow(
                        """
                        INSERT INTO payments 
                        (user_id, plan_id, amount, currency, payment_method, created_at)
                        VALUES ($1, $2, $3, $4, $5, NOW())
                        RETURNING *
                        """,
                        user_id, plan_id, amount, 'USD', payment_method
                    )
                
                elif payment_method == 'ton':
                    payment_record = await conn.fetchrow(
                        """
                        INSERT INTO payments 
                        (user_id, plan_id, amount, currency, payment_method, created_at)
                        VALUES ($1, $2, $3, $4, $5, NOW())
                        RETURNING *
                        """,
                        user_id, plan_id, amount, 'TON', payment_method
                    )
                
                # Create premium subscription
                subscription = await conn.fetchrow(
                    """
                    INSERT INTO premium_subscriptions 
                    (user_id, plan_id, payment_id, expires_at, created_at)
                    VALUES ($1, $2, $3, $4, NOW())
                    RETURNING *
                    """,
                    user_id, plan_id, payment_record['id'],
                    datetime.now() + timedelta(days=plan['duration_days'])
                )
                
                # Create VPN account
                vpn_account = await vpn_service.create_premium_vpn(
                    user_id=user_id,
                    plan_id=plan_id,
                    duration_days=plan['duration_days'],
                    data_limit_gb=plan['data_limit_gb']
                )
                
                if not vpn_account:
                    raise Exception("Failed to create VPN account")
                
                self.logger.info(
                    f"Processed {payment_method} payment for user {user_id}, plan {plan_id}, "
                    f"created VPN account {vpn_account['username']}"
                )
                
                return {
                    'success': True,
                    'payment_id': payment_record['id'],
                    'subscription': dict(subscription),
                    'vpn_account': vpn_account,
                    'plan': dict(plan)
                }

        except Exception as e:
            self.logger.error(f"Error processing payment for user {user_id} (attempt {retry_count + 1}): {e}")

            # Retry logic for transient errors
            if retry_count < max_retries and self._is_retryable_error(e):
                self.logger.info(f"Retrying payment processing for user {user_id}, attempt {retry_count + 2}")
                await asyncio.sleep(2 ** retry_count)  # Exponential backoff
                return await self.process_successful_payment(
                    user_id, payment_data, payment_method, retry_count + 1
                )

            # If all retries failed or non-retryable error
            return {
                'success': False,
                'error': str(e),
                'retry_count': retry_count
            }

    def _is_retryable_error(self, error: Exception) -> bool:
        """Determine if an error is retryable."""
        retryable_errors = [
            "connection",
            "timeout",
            "temporary",
            "network",
            "database",
            "deadlock"
        ]

        error_str = str(error).lower()
        return any(keyword in error_str for keyword in retryable_errors)

    async def verify_payment_status(self, payment_id: str, payment_method: str) -> Dict[str, Any]:
        """Verify payment status across different payment methods."""
        try:
            if payment_method == 'crypto':
                return await self.check_crypto_payment_status(payment_id)
            elif payment_method == 'ton':
                return await ton_service.check_ton_payment_status(payment_id)
            elif payment_method in ['telegram_payment', 'telegram_stars']:
                # For Telegram payments, status is confirmed via webhook
                async with get_db_connection() as conn:
                    payment = await conn.fetchrow(
                        "SELECT * FROM payments WHERE telegram_payment_charge_id = $1",
                        payment_id
                    )
                    if payment:
                        return {
                            'status': 'completed',
                            'payment_data': dict(payment)
                        }
                    return {'status': 'not_found'}
            else:
                return {'status': 'unsupported_method'}

        except Exception as e:
            self.logger.error(f"Error verifying payment status for {payment_id}: {e}")
            return {'status': 'error', 'error': str(e)}

    async def handle_failed_payment(self, user_id: int, payment_data: Dict[str, Any], reason: str) -> bool:
        """Handle failed payment and log for retry or refund."""
        try:
            async with get_db_connection() as conn:
                await conn.execute(
                    """
                    INSERT INTO failed_payments
                    (user_id, payment_data, failure_reason, created_at, retry_count)
                    VALUES ($1, $2, $3, NOW(), 0)
                    """,
                    user_id, json.dumps(payment_data), reason
                )

            self.logger.info(f"Logged failed payment for user {user_id}: {reason}")
            return True

        except Exception as e:
            self.logger.error(f"Error logging failed payment for user {user_id}: {e}")
            return False

    async def retry_failed_payments(self, max_retry_count: int = 3) -> Dict[str, Any]:
        """Retry failed payments that are eligible for retry."""
        try:
            async with get_db_connection() as conn:
                # Get failed payments eligible for retry
                failed_payments = await conn.fetch(
                    """
                    SELECT * FROM failed_payments
                    WHERE retry_count < $1
                    AND created_at > NOW() - INTERVAL '24 hours'
                    ORDER BY created_at ASC
                    LIMIT 50
                    """,
                    max_retry_count
                )

                retry_results = {
                    'total_retried': 0,
                    'successful_retries': 0,
                    'failed_retries': 0,
                    'details': []
                }

                for failed_payment in failed_payments:
                    retry_results['total_retried'] += 1

                    try:
                        payment_data = json.loads(failed_payment['payment_data'])

                        # Attempt to reprocess the payment
                        result = await self.process_successful_payment(
                            failed_payment['user_id'],
                            payment_data,
                            payment_data.get('payment_method', 'telegram_payment')
                        )

                        if result and result.get('success'):
                            retry_results['successful_retries'] += 1

                            # Mark as resolved
                            await conn.execute(
                                "DELETE FROM failed_payments WHERE id = $1",
                                failed_payment['id']
                            )

                            retry_results['details'].append({
                                'payment_id': failed_payment['id'],
                                'user_id': failed_payment['user_id'],
                                'status': 'success'
                            })
                        else:
                            retry_results['failed_retries'] += 1

                            # Increment retry count
                            await conn.execute(
                                "UPDATE failed_payments SET retry_count = retry_count + 1 WHERE id = $1",
                                failed_payment['id']
                            )

                            retry_results['details'].append({
                                'payment_id': failed_payment['id'],
                                'user_id': failed_payment['user_id'],
                                'status': 'failed',
                                'error': result.get('error', 'Unknown error') if result else 'No result'
                            })

                    except Exception as e:
                        retry_results['failed_retries'] += 1
                        self.logger.error(f"Error retrying payment {failed_payment['id']}: {e}")

                        retry_results['details'].append({
                            'payment_id': failed_payment['id'],
                            'user_id': failed_payment['user_id'],
                            'status': 'error',
                            'error': str(e)
                        })

                return retry_results

        except Exception as e:
            self.logger.error(f"Error retrying failed payments: {e}")
            return {
                'total_retried': 0,
                'successful_retries': 0,
                'failed_retries': 0,
                'error': str(e)
            }
    
    async def get_user_subscriptions(
        self, 
        user_id: int, 
        active_only: bool = False
    ) -> List[Dict[str, Any]]:
        """Get user's premium subscriptions."""
        try:
            async with get_db_connection() as conn:
                query = """
                    SELECT ps.*, pp.name as plan_name, pp.price as plan_price,
                           pp.duration_days, pp.data_limit
                    FROM premium_subscriptions ps
                    JOIN premium_plans pp ON ps.plan_id = pp.id
                    WHERE ps.user_id = $1
                """
                params = [user_id]
                
                if active_only:
                    query += " AND ps.expires_at > NOW()"
                
                query += " ORDER BY ps.created_at DESC"
                
                subscriptions = await conn.fetch(query, *params)
                return [dict(sub) for sub in subscriptions]
        
        except Exception as e:
            self.logger.error(f"Error getting subscriptions for user {user_id}: {e}")
            return []
    
    async def get_user_payments(self, user_id: int) -> List[Dict[str, Any]]:
        """Get user's payment history."""
        try:
            async with get_db_connection() as conn:
                payments = await conn.fetch(
                    """
                    SELECT p.*, pp.name as plan_name
                    FROM payments p
                    JOIN premium_plans pp ON p.plan_id = pp.id
                    WHERE p.user_id = $1
                    ORDER BY p.created_at DESC
                    """,
                    user_id
                )
                return [dict(payment) for payment in payments]
        
        except Exception as e:
            self.logger.error(f"Error getting payments for user {user_id}: {e}")
            return []
    
    async def extend_subscription(
        self, 
        subscription_id: int, 
        additional_days: int
    ) -> bool:
        """Extend premium subscription."""
        try:
            async with get_db_connection() as conn:
                # Get current subscription
                subscription = await conn.fetchrow(
                    "SELECT * FROM premium_subscriptions WHERE id = $1", subscription_id
                )
                
                if not subscription:
                    return False
                
                # Calculate new expiration date
                current_expires = subscription['expires_at']
                if current_expires < datetime.now():
                    # If already expired, extend from now
                    new_expires = datetime.now() + timedelta(days=additional_days)
                else:
                    # If still active, extend from current expiration
                    new_expires = current_expires + timedelta(days=additional_days)
                
                # Update subscription
                await conn.execute(
                    "UPDATE premium_subscriptions SET expires_at = $1 WHERE id = $2",
                    new_expires, subscription_id
                )
                
                # Also extend associated VPN account
                vpn_account = await conn.fetchrow(
                    """
                    SELECT username FROM vpn_accounts 
                    WHERE user_id = $1 AND plan_id = $2
                    ORDER BY created_at DESC LIMIT 1
                    """,
                    subscription['user_id'], subscription['plan_id']
                )
                
                if vpn_account:
                    await vpn_service.extend_account(
                        vpn_account['username'], additional_days
                    )
                
                self.logger.info(
                    f"Extended subscription {subscription_id} by {additional_days} days"
                )
                return True
        
        except Exception as e:
            self.logger.error(f"Error extending subscription {subscription_id}: {e}")
            return False
    
    async def create_crypto_invoice(self, user_id: int, plan_id: int, pay_currency: str = "btc") -> Optional[Dict[str, Any]]:
        """Create a cryptocurrency invoice using NowPayments."""
        try:
            # Check if NowPayments is configured
            if not settings.NOWPAYMENTS_API_KEY:
                self.logger.error("NowPayments API key not configured")
                return None
            
            # Create NowPayments invoice
            payment_data = await nowpayments_service.create_invoice_for_plan(
                user_id=user_id,
                plan_id=plan_id,
                pay_currency=pay_currency
            )
            
            if payment_data:
                self.logger.info(f"Crypto invoice created for user {user_id}, plan {plan_id}")
                return {
                    'payment_id': payment_data['payment_id'],
                    'payment_url': payment_data.get('invoice_url') or payment_data.get('pay_address'),
                    'pay_address': payment_data.get('pay_address'),
                    'pay_amount': payment_data.get('pay_amount'),
                    'pay_currency': payment_data.get('pay_currency'),
                    'price_amount': payment_data.get('price_amount'),
                    'price_currency': payment_data.get('price_currency'),
                    'order_id': payment_data.get('order_id'),
                    'payment_status': payment_data.get('payment_status')
                }
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error creating crypto invoice: {e}")
            return None
    
    async def get_crypto_payment_status(self, payment_id: str) -> Optional[Dict[str, Any]]:
        """Get cryptocurrency payment status."""
        try:
            return await nowpayments_service.get_payment_status(payment_id)
        except Exception as e:
            self.logger.error(f"Error getting crypto payment status: {e}")
            return None
    
    async def create_ton_invoice(self, user_id: int, plan_id: int) -> Optional[Dict[str, Any]]:
        """Create a TON payment invoice for premium plan purchase."""
        try:
            # Check if TON is configured
            if not hasattr(settings, 'TON_MASTER_WALLET') or not settings.TON_MASTER_WALLET:
                self.logger.error("TON master wallet not configured")
                return None
            
            # Create TON payment
            payment_data = await ton_service.create_ton_payment(
                user_id=user_id,
                plan_id=plan_id
            )
            
            if payment_data:
                self.logger.info(f"TON invoice created for user {user_id}, plan {plan_id}")
                return payment_data
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error creating TON invoice: {e}")
            return None
    
    async def check_ton_payment_status(self, payment_id: str) -> Optional[Dict[str, Any]]:
        """Get TON payment status."""
        try:
            payment_status = await ton_service.check_ton_payment_status(payment_id)
            
            if not payment_status:
                return None
            
            # If payment is successful, process it
            if payment_status.get('status') == 'confirmed':
                # Get payment details from database
                async with get_db_connection() as conn:
                    payment_record = await conn.fetchrow(
                        "SELECT * FROM ton_payments WHERE payment_id = $1",
                        payment_id
                    )
                    
                    if payment_record and payment_record['status'] != 'confirmed':
                        # Update payment status
                        await conn.execute(
                            "UPDATE ton_payments SET status = 'confirmed', updated_at = NOW() WHERE payment_id = $1",
                            payment_id
                        )
                        
                        # Process successful payment
                        await self.process_successful_payment(
                            payment_record['user_id'],
                            {'plan_id': payment_record['plan_id']},
                            'ton'
                        )
            
            return payment_status
            
        except Exception as e:
            self.logger.error(f"Error getting TON payment status: {e}")
            return None

    async def get_available_crypto_currencies(self) -> List[Dict[str, Any]]:
        """Get list of available cryptocurrency currencies."""
        try:
            currencies = await nowpayments_service.get_available_currencies()
            # Filter to show only popular cryptocurrencies
            popular_cryptos = ['btc', 'eth', 'ltc', 'usdt', 'usdc', 'trx', 'bnb']
            return [c for c in currencies if c.get('code', '').lower() in popular_cryptos]
        except Exception as e:
            self.logger.error(f"Error getting crypto currencies: {e}")
            return []
    
    async def create_crypto_payment(self, user_id: int, plan_id: int, currency: str) -> Optional[Dict[str, Any]]:
        """Create a cryptocurrency payment for a premium plan."""
        try:
            # Get plan details
            plan = await self.get_plan_by_id(plan_id)
            if not plan:
                return None
            
            # Create payment with NowPayments
            payment_data = await nowpayments_service.create_payment(
                price_amount=plan['price'],
                price_currency='USD',
                pay_currency=currency,
                order_id=f"plan_{plan_id}_user_{user_id}_{int(datetime.now().timestamp())}",
                order_description=f"Premium VPN Plan: {plan['name']}"
            )
            
            if not payment_data:
                return None
            
            # Store payment in database
            async with get_db_connection() as conn:
                await conn.execute(
                    """
                    INSERT INTO crypto_payments (
                        payment_id, user_id, plan_id, order_id, amount, currency,
                        pay_currency, pay_amount, status, payment_url
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                    """,
                    payment_data['payment_id'],
                    user_id,
                    plan_id,
                    payment_data['order_id'],
                    payment_data['price_amount'],
                    payment_data['price_currency'],
                    payment_data['pay_currency'],
                    payment_data['pay_amount'],
                    payment_data['payment_status'],
                    payment_data.get('invoice_url', '')
                )
            
            return {
                'payment_id': payment_data['payment_id'],
                'amount': payment_data['pay_amount'],
                'currency': payment_data['pay_currency'],
                'address': payment_data['pay_address'],
                'expires_in': 60,  # 60 minutes default
                'payment_url': payment_data.get('invoice_url', '')
            }
            
        except Exception as e:
            self.logger.error(f"Error creating crypto payment: {e}")
            return None
    
    async def check_crypto_payment_status(self, payment_id: str) -> Optional[Dict[str, Any]]:
        """Get cryptocurrency payment status."""
        try:
            # Get payment status from NowPayments
            payment_status = await nowpayments_service.get_payment_status(payment_id)
            
            if not payment_status:
                return None
            
            # Update payment status in database
            async with get_db_connection() as conn:
                await conn.execute(
                    """
                    UPDATE crypto_payments 
                    SET status = $1, updated_at = NOW()
                    WHERE payment_id = $2
                    """,
                    payment_status['payment_status'],
                    payment_id
                )
                
                # If payment is successful, process it
                if payment_status['payment_status'] in ['finished', 'confirmed']:
                    # Get payment details from database
                    payment_record = await conn.fetchrow(
                        "SELECT * FROM crypto_payments WHERE payment_id = $1",
                        payment_id
                    )
                    
                    if payment_record and payment_record['status'] not in ['finished', 'confirmed']:
                        # Process successful payment
                        await self.process_successful_payment(
                            payment_record['user_id'],
                            {'plan_id': payment_record['plan_id']},
                            'crypto'
                        )
            
            return payment_status
            
        except Exception as e:
            self.logger.error(f"Error getting crypto payment status: {e}")
            return None
    
    async def _process_crypto_payment_success(self, user_id: int, plan_id: int, payment_record: Dict[str, Any]) -> None:
        """Process successful crypto payment."""
        try:
            # Get plan details
            plan = await self.get_plan_by_id(plan_id)
            if not plan:
                return
            
            async with get_db_connection() as conn:
                # Create premium subscription
                subscription = await conn.fetchrow(
                    """
                    INSERT INTO premium_subscriptions 
                    (user_id, plan_id, crypto_payment_id, expires_at, created_at)
                    VALUES ($1, $2, $3, $4, NOW())
                    RETURNING *
                    """,
                    user_id, plan_id, payment_record['id'],
                    datetime.now() + timedelta(days=plan['duration_days'])
                )
                
                # Create VPN account
                vpn_account = await vpn_service.create_premium_vpn(
                    user_id=user_id,
                    plan_id=plan_id,
                    duration_days=plan['duration_days'],
                    data_limit_gb=plan['data_limit_gb']
                )
                
                if vpn_account:
                    self.logger.info(
                        f"Processed crypto payment for user {user_id}, plan {plan_id}, "
                        f"payment {payment_record['payment_id']}"
                    )
                else:
                    self.logger.error(
                        f"Failed to create VPN account for crypto payment {payment_record['payment_id']}"
                    )
                    
        except Exception as e:
            self.logger.error(f"Error processing crypto payment success: {e}")
    
    async def cancel_subscription(self, subscription_id: int) -> bool:
        """Cancel premium subscription."""
        try:
            async with get_db_connection() as conn:
                # Get subscription details
                subscription = await conn.fetchrow(
                    "SELECT * FROM premium_subscriptions WHERE id = $1", subscription_id
                )
                
                if not subscription:
                    return False
                
                # Mark as cancelled
                await conn.execute(
                    "UPDATE premium_subscriptions SET cancelled_at = NOW() WHERE id = $1",
                    subscription_id
                )
                
                # Optionally disable associated VPN account
                vpn_account = await conn.fetchrow(
                    """
                    SELECT username FROM vpn_accounts 
                    WHERE user_id = $1 AND plan_id = $2
                    ORDER BY created_at DESC LIMIT 1
                    """,
                    subscription['user_id'], subscription['plan_id']
                )
                
                if vpn_account:
                    await vpn_service.delete_account(vpn_account['username'])
                
                self.logger.info(f"Cancelled subscription {subscription_id}")
                return True
        
        except Exception as e:
            self.logger.error(f"Error cancelling subscription {subscription_id}: {e}")
            return False


# Global instance
payment_service = PaymentService()