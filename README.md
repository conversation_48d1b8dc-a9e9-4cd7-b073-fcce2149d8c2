# VPN Telegram Bot 🤖🔐

[![Python](https://img.shields.io/badge/Python-3.11+-blue.svg)](https://www.python.org/downloads/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://www.docker.com/)
[![Telegram](https://img.shields.io/badge/Telegram-Bot-blue.svg)](https://core.telegram.org/bots)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-15+-blue.svg)](https://www.postgresql.org/)
[![Redis](https://img.shields.io/badge/Redis-7+-red.svg)](https://redis.io/)

A comprehensive Telegram bot for VPN service management with subscription handling, payment processing, and user management capabilities.

## ✨ Features

### 🔐 VPN Management
- **Account Creation**: Automated VPN account provisioning
- **Trial Accounts**: Free trial periods for new users
- **Multiple Protocols**: Support for various VPN protocols
- **Server Selection**: Multiple server locations
- **Usage Monitoring**: Real-time bandwidth and connection tracking

### 💳 Payment System
- **Telegram Payments**: Native Telegram payment integration
- **Multiple Providers**: Support for various payment processors
- **Subscription Plans**: Flexible pricing tiers
- **Invoice Management**: Automated billing and receipts
- **Payment History**: Complete transaction records

### 👥 User Management
- **User Registration**: Seamless onboarding process
- **Profile Management**: User preferences and settings
- **Subscription Tracking**: Active plan monitoring
- **Usage Analytics**: Detailed usage statistics
- **Support System**: Built-in help and support

### 🛡️ Security & Compliance
- **Data Encryption**: End-to-end data protection
- **Access Control**: Role-based permissions
- **Audit Logging**: Comprehensive activity tracking
- **Rate Limiting**: Protection against abuse
- **GDPR Compliance**: Privacy-focused design

### 📊 Admin Features
- **Dashboard**: Comprehensive admin panel
- **User Analytics**: Detailed user insights
- **System Monitoring**: Real-time system health
- **Configuration Management**: Dynamic settings
- **Bulk Operations**: Mass user management

## 🚀 Quick Start

### Prerequisites

- Docker & Docker Compose
- Node.js 18+ (for development)
- Python 3.12+ (for development)

### Production Setup (Recommended)

1. **Clone and configure**:
```bash
git clone <repository-url>
cd vpn-telegram-bot
cp .env.example .env
# Edit .env with your configuration
```

2. **Start with one command**:
```bash
.\start.ps1 up -Build
```

3. **Access your services**:
- Admin Panel: http://localhost:3000
- Admin API: http://localhost:8000

### Development Setup

1. **Setup development environment**:
```bash
.\dev.ps1 setup
```

2. **Start services individually**:
```bash
# Start database services
.\start.ps1 up

# Start admin panel (dev mode)
.\dev.ps1 admin

# Start bot (dev mode)
.\dev.ps1 bot
```

## 🛠️ Management Scripts

### Production Management
```bash
.\start.ps1 up [-Build]     # Start all services
.\start.ps1 down           # Stop all services
.\start.ps1 restart        # Restart services
.\start.ps1 logs           # View logs
.\start.ps1 status         # Check service status
.\start.ps1 build          # Rebuild images
```

### Development Tools
```bash
.\dev.ps1 setup     # Setup development environment
.\dev.ps1 admin     # Start admin panel (dev mode)
.\dev.ps1 bot       # Start bot (dev mode)
.\dev.ps1 test      # Run tests
.\dev.ps1 clean     # Clean environment
.\dev.ps1 install   # Update dependencies
```

## 🏗️ Architecture

### Simplified Docker Setup
- **vpn-app**: Combined bot, celery worker, and admin API
- **admin-panel**: React TypeScript frontend with React Query
- **mysql**: Database with health checks
- **redis**: Cache and message broker

### Tech Stack
- **Backend**: Python 3.12, FastAPI, Celery
- **Frontend**: React 18, TypeScript, React Query
- **Database**: MySQL 8.0
- **Cache**: Redis 7.4
- **Package Manager**: pnpm (frontend)

## ⚙️ Configuration

Key environment variables in `.env`:

```env
# Bot Configuration
BOT_TOKEN=your_telegram_bot_token
ADMIN_CHAT_ID=your_telegram_chat_id

# Database
MYSQL_ROOT_PASSWORD=secure_password
MYSQL_DATABASE=telegram_vpn_bot
MYSQL_USER=vpn_user
MYSQL_PASSWORD=secure_password

# Redis
REDIS_URL=redis://redis:6379/0

# API
API_BASE_URL=http://localhost:8000
```

## 📱 Admin Panel Features

- **Dashboard**: Real-time statistics and analytics
- **User Management**: View, search, and manage users
- **VPN Panels**: Configure and monitor VPN servers
- **Premium Plans**: Create and manage subscription plans
- **Settings**: Bot configuration and preferences
- **TypeScript**: Full type safety and better DX
- **React Query**: Optimized data fetching and caching

## 🤖 Bot Commands

- `/start` - Welcome message and registration
- `/help` - Show available commands
- `/profile` - View user profile and subscription
- `/plans` - Browse premium plans
- `/support` - Contact support

## 🔧 Development

### Code Structure
```
├── bot/                 # Telegram bot logic
├── admin/              # FastAPI admin API
├── admin-panel/        # React TypeScript frontend
│   ├── src/
│   │   ├── components/ # React components (TypeScript)
│   │   ├── services/   # API services with React Query
│   │   └── types/      # TypeScript type definitions
├── start.ps1          # Production management script
├── dev.ps1           # Development utilities
└── docker-compose.yml # Simplified Docker setup
```

### Contributing

1. Use TypeScript for all new frontend code
2. Follow React Query patterns for data fetching
3. Use pnpm for package management
4. Test changes with `./dev.ps1 test`
5. Clean environment with `./dev.ps1 clean`

## 📊 Monitoring

- **Health Checks**: Built-in Docker health monitoring
- **Logs**: Centralized logging with `./start.ps1 logs`
- **Status**: Service status with `./start.ps1 status`

## 🚀 Deployment

1. **Production deployment**:
```bash
.\start.ps1 up -Build
```

2. **Update deployment**:
```bash
.\start.ps1 build
.\start.ps1 restart
```

## 📄 License

MIT License - see LICENSE file for details