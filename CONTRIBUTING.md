# Contributing to VPN Telegram Bot

Thank you for your interest in contributing to the VPN Telegram Bot project! This guide will help you understand how to contribute effectively.

## Table of Contents

1. [Code of Conduct](#code-of-conduct)
2. [Getting Started](#getting-started)
3. [Development Setup](#development-setup)
4. [Contributing Guidelines](#contributing-guidelines)
5. [Pull Request Process](#pull-request-process)
6. [Coding Standards](#coding-standards)
7. [Testing Guidelines](#testing-guidelines)
8. [Documentation](#documentation)
9. [Issue Reporting](#issue-reporting)
10. [Community](#community)

## Code of Conduct

This project and everyone participating in it is governed by our Code of Conduct. By participating, you are expected to uphold this code.

### Our Pledge

We pledge to make participation in our project a harassment-free experience for everyone, regardless of:
- Age, body size, disability, ethnicity, gender identity and expression
- Level of experience, nationality, personal appearance, race, religion
- Sexual identity and orientation

### Our Standards

**Positive behavior includes:**
- Using welcoming and inclusive language
- Being respectful of differing viewpoints and experiences
- Gracefully accepting constructive criticism
- Focusing on what is best for the community
- Showing empathy towards other community members

**Unacceptable behavior includes:**
- Trolling, insulting/derogatory comments, and personal or political attacks
- Public or private harassment
- Publishing others' private information without explicit permission
- Other conduct which could reasonably be considered inappropriate

### Enforcement

Instances of abusive, harassing, or otherwise unacceptable behavior may be reported by contacting the project team at [<EMAIL>]. All complaints will be reviewed and investigated promptly and fairly.

## Getting Started

### Ways to Contribute

There are many ways to contribute to this project:

1. **🐛 Bug Reports**: Help us identify and fix issues
2. **💡 Feature Requests**: Suggest new functionality
3. **💻 Code Contributions**: Submit bug fixes and new features
4. **📚 Documentation**: Improve guides, examples, and API docs
5. **🧪 Testing**: Help with quality assurance and test coverage
6. **🎨 Design**: Improve UI/UX and user experience
7. **🌍 Translation**: Help translate the bot to other languages
8. **📢 Community**: Help others in discussions and support

### First-Time Contributors

New to open source? Here are some good first issues:
- Look for issues labeled `good first issue`
- Documentation improvements
- Adding tests for existing functionality
- Fixing typos or improving error messages
- Adding examples or tutorials

## Development Setup

### Prerequisites

Before you begin, ensure you have:
- **Python 3.11+** installed
- **Git** for version control
- **Docker** and **Docker Compose** (recommended)
- **PostgreSQL** and **Redis** (if not using Docker)
- **Code editor** (VS Code, PyCharm, etc.)

### Fork and Clone

1. **Fork the repository** on GitHub
2. **Clone your fork** locally:
   ```bash
   git clone https://github.com/YOUR_USERNAME/vpn-telegram-bot.git
   cd vpn-telegram-bot
   ```

3. **Add upstream remote**:
   ```bash
   git remote add upstream https://github.com/ORIGINAL_OWNER/vpn-telegram-bot.git
   ```

### Environment Setup

1. **Create virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   pip install -r requirements-dev.txt
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start services with Docker**:
   ```bash
   docker-compose up -d postgres redis
   ```

5. **Run database migrations**:
   ```bash
   python -m alembic upgrade head
   ```

6. **Verify setup**:
   ```bash
   python -m pytest tests/
   python main.py --check-config
   ```

### Development Tools

Install recommended development tools:

```bash
# Code formatting and linting
pip install black isort flake8 mypy

# Pre-commit hooks
pip install pre-commit
pre-commit install

# Testing tools
pip install pytest pytest-asyncio pytest-cov pytest-mock
```

## Contributing Guidelines

### Branch Strategy

We use a simplified Git flow:

- **`main`**: Production-ready code
- **`develop`**: Integration branch for features
- **`feature/feature-name`**: New features
- **`bugfix/bug-description`**: Bug fixes
- **`hotfix/critical-fix`**: Critical production fixes

### Creating a Branch

```bash
# Update your local main branch
git checkout main
git pull upstream main

# Create and switch to a new feature branch
git checkout -b feature/your-feature-name

# Or for bug fixes
git checkout -b bugfix/issue-description
```

### Making Changes

1. **Keep changes focused**: One feature or bug fix per pull request
2. **Write clear commit messages**: Follow conventional commit format
3. **Add tests**: Ensure your changes are tested
4. **Update documentation**: Keep docs in sync with code changes
5. **Follow coding standards**: Use provided linting tools

### Commit Message Format

We follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
type(scope): description

[optional body]

[optional footer]
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

**Examples:**
```bash
feat(auth): add JWT token refresh mechanism
fix(payments): resolve payment webhook timeout issue
docs(api): update API documentation for new endpoints
test(users): add unit tests for user service
```

## Pull Request Process

### Before Submitting

1. **Ensure tests pass**:
   ```bash
   python -m pytest tests/
   ```

2. **Check code quality**:
   ```bash
   black .
   isort .
   flake8 .
   mypy .
   ```

3. **Update documentation**:
   - Update relevant documentation files
   - Add docstrings to new functions/classes
   - Update API documentation if needed

4. **Test your changes**:
   - Manual testing in development environment
   - Verify existing functionality still works
   - Test edge cases and error conditions

### Submitting Pull Request

1. **Push your branch**:
   ```bash
   git push origin feature/your-feature-name
   ```

2. **Create pull request** on GitHub with:
   - **Clear title**: Descriptive and concise
   - **Detailed description**: What, why, and how
   - **Link related issues**: Use "Fixes #123" or "Closes #123"
   - **Screenshots**: For UI changes
   - **Testing notes**: How to test the changes

### Pull Request Template

```markdown
## Description
Brief description of changes and motivation.

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed
- [ ] New tests added for new functionality

## Checklist
- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Code is commented, particularly in hard-to-understand areas
- [ ] Documentation updated
- [ ] No new warnings introduced

## Related Issues
Fixes #(issue number)

## Screenshots (if applicable)
[Add screenshots here]

## Additional Notes
[Any additional information]
```

### Review Process

1. **Automated checks**: CI/CD pipeline runs tests and quality checks
2. **Code review**: Maintainers review code for quality and design
3. **Feedback**: Address any requested changes
4. **Approval**: At least one maintainer approval required
5. **Merge**: Maintainer merges the pull request

## Coding Standards

### Python Style Guide

We follow [PEP 8](https://pep8.org/) with some modifications:

- **Line length**: 88 characters (Black default)
- **Indentation**: 4 spaces
- **Quotes**: Double quotes for strings
- **Imports**: Organized with isort

### Code Formatting

Use automated tools for consistent formatting:

```bash
# Format code
black .

# Sort imports
isort .

# Check style
flake8 .

# Type checking
mypy .
```

### Naming Conventions

```python
# Variables and functions: snake_case
user_id = 123
def get_user_data():
    pass

# Classes: PascalCase
class UserService:
    pass

# Constants: UPPER_SNAKE_CASE
MAX_RETRY_ATTEMPTS = 3

# Private methods: _leading_underscore
def _internal_method():
    pass
```

### Type Hints

Use type hints for all function signatures:

```python
from typing import Optional, List, Dict, Any

def process_user_data(
    user_id: int,
    data: Dict[str, Any],
    options: Optional[List[str]] = None
) -> bool:
    """Process user data with optional configuration.
    
    Args:
        user_id: The user identifier
        data: User data dictionary
        options: Optional processing options
        
    Returns:
        True if processing successful, False otherwise
        
    Raises:
        ValueError: If user_id is invalid
        ProcessingError: If data processing fails
    """
    pass
```

### Documentation Strings

Use Google-style docstrings:

```python
def calculate_subscription_cost(
    plan: str,
    duration: int,
    discount: float = 0.0
) -> float:
    """Calculate the cost of a subscription plan.
    
    This function calculates the total cost for a subscription
    based on the plan type, duration, and any applicable discounts.
    
    Args:
        plan: The subscription plan name ('basic', 'premium', 'enterprise')
        duration: Duration in months (1-12)
        discount: Discount percentage as decimal (0.0-1.0)
        
    Returns:
        The calculated subscription cost in USD
        
    Raises:
        ValueError: If plan is not recognized or duration is invalid
        
    Example:
        >>> calculate_subscription_cost('premium', 6, 0.1)
        89.94
    """
    pass
```

## Testing Guidelines

### Test Structure

Organize tests in the `tests/` directory:

```
tests/
├── unit/
│   ├── test_services/
│   ├── test_handlers/
│   └── test_utils/
├── integration/
│   ├── test_database/
│   ├── test_api/
│   └── test_payments/
├── e2e/
│   └── test_user_journeys/
└── fixtures/
    ├── conftest.py
    └── factories.py
```

### Writing Tests

```python
import pytest
from unittest.mock import Mock, patch
from bot.services.user import UserService
from tests.fixtures.factories import UserFactory

class TestUserService:
    """Test cases for UserService."""
    
    @pytest.fixture
    def user_service(self, mock_db):
        """Create UserService instance with mocked database."""
        return UserService(mock_db)
    
    @pytest.fixture
    def sample_user(self):
        """Create sample user for testing."""
        return UserFactory.build()
    
    async def test_create_user_success(self, user_service, sample_user):
        """Test successful user creation."""
        # Arrange
        user_data = {
            'user_id': sample_user.user_id,
            'username': sample_user.username,
            'first_name': sample_user.first_name
        }
        
        # Act
        result = await user_service.create_user(user_data)
        
        # Assert
        assert result is not None
        assert result.user_id == sample_user.user_id
        assert result.username == sample_user.username
    
    async def test_create_user_duplicate_error(self, user_service):
        """Test user creation with duplicate user_id."""
        # Arrange
        user_data = {'user_id': 123, 'username': 'test'}
        
        # Mock database to raise integrity error
        with patch.object(user_service.db, 'execute') as mock_execute:
            mock_execute.side_effect = IntegrityError("Duplicate key")
            
            # Act & Assert
            with pytest.raises(UserAlreadyExistsError):
                await user_service.create_user(user_data)
    
    @pytest.mark.parametrize("user_id,expected", [
        (123, True),
        (456, False),
        (None, False)
    ])
    async def test_user_exists(self, user_service, user_id, expected):
        """Test user existence check with various inputs."""
        # Arrange
        with patch.object(user_service, 'get_user') as mock_get:
            mock_get.return_value = Mock() if expected else None
            
            # Act
            result = await user_service.user_exists(user_id)
            
            # Assert
            assert result == expected
```

### Test Coverage

Maintain high test coverage:

```bash
# Run tests with coverage
pytest --cov=bot tests/

# Generate HTML coverage report
pytest --cov=bot --cov-report=html tests/

# Check coverage requirements
pytest --cov=bot --cov-fail-under=80 tests/
```

### Testing Best Practices

1. **Test naming**: Use descriptive test names that explain what is being tested
2. **AAA pattern**: Arrange, Act, Assert structure
3. **One assertion per test**: Focus on testing one thing at a time
4. **Use fixtures**: Share common test setup
5. **Mock external dependencies**: Database, APIs, file system
6. **Test edge cases**: Error conditions, boundary values
7. **Parametrized tests**: Test multiple inputs efficiently

## Documentation

### Types of Documentation

1. **Code documentation**: Docstrings and inline comments
2. **API documentation**: Endpoint descriptions and examples
3. **User guides**: How-to guides and tutorials
4. **Developer guides**: Architecture and development setup
5. **README files**: Project overview and quick start

### Documentation Standards

- **Clear and concise**: Easy to understand
- **Up-to-date**: Keep in sync with code changes
- **Examples**: Include practical examples
- **Searchable**: Use clear headings and structure
- **Accessible**: Consider different skill levels

### Writing Guidelines

1. **Use active voice**: "The bot processes payments" vs "Payments are processed"
2. **Be specific**: Provide exact steps and examples
3. **Include context**: Explain why, not just how
4. **Use consistent terminology**: Maintain a glossary
5. **Add screenshots**: Visual aids for UI elements

## Issue Reporting

### Before Creating an Issue

1. **Search existing issues**: Check if already reported
2. **Check documentation**: Ensure it's not a usage question
3. **Test with latest version**: Verify issue exists in current version
4. **Gather information**: Collect relevant details

### Bug Report Template

```markdown
**Bug Description**
A clear and concise description of the bug.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected Behavior**
A clear description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Environment:**
- OS: [e.g. Ubuntu 20.04]
- Python Version: [e.g. 3.11.0]
- Bot Version: [e.g. 1.0.0]
- Database: [e.g. PostgreSQL 15]

**Additional Context**
Add any other context about the problem here.

**Logs**
```
Paste relevant log entries here
```
```

### Feature Request Template

```markdown
**Is your feature request related to a problem?**
A clear description of what the problem is. Ex. I'm always frustrated when [...]

**Describe the solution you'd like**
A clear description of what you want to happen.

**Describe alternatives you've considered**
A clear description of any alternative solutions or features you've considered.

**Additional context**
Add any other context or screenshots about the feature request here.

**Implementation Ideas**
If you have ideas about how this could be implemented, please share them.
```

## Community

### Communication Channels

- **GitHub Issues**: Bug reports and feature requests
- **GitHub Discussions**: General questions and community discussions
- **Telegram Group**: Real-time chat and support
- **Email**: Direct contact for sensitive issues

### Getting Help

1. **Check documentation**: Start with guides and FAQ
2. **Search issues**: Look for similar problems
3. **Ask in discussions**: Community questions
4. **Join Telegram group**: Real-time help
5. **Create an issue**: For bugs or feature requests

### Helping Others

- **Answer questions**: Help in discussions and issues
- **Review pull requests**: Provide constructive feedback
- **Improve documentation**: Fix errors and add examples
- **Share knowledge**: Write tutorials and guides
- **Mentor newcomers**: Help first-time contributors

## Recognition

We appreciate all contributions! Contributors are recognized through:

- **Contributors list**: Listed in README and documentation
- **Release notes**: Mentioned in changelog
- **GitHub profile**: Contribution activity visible
- **Community recognition**: Highlighted in discussions

## Questions?

If you have questions about contributing:

- **Read the documentation**: [docs/guides/](docs/guides/)
- **Check existing issues**: [GitHub Issues](https://github.com/your-repo/issues)
- **Join our community**: [Telegram Group](https://t.me/your-group)
- **Contact maintainers**: [Email](mailto:<EMAIL>)

Thank you for contributing to the VPN Telegram Bot project! 🚀