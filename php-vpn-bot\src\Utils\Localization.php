<?php

declare(strict_types=1);

namespace VpnBot\Utils;

class Localization
{
    private static ?Localization $instance = null;
    private array $translations = [];
    private string $defaultLanguage = 'en';

    private function __construct()
    {
        $this->loadTranslations();
    }

    public static function getInstance(): Localization
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function loadTranslations(): void
    {
        $this->translations = [
            'en' => [
                'welcome' => [
                    'title' => '🎉 Welcome {name}!',
                    'description' => 'Welcome to our VPN service! Choose an option below to get started.',
                ],
                'buttons' => [
                    'trial_vpn' => '🆓 Free Trial',
                    'premium' => '💎 Premium Plans',
                    'dashboard' => '📊 Dashboard',
                    'my_accounts' => '👤 My Accounts',
                    'referral' => '🎁 Referral Program',
                    'settings' => '⚙️ Settings',
                    'help' => '❓ Help',
                    'support' => '🆘 Support',
                    'back' => '⬅️ Back',
                    'main_menu' => '🏠 Main Menu',
                    'change_language' => '🌐 Change Language',
                    'check_subscription' => '✅ Check Subscription',
                ],
                'menu' => [
                    'title' => '📋 Main Menu',
                    'select_option' => 'Please select an option:',
                ],
                'trial' => [
                    'success' => '✅ Trial account created successfully!\n\n👤 Username: `{username}`\n📋 Config: `{config}`',
                    'already_used' => '❌ You have already used your free trial.',
                    'not_eligible' => '❌ You are not eligible for a trial: {reason}',
                    'subscription_required' => '📢 You need to subscribe to our channels first!',
                    'error' => '❌ Failed to create trial account. Please try again later.',
                ],
                'premium' => [
                    'title' => '💎 Premium Plans',
                    'description' => 'Choose a premium plan that suits your needs:',
                    'packages' => [
                        'basic' => [
                            'name' => '1 Month Premium',
                            'price' => '$9.99',
                            'features' => '• 100GB Data\n• 30 Days\n• High Speed\n• 24/7 Support',
                        ],
                        'standard' => [
                            'name' => '3 Months Premium',
                            'price' => '$24.99',
                            'features' => '• 300GB Data\n• 90 Days\n• High Speed\n• 24/7 Support',
                        ],
                        'premium' => [
                            'name' => '1 Year Premium',
                            'price' => '$79.99',
                            'features' => '• Unlimited Data\n• 365 Days\n• Ultra High Speed\n• Priority Support',
                        ],
                    ],
                ],
                'payment' => [
                    'select_method' => 'Select payment method:',
                    'stars' => '⭐ Telegram Stars',
                    'crypto' => '₿ Cryptocurrency',
                    'ton' => '💎 TON',
                    'card' => '💳 Credit Card',
                ],
                'settings' => [
                    'title' => '⚙️ Settings',
                    'language_selection' => 'Select your language:',
                    'language_changed' => '✅ Language changed successfully!',
                ],
                'errors' => [
                    'general_error' => '❌ An error occurred. Please try again.',
                    'auth_required' => '❌ Authentication required.',
                    'unknown_command' => '❌ Unknown command. Please use the menu.',
                ],
                'languages' => [
                    'en' => '🇺🇸 English',
                    'fa' => '🇮🇷 فارسی',
                    'ru' => '🇷🇺 Русский',
                    'zh' => '🇨🇳 中文',
                ],
            ],
            'fa' => [
                'welcome' => [
                    'title' => '🎉 خوش آمدید {name}!',
                    'description' => 'به سرویس VPN ما خوش آمدید! یکی از گزینه‌های زیر را انتخاب کنید.',
                ],
                'buttons' => [
                    'trial_vpn' => '🆓 آزمایش رایگان',
                    'premium' => '💎 پلن‌های پریمیوم',
                    'dashboard' => '📊 داشبورد',
                    'my_accounts' => '👤 اکانت‌های من',
                    'referral' => '🎁 برنامه معرفی',
                    'settings' => '⚙️ تنظیمات',
                    'help' => '❓ راهنما',
                    'support' => '🆘 پشتیبانی',
                    'back' => '⬅️ بازگشت',
                    'main_menu' => '🏠 منوی اصلی',
                    'change_language' => '🌐 تغییر زبان',
                    'check_subscription' => '✅ بررسی عضویت',
                ],
                'menu' => [
                    'title' => '📋 منوی اصلی',
                    'select_option' => 'لطفاً یک گزینه انتخاب کنید:',
                ],
                'trial' => [
                    'success' => '✅ اکانت آزمایشی با موفقیت ایجاد شد!\n\n👤 نام کاربری: `{username}`\n📋 کانفیگ: `{config}`',
                    'already_used' => '❌ شما قبلاً از آزمایش رایگان استفاده کرده‌اید.',
                    'not_eligible' => '❌ شما واجد شرایط آزمایش نیستید: {reason}',
                    'subscription_required' => '📢 ابتدا باید در کانال‌های ما عضو شوید!',
                    'error' => '❌ ایجاد اکانت آزمایشی ناموفق بود. لطفاً بعداً تلاش کنید.',
                ],
                'premium' => [
                    'title' => '💎 پلن‌های پریمیوم',
                    'description' => 'پلن پریمیومی که مناسب نیازتان است را انتخاب کنید:',
                ],
                'settings' => [
                    'title' => '⚙️ تنظیمات',
                    'language_selection' => 'زبان خود را انتخاب کنید:',
                    'language_changed' => '✅ زبان با موفقیت تغییر کرد!',
                ],
                'errors' => [
                    'general_error' => '❌ خطایی رخ داد. لطفاً دوباره تلاش کنید.',
                    'auth_required' => '❌ احراز هویت مورد نیاز است.',
                    'unknown_command' => '❌ دستور نامشخص. لطفاً از منو استفاده کنید.',
                ],
            ],
            'ru' => [
                'welcome' => [
                    'title' => '🎉 Добро пожаловать, {name}!',
                    'description' => 'Добро пожаловать в наш VPN сервис! Выберите опцию ниже, чтобы начать.',
                ],
                'buttons' => [
                    'trial_vpn' => '🆓 Бесплатная пробная версия',
                    'premium' => '💎 Премиум планы',
                    'dashboard' => '📊 Панель управления',
                    'my_accounts' => '👤 Мои аккаунты',
                    'referral' => '🎁 Реферальная программа',
                    'settings' => '⚙️ Настройки',
                    'help' => '❓ Помощь',
                    'support' => '🆘 Поддержка',
                    'back' => '⬅️ Назад',
                    'main_menu' => '🏠 Главное меню',
                    'change_language' => '🌐 Изменить язык',
                    'check_subscription' => '✅ Проверить подписку',
                ],
                'menu' => [
                    'title' => '📋 Главное меню',
                    'select_option' => 'Пожалуйста, выберите опцию:',
                ],
                'settings' => [
                    'title' => '⚙️ Настройки',
                    'language_selection' => 'Выберите ваш язык:',
                    'language_changed' => '✅ Язык успешно изменен!',
                ],
                'errors' => [
                    'general_error' => '❌ Произошла ошибка. Пожалуйста, попробуйте снова.',
                    'auth_required' => '❌ Требуется аутентификация.',
                    'unknown_command' => '❌ Неизвестная команда. Пожалуйста, используйте меню.',
                ],
            ],
            'zh' => [
                'welcome' => [
                    'title' => '🎉 欢迎 {name}！',
                    'description' => '欢迎使用我们的VPN服务！请选择下面的选项开始使用。',
                ],
                'buttons' => [
                    'trial_vpn' => '🆓 免费试用',
                    'premium' => '💎 高级套餐',
                    'dashboard' => '📊 仪表板',
                    'my_accounts' => '👤 我的账户',
                    'referral' => '🎁 推荐计划',
                    'settings' => '⚙️ 设置',
                    'help' => '❓ 帮助',
                    'support' => '🆘 支持',
                    'back' => '⬅️ 返回',
                    'main_menu' => '🏠 主菜单',
                    'change_language' => '🌐 更改语言',
                    'check_subscription' => '✅ 检查订阅',
                ],
                'menu' => [
                    'title' => '📋 主菜单',
                    'select_option' => '请选择一个选项：',
                ],
                'settings' => [
                    'title' => '⚙️ 设置',
                    'language_selection' => '选择您的语言：',
                    'language_changed' => '✅ 语言更改成功！',
                ],
                'errors' => [
                    'general_error' => '❌ 发生错误。请重试。',
                    'auth_required' => '❌ 需要身份验证。',
                    'unknown_command' => '❌ 未知命令。请使用菜单。',
                ],
            ],
        ];
    }

    public function get(string $key, string $language = 'en', array $params = []): string
    {
        $language = $this->getSupportedLanguage($language);
        $keys = explode('.', $key);
        $value = $this->translations[$language] ?? $this->translations[$this->defaultLanguage];

        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                // Fallback to default language
                $value = $this->translations[$this->defaultLanguage];
                foreach ($keys as $fallbackKey) {
                    if (!isset($value[$fallbackKey])) {
                        return $key; // Return key if not found
                    }
                    $value = $value[$fallbackKey];
                }
                break;
            }
            $value = $value[$k];
        }

        if (is_string($value) && !empty($params)) {
            foreach ($params as $param => $replacement) {
                $value = str_replace('{' . $param . '}', (string)$replacement, $value);
            }
        }

        return is_string($value) ? $value : $key;
    }

    private function getSupportedLanguage(string $language): string
    {
        return isset($this->translations[$language]) ? $language : $this->defaultLanguage;
    }

    public function getSupportedLanguages(): array
    {
        return array_keys($this->translations);
    }
}
