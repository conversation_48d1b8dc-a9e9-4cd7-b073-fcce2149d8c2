﻿"""Error handlers for the bot."""

import logging
import traceback
from typing import Optional
from telegram import Update
from telegram.ext import ContextTypes
from telegram.error import (
    BadRequest,
    Forbidden,
    NetworkError,
    RetryAfter,
    TimedOut,
    ChatMigrated,
    Conflict,
    TelegramError
)
from bot.utils.helpers import get_text
from bot.services.auth_service import auth_service

logger = logging.getLogger(__name__)


class ErrorHandlers:
    """Handlers for various types of errors and exceptions."""

    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)

    async def handle_error(
        self, update: Optional[Update], context: ContextTypes.DEFAULT_TYPE
    ) -> None:
        """Main error handler for all unhandled exceptions."""
        try:
            error = context.error
            error_type = type(error).__name__
            error_message = str(error)

            user_id = None
            username = None
            language = 'en'

            if update and update.effective_user:
                user_id = update.effective_user.id
                username = update.effective_user.username
                if hasattr(context, 'user_data') and context.user_data:
                    user_data = context.user_data.get('user', {})
                    language = user_data.get('language_code', 'en')

            self.logger.error(
                "Error occurred: %s: %s\nUser: %s (@%s)\nUpdate: %s\nTraceback: %s",
                error_type, error_message, user_id, username, update, traceback.format_exc()
            )

            if isinstance(error, BadRequest):
                await self._handle_bad_request(update, context, error, language)
            elif isinstance(error, Forbidden):
                await self._handle_forbidden(update, error)
            elif isinstance(error, NetworkError):
                await self._handle_network_error(error)
            elif isinstance(error, RetryAfter):
                await self._handle_retry_after(error)
            elif isinstance(error, TimedOut):
                await self._handle_timeout(update, context, error, language)
            elif isinstance(error, ChatMigrated):
                await self._handle_chat_migrated(error)
            elif isinstance(error, Conflict):
                await self._handle_conflict(error)
            else:
                await self._handle_generic_error(update, context, error, language)

        except (TelegramError, ValueError) as e:
            self.logger.critical("Error in error handler: %s", e, exc_info=True)

    async def _handle_bad_request(
        self, update: Optional[Update], context: ContextTypes.DEFAULT_TYPE,
        error: BadRequest, language: str
    ) -> None:
        """Handle BadRequest errors."""
        try:
            error_message = str(error).lower()

            if "message is not modified" in error_message:
                return
            if "message to edit not found" in error_message:
                return
            if "query is too old" in error_message:
                return
            if "button_data_invalid" in error_message or "invalid button" in error_message:
                await self._send_error_message(
                    update, context,
                    get_text('error.invalid_button', language)
                )
            elif "file_id" in error_message:
                await self._send_error_message(
                    update, context,
                    get_text('error.file_sending_error', language)
                )
            else:
                await self._send_error_message(
                    update, context,
                    get_text('error.invalid_request', language)
                )

        except (TelegramError, ValueError) as e:
            self.logger.error("Error handling BadRequest: %s", e, exc_info=True)

    async def _handle_forbidden(self, update: Optional[Update], error: Forbidden) -> None:
        """Handle Forbidden errors (user blocked bot, etc.)."""
        try:
            if update and update.effective_user:
                user_id = update.effective_user.id
                try:
                    await auth_service.update_user_status(user_id, False)
                    self.logger.info("Marked user %s as inactive due to Forbidden error", user_id)
                except (TelegramError, ValueError) as e:
                    self.logger.error("Error updating user status: %s", e, exc_info=True)

            self.logger.warning("Forbidden error: %s", error)

        except (TelegramError, ValueError) as e:
            self.logger.error("Error handling Forbidden: %s", e, exc_info=True)

    async def _handle_network_error(self, error: NetworkError) -> None:
        """Handle network-related errors."""
        try:
            self.logger.warning("Network error: %s", error)

        except (TelegramError, ValueError) as e:
            self.logger.error("Error handling NetworkError: %s", e, exc_info=True)

    async def _handle_retry_after(self, error: RetryAfter) -> None:
        """Handle rate limiting errors."""
        try:
            retry_after = error.retry_after
            self.logger.warning("Rate limited, retry after %s seconds", retry_after)

        except (TelegramError, ValueError) as e:
            self.logger.error("Error handling RetryAfter: %s", e, exc_info=True)

    async def _handle_timeout(
        self, update: Optional[Update], context: ContextTypes.DEFAULT_TYPE,
        error: TimedOut, language: str
    ) -> None:
        """Handle timeout errors."""
        try:
            self.logger.warning("Request timed out: %s", error)

            await self._send_error_message(
                update, context,
                get_text('error.request_timed_out', language)
            )

        except (TelegramError, ValueError) as e:
            self.logger.error("Error handling TimedOut: %s", e, exc_info=True)

    async def _handle_chat_migrated(self, error: ChatMigrated) -> None:
        """Handle chat migration errors."""
        try:
            new_chat_id = error.new_chat_id
            self.logger.info("Chat migrated to new ID: %s", new_chat_id)

        except (TelegramError, ValueError) as e:
            self.logger.error("Error handling ChatMigrated: %s", e, exc_info=True)

    async def _handle_conflict(self, error: Conflict) -> None:
        """Handle conflict errors (multiple bot instances)."""
        try:
            self.logger.critical("Conflict error - multiple bot instances: %s", error)

        except (TelegramError, ValueError) as e:
            self.logger.error("Error handling Conflict: %s", e, exc_info=True)

    async def _handle_generic_error(
        self, update: Optional[Update], context: ContextTypes.DEFAULT_TYPE,
        error: Exception, language: str
    ) -> None:
        """Handle generic/unknown errors."""
        try:
            self.logger.error("Unhandled error: %s: %s", type(error).__name__, error)

            await self._send_error_message(
                update, context,
                get_text('error.unexpected_error', language)
            )

        except (TelegramError, ValueError) as e:
            self.logger.error("Error handling generic error: %s", e, exc_info=True)

    async def _send_error_message(
        self, update: Optional[Update], context: ContextTypes.DEFAULT_TYPE,
        message: str
    ) -> None:
        """Send error message to user if possible."""
        try:
            if not update:
                return

            if update.message:
                await update.message.reply_text(message)
            elif update.callback_query:
                try:
                    await update.callback_query.message.reply_text(message)
                except BadRequest:
                    await update.callback_query.answer(message, show_alert=True)
            elif update.effective_chat:
                await context.bot.send_message(
                    chat_id=update.effective_chat.id,
                    text=message
                )

        except (TelegramError, ValueError) as e:
            self.logger.error("Error sending error message: %s", e, exc_info=True)

    async def handle_database_error(
        self, error: Exception, operation: str, user_id: Optional[int] = None
    ) -> None:
        """Handle database-related errors."""
        try:
            self.logger.error(
                "Database error in %s: %s: %s\nUser: %s\nTraceback: %s",
                operation, type(error).__name__, error, user_id, traceback.format_exc()
            )

        except (TelegramError, ValueError) as e:
            self.logger.critical("Error in database error handler: %s", e, exc_info=True)

    async def handle_api_error(
        self, error: Exception, api_name: str, user_id: Optional[int] = None
    ) -> None:
        """Handle external API errors (Marzban, payment providers, etc.)."""
        try:
            self.logger.error(
                "API error in %s: %s: %s\nUser: %s\nTraceback: %s",
                api_name, type(error).__name__, error, user_id, traceback.format_exc()
            )

        except (TelegramError, ValueError) as e:
            self.logger.critical("Error in API error handler: %s", e, exc_info=True)

    async def handle_validation_error(
        self, error: Exception, data: dict, user_id: Optional[int] = None
    ) -> None:
        """Handle data validation errors."""
        try:
            self.logger.warning(
                "Validation error: %s: %s\nData: %s\nUser: %s",
                type(error).__name__, error, data, user_id
            )

        except (TelegramError, ValueError) as e:
            self.logger.error("Error in validation error handler: %s", e, exc_info=True)

    def setup_logging(self, log_level: str = "INFO") -> None:
        """Setup logging configuration."""
        try:
            log_format = (
                "%(asctime)s - %(name)s - %(levelname)s - "
                "%(filename)s:%(lineno)d - %(message)s"
            )

            logging.basicConfig(
                level=getattr(logging, log_level.upper()),
                format=log_format,
                handlers=[
                    logging.StreamHandler(),
                    logging.FileHandler('bot_errors.log', encoding='utf-8')
                ]
            )

            logging.getLogger('telegram').setLevel(logging.WARNING)
            logging.getLogger('httpx').setLevel(logging.WARNING)

            self.logger.info("Logging configured successfully")

        except (ValueError, IOError) as e:
            print(f"Error setting up logging: {e}")


error_handlers = ErrorHandlers()
