"""Channel subscription middleware for the Telegram bot."""

import logging
from typing import Callable, Any, Awaitable, List, Optional
from telegram import Update
from telegram.ext import ContextTypes
from telegram.error import TelegramError
from bot.database import get_db_connection

logger = logging.getLogger(__name__)


class ChannelSubscriptionMiddleware:
    """Middleware for checking channel subscription requirements."""
    
    def __init__(self, exempt_commands: List[str] = None):
        self.exempt_commands = exempt_commands or ['/start', '/help']
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def __call__(
        self,
        update: Update,
        context: ContextTypes.DEFAULT_TYPE,
        next_handler: Callable[[Update, ContextTypes.DEFAULT_TYPE], Awaitable[Any]]
    ) -> Any:
        """Process channel subscription middleware."""
        if not update.effective_user:
            return await next_handler(update, context)
        
        # Skip subscription check for exempt commands
        if update.message and update.message.text:
            command = update.message.text.split()[0].lower()
            if command in self.exempt_commands:
                return await next_handler(update, context)
        
        # Skip for callback queries that are checking subscriptions
        if (update.callback_query and 
            update.callback_query.data and 
            update.callback_query.data.startswith('check_subscriptions')):
            return await next_handler(update, context)
        
        user_id = update.effective_user.id
        
        # Check if user is subscribed to required channels
        missing_channels = await self._check_user_subscriptions(user_id, context)
        
        if missing_channels:
            await self._send_subscription_required_message(update, missing_channels)
            return None
        
        return await next_handler(update, context)
    
    async def _check_user_subscriptions(self, user_id: int, context: ContextTypes.DEFAULT_TYPE) -> List[dict]:
        """Check which required channels the user is not subscribed to."""
        missing_channels = []

        try:
            # Use the enhanced channel service for better subscription verification
            from bot.services.channel_service import ChannelService
            channel_service = ChannelService()

            verification_result = await channel_service.verify_all_subscriptions(
                context.bot, user_id
            )

            return verification_result.get('missing_channels', [])

        except Exception as e:
            self.logger.error(f"Error checking user subscriptions: {e}")
            return []
    
    async def _send_subscription_required_message(
        self, 
        update: Update, 
        missing_channels: List[dict]
    ) -> None:
        """Send message requiring user to subscribe to channels."""
        message_text = (
            "🔒 <b>Channel Subscription Required</b>\n\n"
            "To use this bot, you must subscribe to the following channels:\n\n"
        )
        
        for i, channel in enumerate(missing_channels, 1):
            channel_title = channel['title'] or f"Channel {i}"
            
            # Create join URL
            if channel['invite_link']:
                join_url = channel['invite_link']
            elif channel['username']:
                join_url = f"https://t.me/{channel['username'].lstrip('@')}"
            else:
                join_url = f"https://t.me/c/{str(channel['id']).replace('-100', '')}"
            
            message_text += f"{i}. {channel_title}\n   📢 Join: {join_url}\n\n"
        
        message_text += (
            "✅ After joining all channels, use the 'Check Subscription' button from the main menu to continue.\n\n"
            "💡 You can access the main menu using the reply keyboard buttons below."
        )
        
        try:
            if update.message:
                await update.message.reply_text(
                    message_text,
                    parse_mode='HTML',
                    disable_web_page_preview=True
                )
            elif update.callback_query:
                await update.callback_query.message.reply_text(
                    message_text,
                    parse_mode='HTML',
                    disable_web_page_preview=True
                )
        except TelegramError as e:
            self.logger.error(f"Failed to send subscription required message: {e}")


class SubscriptionVerificationService:
    """Service for verifying and managing channel subscriptions."""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def verify_user_subscriptions(
        self, 
        user_id: int, 
        context: ContextTypes.DEFAULT_TYPE
    ) -> tuple[bool, List[dict]]:
        """Verify if user is subscribed to all required channels."""
        missing_channels = []
        
        try:
            async with get_db_connection() as conn:
                channels = await conn.fetch(
                    "SELECT * FROM channels WHERE is_required = true AND is_active = true"
                )
                
                for channel in channels:
                    channel_id = channel['channel_id']
                    
                    try:
                        member = await context.bot.get_chat_member(
                            chat_id=channel_id, user_id=user_id
                        )
                        
                        if member.status in ['left', 'kicked']:
                            missing_channels.append({
                                'id': channel_id,
                                'username': channel['username'],
                                'title': channel['title'],
                                'invite_link': channel['invite_link']
                            })
                    
                    except TelegramError as e:
                        self.logger.warning(
                            f"Could not verify membership for channel {channel_id}: {e}"
                        )
                        missing_channels.append({
                            'id': channel_id,
                            'username': channel['username'],
                            'title': channel['title'],
                            'invite_link': channel['invite_link']
                        })
        
        except Exception as e:
            self.logger.error(f"Error verifying subscriptions: {e}")
            return False, []
        
        return len(missing_channels) == 0, missing_channels
    
    async def update_user_subscription_status(
        self, 
        user_id: int, 
        channel_id: str, 
        is_subscribed: bool
    ) -> None:
        """Update user's subscription status in database."""
        try:
            async with get_db_connection() as conn:
                if is_subscribed:
                    # Add or update subscription
                    await conn.execute(
                        """
                        INSERT INTO user_channel_subscriptions (user_id, channel_id, subscribed_at)
                        VALUES ($1, $2, NOW())
                        ON CONFLICT (user_id, channel_id) 
                        DO UPDATE SET subscribed_at = NOW(), unsubscribed_at = NULL
                        """,
                        user_id, channel_id
                    )
                else:
                    # Mark as unsubscribed
                    await conn.execute(
                        """
                        UPDATE user_channel_subscriptions 
                        SET unsubscribed_at = NOW()
                        WHERE user_id = $1 AND channel_id = $2
                        """,
                        user_id, channel_id
                    )
        
        except Exception as e:
            self.logger.error(f"Error updating subscription status: {e}")


# Global instances
subscription_middleware = ChannelSubscriptionMiddleware()
subscription_service = SubscriptionVerificationService()