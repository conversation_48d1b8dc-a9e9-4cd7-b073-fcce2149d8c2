"""QR code generation service."""

import logging
import qrcode
import io
import base64
from typing import Optional
from qrcode.image.styledpil import StyledPilImage
from qrcode.image.styles.moduledrawers import RoundedModuleDrawer
from qrcode.image.styles.colormasks import SquareGradiantColorMask
from PIL import Image, ImageDraw, ImageFont

logger = logging.getLogger(__name__)


class QRCodeService:
    """Service for generating QR codes."""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def generate_qr_code(
        self, 
        data: str, 
        size: int = 10,
        border: int = 4,
        styled: bool = True
    ) -> Optional[bytes]:
        """Generate QR code for given data."""
        try:
            # Create QR code instance
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=size,
                border=border,
            )
            
            qr.add_data(data)
            qr.make(fit=True)
            
            if styled:
                # Create styled QR code
                img = qr.make_image(
                    image_factory=StyledPilImage,
                    module_drawer=RoundedModuleDrawer(),
                    color_mask=SquareGradiantColorMask(
                        back_color=(255, 255, 255),
                        center_color=(0, 100, 200),
                        edge_color=(0, 50, 150)
                    )
                )
            else:
                # Create simple QR code
                img = qr.make_image(fill_color="black", back_color="white")
            
            # Convert to bytes
            img_buffer = io.BytesIO()
            img.save(img_buffer, format='PNG')
            img_buffer.seek(0)
            
            return img_buffer.getvalue()
        
        except Exception as e:
            self.logger.error(f"Error generating QR code: {e}")
            return None
    
    async def generate_qr_code_with_logo(
        self, 
        data: str, 
        logo_path: Optional[str] = None,
        size: int = 10,
        border: int = 4
    ) -> Optional[bytes]:
        """Generate QR code with logo in the center."""
        try:
            # Create QR code
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_H,  # High error correction for logo
                box_size=size,
                border=border,
            )
            
            qr.add_data(data)
            qr.make(fit=True)
            
            # Create QR code image
            qr_img = qr.make_image(fill_color="black", back_color="white")
            
            if logo_path:
                try:
                    # Open logo image
                    logo = Image.open(logo_path)
                    
                    # Calculate logo size (10% of QR code)
                    qr_width, qr_height = qr_img.size
                    logo_size = min(qr_width, qr_height) // 10
                    
                    # Resize logo
                    logo = logo.resize((logo_size, logo_size), Image.Resampling.LANCZOS)
                    
                    # Create a white background for logo
                    logo_bg = Image.new('RGB', (logo_size + 20, logo_size + 20), 'white')
                    logo_bg.paste(logo, (10, 10))
                    
                    # Calculate position to center logo
                    logo_pos = (
                        (qr_width - logo_bg.size[0]) // 2,
                        (qr_height - logo_bg.size[1]) // 2
                    )
                    
                    # Paste logo onto QR code
                    qr_img.paste(logo_bg, logo_pos)
                    
                except Exception as logo_error:
                    self.logger.warning(f"Could not add logo: {logo_error}")
            
            # Convert to bytes
            img_buffer = io.BytesIO()
            qr_img.save(img_buffer, format='PNG')
            img_buffer.seek(0)
            
            return img_buffer.getvalue()
        
        except Exception as e:
            self.logger.error(f"Error generating QR code with logo: {e}")
            return None
    
    async def generate_qr_code_with_text(
        self, 
        data: str, 
        title: str = "VPN Configuration",
        subtitle: str = "Scan to connect",
        size: int = 10,
        border: int = 4
    ) -> Optional[bytes]:
        """Generate QR code with text labels."""
        try:
            # Generate basic QR code
            qr_bytes = await self.generate_qr_code(data, size, border, styled=True)
            if not qr_bytes:
                return None
            
            # Open QR code image
            qr_img = Image.open(io.BytesIO(qr_bytes))
            qr_width, qr_height = qr_img.size
            
            # Create new image with space for text
            text_height = 100
            total_height = qr_height + text_height
            
            final_img = Image.new('RGB', (qr_width, total_height), 'white')
            
            # Paste QR code
            final_img.paste(qr_img, (0, 0))
            
            # Add text
            draw = ImageDraw.Draw(final_img)
            
            try:
                # Try to use a nice font
                title_font = ImageFont.truetype("arial.ttf", 24)
                subtitle_font = ImageFont.truetype("arial.ttf", 16)
            except:
                # Fallback to default font
                title_font = ImageFont.load_default()
                subtitle_font = ImageFont.load_default()
            
            # Calculate text positions
            title_bbox = draw.textbbox((0, 0), title, font=title_font)
            title_width = title_bbox[2] - title_bbox[0]
            title_x = (qr_width - title_width) // 2
            title_y = qr_height + 10
            
            subtitle_bbox = draw.textbbox((0, 0), subtitle, font=subtitle_font)
            subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]
            subtitle_x = (qr_width - subtitle_width) // 2
            subtitle_y = title_y + 35
            
            # Draw text
            draw.text((title_x, title_y), title, fill='black', font=title_font)
            draw.text((subtitle_x, subtitle_y), subtitle, fill='gray', font=subtitle_font)
            
            # Convert to bytes
            img_buffer = io.BytesIO()
            final_img.save(img_buffer, format='PNG')
            img_buffer.seek(0)
            
            return img_buffer.getvalue()
        
        except Exception as e:
            self.logger.error(f"Error generating QR code with text: {e}")
            return None
    
    async def generate_base64_qr_code(self, data: str) -> Optional[str]:
        """Generate QR code and return as base64 string."""
        try:
            qr_bytes = await self.generate_qr_code(data)
            if qr_bytes:
                return base64.b64encode(qr_bytes).decode('utf-8')
            return None
        
        except Exception as e:
            self.logger.error(f"Error generating base64 QR code: {e}")
            return None
    
    def create_subscription_qr_data(self, subscription_url: str, name: str = "VPN") -> str:
        """Create properly formatted QR data for VPN subscription."""
        # For most VPN clients, the subscription URL is used directly
        return subscription_url
    
    async def generate_subscription_qr(
        self, 
        subscription_url: str, 
        account_name: str = "VPN Account"
    ) -> Optional[bytes]:
        """Generate QR code specifically for VPN subscription."""
        try:
            qr_data = self.create_subscription_qr_data(subscription_url, account_name)
            
            return await self.generate_qr_code_with_text(
                data=qr_data,
                title=account_name,
                subtitle="Scan to add VPN"
            )
        
        except Exception as e:
            self.logger.error(f"Error generating subscription QR: {e}")
            return None


# Global instance
qr_service = QRCodeService()