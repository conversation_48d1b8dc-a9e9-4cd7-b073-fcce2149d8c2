import React, { useState, useMemo } from 'react';
import { useUsers, useResetUserTrial, useBulkResetTrials } from '../services/api';
import { User } from '../types/api';

const Users: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedUsers, setSelectedUsers] = useState<number[]>([]);
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [success, setSuccess] = useState('');
  
  const { data: users = [], isLoading, error, refetch } = useUsers();
  const resetTrialMutation = useResetUserTrial();
  const bulkResetTrialsMutation = useBulkResetTrials();
  
  const filteredUsers = useMemo(() => {
    if (searchQuery.trim() === '') {
      return users;
    }
    return users.filter(user => 
      user.username?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.first_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.last_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.telegram_id?.toString().includes(searchQuery)
    );
  }, [users, searchQuery]);

  const formatDate = (dateString: string | null | undefined): string => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (user: User) => {
    const isActive = user.is_premium || user.data_used > 0;
    return (
      <span style={{ 
        padding: '0.25rem 0.5rem', 
        borderRadius: '4px', 
        fontSize: '0.875rem',
        backgroundColor: isActive ? '#d4edda' : '#f8d7da',
        color: isActive ? '#155724' : '#721c24'
      }}>
        {isActive ? 'Active' : 'Inactive'}
      </span>
    );
  };

  const getTrialBadge = (user: User) => {
    const hasUsedTrial = user.has_used_trial;
    return (
      <span style={{ 
        padding: '0.25rem 0.5rem', 
        borderRadius: '4px', 
        fontSize: '0.875rem',
        backgroundColor: hasUsedTrial ? '#f8d7da' : '#d4edda',
        color: hasUsedTrial ? '#721c24' : '#155724'
      }}>
        {hasUsedTrial ? 'Used' : 'Available'}
      </span>
    );
  };

  const handleSelectUser = (userId: number) => {
    setSelectedUsers(prev => 
      prev.includes(userId) 
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const handleSelectAll = () => {
    if (selectedUsers.length === filteredUsers.length) {
      setSelectedUsers([]);
    } else {
      setSelectedUsers(filteredUsers.map(user => user.id));
    }
  };

  const handleResetTrial = async (userId: number) => {
    try {
      await resetTrialMutation.mutateAsync(userId);
      setSuccess('Trial reset successfully');
      refetch();
      setTimeout(() => setSuccess(''), 3000);
    } catch (err) {
      console.error('Reset trial error:', err);
    }
  };

  const handleBulkResetTrials = async () => {
    if (selectedUsers.length === 0) return;
    
    try {
      await bulkResetTrialsMutation.mutateAsync(selectedUsers);
      setSuccess(`Trials reset for ${selectedUsers.length} users`);
      setSelectedUsers([]);
      setShowBulkActions(false);
      refetch();
      setTimeout(() => setSuccess(''), 3000);
    } catch (err) {
      console.error('Bulk reset trials error:', err);
    }
  };

  const getUserType = (user: User) => {
    if (user.is_premium) {
      return (
        <span style={{ 
          padding: '0.25rem 0.5rem', 
          borderRadius: '4px', 
          fontSize: '0.875rem',
          backgroundColor: '#fff3cd',
          color: '#856404'
        }}>
          Premium
        </span>
      );
    }
    return (
      <span style={{ 
        padding: '0.25rem 0.5rem', 
        borderRadius: '4px', 
        fontSize: '0.875rem',
        backgroundColor: '#e2e3e5',
        color: '#383d41'
      }}>
        Free
      </span>
    );
  };

  if (isLoading) {
    return <div className="loading">Loading users...</div>;
  }

  return (
    <div>
      <div className="page-header">
        <h1>Users</h1>
        <p>View and manage registered users</p>
      </div>

      {error && <div className="error">Failed to load users</div>}
      {success && (
        <div className="alert alert-success" role="alert">
          {success}
        </div>
      )}

      <div className="card">
        <div style={{ marginBottom: '1.5rem' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <h3>User List ({filteredUsers.length} users)</h3>
            <div style={{ display: 'flex', gap: '0.5rem' }}>
              <button
                className="btn btn-secondary"
                onClick={() => setShowBulkActions(!showBulkActions)}
              >
                {showBulkActions ? 'Hide' : 'Show'} Bulk Actions
              </button>
              <button 
                className="btn btn-primary" 
                onClick={() => refetch()}
                style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}
              >
                🔄 Refresh
              </button>
            </div>
          </div>
          
          <div style={{ display: 'flex', gap: '1rem', alignItems: 'center', marginBottom: '1rem' }}>
            <div className="form-group" style={{ maxWidth: '400px', margin: 0 }}>
              <input
                type="text"
                className="form-control"
                placeholder="Search users by name, username, or Telegram ID..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            {showBulkActions && (
              <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'center' }}>
                <button
                  className="btn btn-outline-primary"
                  onClick={handleSelectAll}
                >
                  {selectedUsers.length === filteredUsers.length ? 'Deselect All' : 'Select All'}
                </button>
                {selectedUsers.length > 0 && (
                  <button
                    className="btn btn-warning"
                    onClick={handleBulkResetTrials}
                    disabled={bulkResetTrialsMutation.isPending}
                  >
                    {bulkResetTrialsMutation.isPending ? 'Resetting...' : `Reset Trials (${selectedUsers.length})`}
                  </button>
                )}
              </div>
            )}
          </div>
        </div>

        {filteredUsers.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '2rem', color: '#6c757d' }}>
            {searchQuery ? 'No users found matching your search.' : 'No users registered yet.'}
          </div>
        ) : (
          <div style={{ overflowX: 'auto' }}>
            <table className="table">
              <thead>
                <tr>
                  {showBulkActions && <th>Select</th>}
                  <th>User Info</th>
                  <th>Telegram ID</th>
                  <th>Type</th>
                  <th>Status</th>
                  <th>Trial Status</th>
                  <th>Joined</th>
                  <th>Last Active</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredUsers.map((user) => (
                  <tr key={user.id}>
                    {showBulkActions && (
                      <td>
                        <input
                          type="checkbox"
                          checked={selectedUsers.includes(user.id)}
                          onChange={() => handleSelectUser(user.id)}
                        />
                      </td>
                    )}
                    <td>
                      <div>
                        <strong>
                          {user.first_name} {user.last_name}
                        </strong>
                        {user.username && (
                          <div style={{ fontSize: '0.875rem', color: '#6c757d' }}>
                            @{user.username}
                          </div>
                        )}
                      </div>
                    </td>
                    <td>
                      <code style={{ 
                        backgroundColor: '#f8f9fa', 
                        padding: '0.25rem 0.5rem', 
                        borderRadius: '4px',
                        fontSize: '0.875rem'
                      }}>
                        {user.telegram_id}
                      </code>
                    </td>
                    <td>{getUserType(user)}</td>
                    <td>{getStatusBadge(user)}</td>
                    <td>{getTrialBadge(user)}</td>
                    <td>{formatDate(user.created_at)}</td>
                    <td>{formatDate(user.updated_at)}</td>
                    <td>
                      <button
                        className="btn btn-sm btn-warning"
                        onClick={() => handleResetTrial(user.id)}
                        disabled={resetTrialMutation.isPending}
                        style={{ fontSize: '0.75rem' }}
                      >
                        {resetTrialMutation.isPending ? 'Resetting...' : 'Reset Trial'}
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* User Statistics */}
      <div className="card" style={{ marginTop: '1.5rem' }}>
        <h3 style={{ marginBottom: '1rem' }}>User Statistics</h3>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem' }}>
          <div style={{ textAlign: 'center', padding: '1rem', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
            <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#3498db' }}>
              {users.length}
            </div>
            <div style={{ color: '#6c757d' }}>Total Users</div>
          </div>
          
          <div style={{ textAlign: 'center', padding: '1rem', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
            <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#27ae60' }}>
              {users.filter(u => u.is_premium || u.data_used > 0).length}
            </div>
            <div style={{ color: '#6c757d' }}>Active Users</div>
          </div>
          
          <div style={{ textAlign: 'center', padding: '1rem', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
            <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#f39c12' }}>
              {users.filter(u => u.is_premium).length}
            </div>
            <div style={{ color: '#6c757d' }}>Premium Users</div>
          </div>
          
          <div style={{ textAlign: 'center', padding: '1rem', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
            <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#e74c3c' }}>
              {users.filter(u => !u.is_premium && u.data_used === 0).length}
            </div>
            <div style={{ color: '#6c757d' }}>Inactive Users</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Users;