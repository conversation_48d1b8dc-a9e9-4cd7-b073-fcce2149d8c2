version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: php-vpn-bot
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - ./:/var/www/html
      - ./logs:/var/www/html/logs
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
    depends_on:
      - postgres
      - redis
    networks:
      - vpn-bot-network

  postgres:
    image: postgres:15-alpine
    container_name: vpn-bot-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: telegram_vpn_bot
      POSTGRES_USER: vpn_bot
      POSTGRES_PASSWORD: vpn_bot_pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/migrations:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - vpn-bot-network

  redis:
    image: redis:7-alpine
    container_name: vpn-bot-redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - vpn-bot-network

  nginx:
    image: nginx:alpine
    container_name: vpn-bot-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/default.conf:/etc/nginx/conf.d/default.conf
      - ./public:/var/www/html/public
    depends_on:
      - app
    networks:
      - vpn-bot-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  vpn-bot-network:
    driver: bridge
