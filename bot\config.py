import os
from typing import Optional
from pydantic_settings import BaseSettings
import asyncio
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy.future import select
import logging

logger = logging.getLogger(__name__)

class Settings(BaseSettings):
    # Bot Configuration
    BOT_TOKEN: str = os.getenv("BOT_TOKEN", "")
    ADMIN_USER_ID: int = int(os.getenv("ADMIN_USER_ID", "0"))
    
    # Database Configuration
    POSTGRES_HOST: str = os.getenv("POSTGRES_HOST", "localhost")
    POSTGRES_USER: str = os.getenv("POSTGRES_USER", "vpn_bot")
    POSTGRES_PASSWORD: str = os.getenv("POSTGRES_PASSWORD", "vpn_bot_pass")
    POSTGRES_DATABASE: str = os.getenv("POSTGRES_DATABASE", "telegram_vpn_bot")
    POSTGRES_PORT: int = int(os.getenv("POSTGRES_PORT", "5432"))
    
    # Redis Configuration
    REDIS_HOST: str = os.getenv("REDIS_HOST", "localhost")
    REDIS_PORT: int = int(os.getenv("REDIS_PORT", "6379"))
    REDIS_DB: int = int(os.getenv("REDIS_DB", "0"))
    
    @property
    def REDIS_URL(self) -> str:
        """Get Redis URL for connections."""
        return f"redis://{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
    
    # VPN Configuration (fallback to .env if database not available)
    FREE_DATA_LIMIT: int = int(os.getenv("FREE_DATA_LIMIT", "**********"))  # 1GB
    FREE_DURATION_DAYS: int = int(os.getenv("FREE_DURATION_DAYS", "7"))
    
    # Trial Configuration
    TRIAL_DATA_LIMIT: int = int(os.getenv("TRIAL_DATA_LIMIT", "**********"))  # 1GB
    TRIAL_DURATION_DAYS: int = int(os.getenv("TRIAL_DURATION_DAYS", "1"))
    
    # Payment Configuration (fallback to .env if database not available)
    PAYMENT_PROVIDER_TOKEN: str = os.getenv("PAYMENT_PROVIDER_TOKEN", "")
    
    # NowPayments Configuration
    NOWPAYMENTS_API_KEY: str = os.getenv("NOWPAYMENTS_API_KEY", "")
    NOWPAYMENTS_IPN_SECRET: str = os.getenv("NOWPAYMENTS_IPN_SECRET", "")
    WEBHOOK_URL: str = os.getenv("WEBHOOK_URL", "https://your-domain.com")
    
    # TON Payment Configuration
    TON_MASTER_WALLET: str = os.getenv("TON_MASTER_WALLET", "")
    TON_MASTER_PRIVATE_KEY: str = os.getenv("TON_MASTER_PRIVATE_KEY", "")
    TON_NETWORK: str = os.getenv("TON_NETWORK", "mainnet")  # mainnet or testnet
    TON_API_KEY: str = os.getenv("TON_API_KEY", "")
    TON_PAYMENT_TIMEOUT_MINUTES: int = int(os.getenv("TON_PAYMENT_TIMEOUT_MINUTES", "30"))
    
    # Marzban Configuration
    MARZBAN_URL: str = os.getenv("MARZBAN_URL", "")
    MARZBAN_USERNAME: str = os.getenv("MARZBAN_USERNAME", "")
    MARZBAN_PASSWORD: str = os.getenv("MARZBAN_PASSWORD", "")
    MARZBAN_TOKEN: str = os.getenv("MARZBAN_TOKEN", "")
    MARZBAN_API_TIMEOUT: int = int(os.getenv("MARZBAN_API_TIMEOUT", "30"))
    
    # Channel Configuration - Use ChannelService.get_required_channels() instead
    # REQUIRED_CHANNELS removed - now handled via database through ChannelService
    
    # Database-loaded settings (will be populated from database)
    _db_settings: Optional[dict] = None
    _db_engine = None
    _db_session_factory = None

    class Config:
        env_file = ".env"
        extra = "ignore"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._initialize_db_connection()
    
    def _initialize_db_connection(self):
        """Initialize database connection for settings loading"""
        try:
            database_url = f"postgresql+asyncpg://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_HOST}:{self.POSTGRES_PORT}/{self.POSTGRES_DATABASE}"
            self._db_engine = create_async_engine(database_url, echo=False)
            self._db_session_factory = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self._db_engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
        except Exception as e:
            logger.warning(f"Failed to initialize database connection for settings: {e}")
    
    async def load_from_database(self):
        """Load settings from database and update current instance"""
        if not self._db_session_factory:
            logger.warning("Database connection not available, using .env settings")
            return
        
        try:
            # Import here to avoid circular imports
            from bot.models import BotSettings
            
            async with self._db_session_factory() as session:
                bot_settings = (await session.execute(select(BotSettings))).scalar_one_or_none()
                
                if bot_settings:
                    # Update settings from database
                    self.FREE_DATA_LIMIT = bot_settings.free_data_limit
                    self.FREE_DURATION_DAYS = bot_settings.free_duration_days
                    self.PAYMENT_PROVIDER_TOKEN = bot_settings.payment_provider_token or self.PAYMENT_PROVIDER_TOKEN
                    
                    # Required channels are now handled via database through ChannelService
                    # No longer loading into settings object
                    
                    self._db_settings = {
                        'free_data_limit': bot_settings.free_data_limit,
                        'free_duration_days': bot_settings.free_duration_days,
                        'payment_provider_token': bot_settings.payment_provider_token
                    }
                    
                    logger.info("Settings loaded from database successfully")
                else:
                    logger.warning("No bot settings found in database, using .env settings")
                    
        except Exception as e:
            logger.error(f"Failed to load settings from database: {e}")
            logger.info("Falling back to .env settings")
    

    
    async def refresh_settings(self):
        """Refresh settings from database"""
        await self.load_from_database()
    
    async def close_db_connection(self):
        """Close database connection"""
        if self._db_engine:
            await self._db_engine.dispose()

settings = Settings()