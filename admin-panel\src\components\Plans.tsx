import React, { useState } from 'react';
import { usePlans, useCreatePlan, useUpdatePlan, useDeletePlan } from '../services/api';
import { PremiumPlan, CreatePlanRequest } from '../types/api';

interface PlanFormData {
  name: string;
  price: string;
  data_limit: string;
  data_limit_unit: 'MB' | 'GB';
  duration_days: string;
  is_active: boolean;
}

const Plans: React.FC = () => {
  const [success, setSuccess] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [editingPlan, setEditingPlan] = useState<PremiumPlan | null>(null);
  const [formData, setFormData] = useState<PlanFormData>({
    name: '',
    price: '',
    data_limit: '',
    data_limit_unit: 'GB',
    duration_days: '',
    is_active: true
  });

  const { data: plans = [], isLoading, error } = usePlans();
  const createPlanMutation = useCreatePlan();
  const updatePlanMutation = useUpdatePlan();
  const deletePlanMutation = useDeletePlan();

  const convertToBytes = (value: number, unit: 'MB' | 'GB'): number => {
    if (unit === 'GB') {
      return value * 1024 * 1024 * 1024;
    }
    return value * 1024 * 1024;
  };

  const convertFromBytes = (bytes: number): { value: number; unit: 'MB' | 'GB' } => {
    if (bytes >= 1024 * 1024 * 1024) {
      return { value: bytes / (1024 * 1024 * 1024), unit: 'GB' };
    }
    return { value: bytes / (1024 * 1024), unit: 'MB' };
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const dataLimitInBytes = convertToBytes(parseFloat(formData.data_limit), formData.data_limit_unit);
      const planData: CreatePlanRequest = {
        name: formData.name,
        price: parseFloat(formData.price),
        data_limit: dataLimitInBytes,
        duration_days: parseInt(formData.duration_days),
        is_active: formData.is_active
      };
      
      if (editingPlan) {
        await updatePlanMutation.mutateAsync({ id: editingPlan.id, data: planData });
        setSuccess('Plan updated successfully');
      } else {
        await createPlanMutation.mutateAsync(planData);
        setSuccess('Plan created successfully');
      }
      setShowModal(false);
      setEditingPlan(null);
      resetForm();
    } catch (err) {
      console.error('Plan save error:', err);
    }
  };

  const handleEdit = (plan: PremiumPlan) => {
    setEditingPlan(plan);
    const { value, unit } = convertFromBytes(plan.data_limit);
    setFormData({
      name: plan.name,
      price: plan.price.toString(),
      data_limit: value.toString(),
      data_limit_unit: unit,
      duration_days: plan.duration_days.toString(),
      is_active: plan.is_active
    });
    setShowModal(true);
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this plan?')) {
      try {
        await deletePlanMutation.mutateAsync(id);
        setSuccess('Plan deleted successfully');
      } catch (err) {
        console.error('Plan delete error:', err);
      }
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      price: '',
      data_limit: '',
      data_limit_unit: 'GB',
      duration_days: '',
      is_active: true
    });
  };

  const openCreateModal = () => {
    setEditingPlan(null);
    resetForm();
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setEditingPlan(null);
    resetForm();
  };

  const formatDataLimit = (bytes: number): string => {
    if (bytes >= 1024 * 1024 * 1024) {
      return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} GB`;
    } else if (bytes >= 1024 * 1024) {
      return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
    } else if (bytes >= 1024) {
      return `${(bytes / 1024).toFixed(1)} KB`;
    }
    return `${bytes} B`;
  };

  if (isLoading) {
    return <div className="loading">Loading plans...</div>;
  }

  return (
    <div>
      <div className="page-header">
        <h1>Premium Plans</h1>
        <p>Manage your subscription plans and packages</p>
      </div>

      {error && <div className="error">Failed to load plans</div>}
      {success && <div className="success">{success}</div>}

      <div className="card">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
          <h3>Plans List</h3>
          <button className="btn btn-primary" onClick={openCreateModal}>
            Add New Plan
          </button>
        </div>

        {plans.length === 0 ? (
          <p>No plans available yet. Create your first plan to get started.</p>
        ) : (
          <table className="table">
            <thead>
              <tr>
                <th>Name</th>
                <th>Price</th>
                <th>Data Limit</th>
                <th>Duration</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {plans.map((plan) => (
                <tr key={plan.id}>
                  <td>
                    <strong>{plan.name}</strong>
                  </td>
                  <td>${plan.price}</td>
                  <td>{formatDataLimit(plan.data_limit)}</td>
                  <td>{plan.duration_days} days</td>
                  <td>
                    <span style={{ 
                      padding: '0.25rem 0.5rem', 
                      borderRadius: '4px', 
                      fontSize: '0.875rem',
                      backgroundColor: plan.is_active ? '#d4edda' : '#f8d7da',
                      color: plan.is_active ? '#155724' : '#721c24'
                    }}>
                      {plan.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td>
                    <div className="actions">
                      <button 
                        className="btn btn-warning btn-sm" 
                        onClick={() => handleEdit(plan)}
                      >
                        Edit
                      </button>
                      <button 
                        className="btn btn-danger btn-sm" 
                        onClick={() => handleDelete(plan.id)}
                      >
                        Delete
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>

      {showModal && (
        <div className="modal">
          <div className="modal-content">
            <div className="modal-header">
              <h3>{editingPlan ? 'Edit Plan' : 'Add New Plan'}</h3>
              <button className="close-btn" onClick={closeModal}>&times;</button>
            </div>
            
            <form onSubmit={handleSubmit}>
              <div className="form-group">
                <label>Plan Name</label>
                <input
                  type="text"
                  className="form-control"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  placeholder="e.g., Basic Plan, Premium Plan"
                  required
                />
              </div>
              
              <div className="form-row">
                <div className="form-group">
                  <label>Price ($)</label>
                  <input
                    type="number"
                    step="0.01"
                    className="form-control"
                    value={formData.price}
                    onChange={(e) => setFormData({...formData, price: e.target.value})}
                    placeholder="9.99"
                    required
                  />
                </div>
                
                <div className="form-group">
                  <label>Duration (Days)</label>
                  <input
                    type="number"
                    className="form-control"
                    value={formData.duration_days}
                    onChange={(e) => setFormData({...formData, duration_days: e.target.value})}
                    placeholder="30"
                    required
                  />
                </div>
              </div>
              
              <div className="form-group">
                <label>Data Limit</label>
                <div style={{ display: 'flex', gap: '0.5rem' }}>
                  <input
                    type="number"
                    step="0.1"
                    className="form-control"
                    value={formData.data_limit}
                    onChange={(e) => setFormData({...formData, data_limit: e.target.value})}
                    placeholder="10"
                    required
                    style={{ flex: 2 }}
                  />
                  <select
                    className="form-control"
                    value={formData.data_limit_unit}
                    onChange={(e) => setFormData({...formData, data_limit_unit: e.target.value as 'MB' | 'GB'})}
                    style={{ flex: 1 }}
                  >
                    <option value="MB">MB</option>
                    <option value="GB">GB</option>
                  </select>
                </div>
                <small style={{ color: '#6c757d' }}>Enter data limit and select unit (MB or GB)</small>
              </div>
              

              
              <div className="form-group">
                <label>
                  <input
                    type="checkbox"
                    checked={formData.is_active}
                    onChange={(e) => setFormData({...formData, is_active: e.target.checked})}
                    style={{ marginRight: '0.5rem' }}
                  />
                  Active
                </label>
              </div>
              
              <div style={{ display: 'flex', gap: '1rem', justifyContent: 'flex-end' }}>
                <button type="button" className="btn" onClick={closeModal} style={{ backgroundColor: '#6c757d', color: 'white' }}>
                  Cancel
                </button>
                <button type="submit" className="btn btn-primary">
                  {editingPlan ? 'Update' : 'Create'} Plan
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default Plans;