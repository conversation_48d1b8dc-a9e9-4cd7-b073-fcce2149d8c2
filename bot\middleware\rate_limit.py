"""Rate limiting middleware for the Telegram bot."""

import time
import logging
from typing import Callable, Any, Awaitable, Dict
from telegram import Update
from telegram.ext import ContextTypes
from collections import defaultdict, deque

logger = logging.getLogger(__name__)


class RateLimitMiddleware:
    """Middleware for rate limiting user requests."""
    
    def __init__(self, max_requests: int = 10, window_seconds: int = 60):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.user_requests: Dict[int, deque] = defaultdict(deque)
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def _clean_old_requests(self, user_id: int) -> None:
        """Remove old requests outside the time window."""
        current_time = time.time()
        user_queue = self.user_requests[user_id]
        
        while user_queue and current_time - user_queue[0] > self.window_seconds:
            user_queue.popleft()
    
    def _is_rate_limited(self, user_id: int) -> bool:
        """Check if user is rate limited."""
        self._clean_old_requests(user_id)
        return len(self.user_requests[user_id]) >= self.max_requests
    
    def _add_request(self, user_id: int) -> None:
        """Add a new request timestamp for the user."""
        self.user_requests[user_id].append(time.time())
    
    async def __call__(
        self,
        update: Update,
        context: ContextTypes.DEFAULT_TYPE,
        next_handler: Callable[[Update, ContextTypes.DEFAULT_TYPE], Awaitable[Any]]
    ) -> Any:
        """Process rate limiting middleware."""
        if not update.effective_user:
            return await next_handler(update, context)
        
        user_id = update.effective_user.id
        
        # Check if user is rate limited
        if self._is_rate_limited(user_id):
            self.logger.warning(f"Rate limit exceeded for user {user_id}")
            
            # Send rate limit message
            if update.message:
                await update.message.reply_text(
                    "⚠️ You're sending messages too quickly. Please wait a moment and try again.",
                    parse_mode='HTML'
                )
            elif update.callback_query:
                await update.callback_query.answer(
                    "⚠️ Too many requests. Please wait a moment.",
                    show_alert=True
                )
            
            return None
        
        # Add request and continue
        self._add_request(user_id)
        return await next_handler(update, context)


class CommandRateLimitMiddleware:
    """Specific rate limiting for commands."""
    
    def __init__(self, max_commands: int = 5, window_seconds: int = 30):
        self.max_commands = max_commands
        self.window_seconds = window_seconds
        self.user_commands: Dict[int, deque] = defaultdict(deque)
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def _clean_old_commands(self, user_id: int) -> None:
        """Remove old commands outside the time window."""
        current_time = time.time()
        user_queue = self.user_commands[user_id]
        
        while user_queue and current_time - user_queue[0] > self.window_seconds:
            user_queue.popleft()
    
    def _is_command_rate_limited(self, user_id: int) -> bool:
        """Check if user is command rate limited."""
        self._clean_old_commands(user_id)
        return len(self.user_commands[user_id]) >= self.max_commands
    
    def _add_command(self, user_id: int) -> None:
        """Add a new command timestamp for the user."""
        self.user_commands[user_id].append(time.time())
    
    async def __call__(
        self,
        update: Update,
        context: ContextTypes.DEFAULT_TYPE,
        next_handler: Callable[[Update, ContextTypes.DEFAULT_TYPE], Awaitable[Any]]
    ) -> Any:
        """Process command rate limiting middleware."""
        if not update.effective_user or not update.message or not update.message.text:
            return await next_handler(update, context)
        
        # Only apply to commands
        if not update.message.text.startswith('/'):
            return await next_handler(update, context)
        
        user_id = update.effective_user.id
        
        # Check if user is command rate limited
        if self._is_command_rate_limited(user_id):
            self.logger.warning(f"Command rate limit exceeded for user {user_id}")
            
            await update.message.reply_text(
                "⚠️ You're using commands too frequently. Please wait 30 seconds before using another command.",
                parse_mode='HTML'
            )
            
            return None
        
        # Add command and continue
        self._add_command(user_id)
        return await next_handler(update, context)


# Global instances
rate_limit_middleware = RateLimitMiddleware()
command_rate_limit_middleware = CommandRateLimitMiddleware()