"""Payment handlers for Telegram payments."""

import logging

from telegram import Update
from telegram.ext import ContextTypes
from bot.services.payment_service import payment_service
from bot.utils.helpers import get_text

logger = logging.getLogger(__name__)


class PaymentHandler:
    """Handler for Telegram payment processing."""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def handle_pre_checkout_query(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle pre-checkout query (payment validation)."""
        try:
            query = update.pre_checkout_query
            user_id = query.from_user.id
            
            # Parse invoice payload
            payload_data = payment_service.parse_invoice_payload(query.invoice_payload)
            
            if not payload_data:
                self.logger.error(f"Invalid invoice payload: {query.invoice_payload}")
                await query.answer(
                    ok=False,
                    error_message=get_text('invalid_payment_data_error', 'en')  # Default to English for pre-checkout
                )
                return
            
            plan_id = payload_data.get('plan_id')
            expected_user_id = payload_data.get('user_id')
            
            # Validate user
            if user_id != expected_user_id:
                self.logger.error(f"User ID mismatch: {user_id} != {expected_user_id}")
                await query.answer(
                    ok=False,
                    error_message=get_text('payment_validation_failed_error', 'en')
                )
                return
            
            # Validate plan
            plan = await payment_service.get_plan_by_id(plan_id)
            if not plan or not plan.get('is_active', False):
                self.logger.error(f"Invalid or inactive plan: {plan_id}")
                await query.answer(
                    ok=False,
                    error_message=get_text('package_no_longer_available_error', 'en')
                )
                return
            
            # Validate amount based on currency
            if query.currency == 'XTR':  # Telegram Stars
                expected_amount = int(plan['price'])  # Stars amount (no conversion)
            else:  # Traditional payment
                expected_amount = int(plan['price'] * 100)  # Convert to cents
                
            if query.total_amount != expected_amount:
                self.logger.error(
                    f"Amount mismatch for {query.currency}: {query.total_amount} != {expected_amount}"
                )
                await query.answer(
                    ok=False,
                    error_message=get_text('payment_amount_mismatch_error', 'en')
                )
                return
            
            # Check if user already has an active subscription for this plan
            user_subscriptions = await payment_service.get_user_subscriptions(
                user_id, is_active=True
            )
            
            for subscription in user_subscriptions:
                if subscription.get('plan_id') == plan_id:
                    await query.answer(
                        ok=False,
                        error_message=get_text('active_subscription_exists_error', 'en')
                    )
                    return
            
            # All validations passed
            await query.answer(ok=True)
            
            self.logger.info(
                f"Pre-checkout validation passed for user {user_id}, plan {plan_id}"
            )
        
        except Exception:
            self.logger.error("Error in pre-checkout query handler", exc_info=True)
            try:
                await update.pre_checkout_query.answer(
                    ok=False,
                    error_message=get_text('payment_validation_failed_error', 'en')
                )
            except:
                pass
    
    async def handle_successful_payment(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle successful payment."""
        try:
            payment = update.message.successful_payment
            user_id = update.effective_user.id
            user_data = context.user_data.get('user', {})
            language = user_data.get('language_code', 'en')
            
            self.logger.info(
                f"Processing successful payment for user {user_id}: {payment.telegram_payment_charge_id}"
            )
            
            # Parse invoice payload
            payload_data = payment_service.parse_invoice_payload(payment.invoice_payload)
            
            if not payload_data:
                self.logger.error(f"Invalid invoice payload in successful payment: {payment.invoice_payload}")
                error_text = get_text('payment_processing_error_support', language)
                await update.message.reply_text(error_text)
                return
            
            plan_id = payload_data.get('plan_id')
            
            # Process the payment
            result = await payment_service.process_successful_payment(
                user_id=user_id,
                plan_id=plan_id,
                payment_data={
                    'telegram_payment_charge_id': payment.telegram_payment_charge_id,
                    'provider_payment_charge_id': payment.provider_payment_charge_id,
                    'total_amount': payment.total_amount,
                    'currency': payment.currency,
                    'invoice_payload': payment.invoice_payload
                }
            )
            
            if not result or not result.get('success'):
                self.logger.error(f"Failed to process payment for user {user_id}")
                error_text = get_text('payment_processing_failed_refund', language)
                await update.message.reply_text(error_text)
                return
            
            # Get created VPN account details
            vpn_account = result.get('vpn_account')
            subscription = result.get('subscription')
            plan = result.get('plan')
            
            if not vpn_account:
                self.logger.error(f"No VPN account created for user {user_id}")
                error_text = get_text('vpn_account_creation_error', language)
                await update.message.reply_text(error_text)
                return
            
            # Format amount based on currency
            if payment.currency == 'XTR':
                amount_text = f"⭐ {payment.total_amount} Stars"
            else:
                amount_text = f"${payment.total_amount / 100:.2f}"
            
            # Format success message
            success_text = get_text('payment_successful_message', language).format(
                plan_name=plan.get('name', get_text('unknown', language)),
                amount=amount_text,
                transaction_id=payment.telegram_payment_charge_id,
                username=vpn_account.get('username'),
                duration_days=plan.get('duration_days', 0),
                data_limit_gb=plan.get('data_limit_gb', 0),
                subscription_url=vpn_account.get('subscription_url')
            )
            
            await update.message.reply_text(
                success_text,
                parse_mode='Markdown'
            )
            
            # Send QR code if available
            try:
                from bot.services.qr_service import qr_service
                
                qr_code_base64 = await qr_service.generate_qr_code_base64(
                    vpn_account.get('subscription_url', '')
                )
                
                if qr_code_base64:
                    import base64
                    import io
                    
                    qr_image_data = base64.b64decode(qr_code_base64.split(',')[1])
                    qr_image_file = io.BytesIO(qr_image_data)
                    qr_image_file.name = f"{vpn_account.get('username')}_qr.png"
                    
                    qr_caption = get_text('qr_code_caption', language).format(username=vpn_account.get('username'))
                    
                    await update.message.reply_photo(
                        photo=qr_image_file,
                        caption=qr_caption,
                        parse_mode='Markdown'
                    )
            except Exception as qr_error:
                self.logger.error(f"Error generating QR code: {qr_error}")
            
            self.logger.info(
                f"Successfully processed payment for user {user_id}, "
                f"created VPN account: {vpn_account.get('username')}"
            )
        
        except Exception:
            self.logger.error("Error in successful payment handler", exc_info=True)
            try:
                user_data = context.user_data.get('user', {})
                language = user_data.get('language_code', 'en')
                
                error_text = get_text('payment_processing_error_support', language)
                
                await update.message.reply_text(error_text)
            except:
                pass
    
    async def handle_shipping_query(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle shipping query (not used for digital products)."""
        try:
            query = update.shipping_query
            
            # For digital products, we don't need shipping
            await query.answer(
                ok=True,
                shipping_options=[],
                error_message=None
            )
        
        except Exception:
            self.logger.error("Error in shipping query handler", exc_info=True)
            try:
                await update.shipping_query.answer(
                    ok=False,
                    error_message=get_text('shipping_not_available_error', 'en')
            )
            except:
                pass
    
    async def send_invoice(self, context: ContextTypes.DEFAULT_TYPE, chat_id: int, plan_id: int, user_id: int) -> bool:
        """Send payment invoice to user."""
        try:
            # Create invoice data
            invoice_data = await payment_service.create_invoice(plan_id, user_id)
            
            if not invoice_data:
                self.logger.error(f"Failed to create invoice for plan {plan_id}, user {user_id}")
                return False
            
            # Send invoice
            await context.bot.send_invoice(
                chat_id=chat_id,
                **invoice_data
            )
            
            self.logger.info(f"Invoice sent for plan {plan_id} to user {user_id}")
            return True
        
        except Exception:
            self.logger.error("Error sending invoice", exc_info=True)
            return False
    
    async def refund_payment(self, context: ContextTypes.DEFAULT_TYPE, user_id: int, telegram_payment_charge_id: str) -> bool:
        """Refund a payment (placeholder - implement based on payment provider)."""
        try:
            # This is a placeholder for refund functionality
            # Implementation depends on the payment provider (Stripe, etc.)
            
            self.logger.info(
                f"Refund requested for user {user_id}, "
                f"charge ID: {telegram_payment_charge_id}"
            )
            
            # TODO: Implement actual refund logic based on payment provider
            # For now, just log the refund request
            
            return True
        
        except Exception:
            self.logger.error("Error processing refund", exc_info=True)
            return False
    
    async def get_payment_history(self, user_id: int, limit: int = 10) -> list:
        """Get user's payment history."""
        try:
            return await payment_service.get_user_payments(user_id, limit)
        
        except Exception:
            self.logger.error("Error getting payment history for user %s", user_id, exc_info=True)
            return []
    
    async def cancel_subscription(self, user_id: int, subscription_id: int) -> bool:
        """Cancel a user's subscription."""
        try:
            return await payment_service.cancel_subscription(subscription_id, user_id)
        
        except Exception as e:
            self.logger.error(
                "Error canceling subscription %s for user %s", subscription_id, user_id, exc_info=True
            )
            return False

    async def handle_stars_payment(self, update: Update, context: ContextTypes.DEFAULT_TYPE, package: dict) -> None:
        """Handle Telegram Stars payment."""
        try:
            user_data = context.user_data.get('user', {})
            user_id = user_data.get('id')
            language = user_data.get('language_code', 'en')

            if not user_id:
                await update.message.reply_text(get_text('errors.auth_required', language))
                return

            # Create Stars invoice
            stars_amount = int(package['price'] * 100)  # Convert to Stars (1 USD = 100 Stars)

            invoice_result = await payment_service.create_invoice(
                plan_id=package.get('plan_id', 1),
                user_id=user_id,
                payment_method='telegram_stars',
                amount=stars_amount
            )

            if invoice_result:
                self.logger.info(f"Stars payment initiated for user {user_id}, amount: {stars_amount} Stars")
            else:
                await update.message.reply_text(get_text('payment.error', language))

        except Exception as e:
            self.logger.error(f"Error handling Stars payment: {e}")
            language = context.user_data.get('user', {}).get('language_code', 'en')
            await update.message.reply_text(get_text('payment.error', language))

    async def create_card_payment(self, update: Update, context: ContextTypes.DEFAULT_TYPE, package: dict) -> str:
        """Create card payment and return payment URL."""
        try:
            user_data = context.user_data.get('user', {})
            user_id = user_data.get('id')

            if not user_id:
                return None

            # Create payment via payment service
            payment_result = await payment_service.create_invoice(
                plan_id=package.get('plan_id', 1),
                user_id=user_id,
                payment_method='card',
                amount=package['price']
            )

            if payment_result and payment_result.get('payment_url'):
                return payment_result['payment_url']

            return None

        except Exception as e:
            self.logger.error(f"Error creating card payment: {e}")
            return None

    async def create_crypto_payment(self, update: Update, context: ContextTypes.DEFAULT_TYPE, package: dict) -> dict:
        """Create crypto payment and return payment details."""
        try:
            user_data = context.user_data.get('user', {})
            user_id = user_data.get('id')

            if not user_id:
                return {}

            # Create crypto payment via payment service
            payment_result = await payment_service.create_invoice(
                plan_id=package.get('plan_id', 1),
                user_id=user_id,
                payment_method='crypto',
                amount=package['price']
            )

            if payment_result:
                return {
                    'address': payment_result.get('crypto_address', 'N/A'),
                    'amount': payment_result.get('crypto_amount', 'N/A'),
                    'currency': payment_result.get('crypto_currency', 'BTC')
                }

            return {}

        except Exception as e:
            self.logger.error(f"Error creating crypto payment: {e}")
            return {}

    async def create_ton_payment(self, update: Update, context: ContextTypes.DEFAULT_TYPE, package: dict) -> dict:
        """Create TON payment and return payment details."""
        try:
            user_data = context.user_data.get('user', {})
            user_id = user_data.get('id')

            if not user_id:
                return {}

            # Create TON payment via payment service
            payment_result = await payment_service.create_invoice(
                plan_id=package.get('plan_id', 1),
                user_id=user_id,
                payment_method='ton',
                amount=package['price']
            )

            if payment_result:
                return {
                    'address': payment_result.get('ton_address', 'N/A'),
                    'amount': payment_result.get('ton_amount', 'N/A')
                }

            return {}

        except Exception as e:
            self.logger.error(f"Error creating TON payment: {e}")
            return {}


# Global instance
payment_handler = PaymentHandler()