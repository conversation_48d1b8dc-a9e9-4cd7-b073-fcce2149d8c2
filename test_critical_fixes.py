#!/usr/bin/env python3
"""
Test script to verify all critical fixes are working.
This script tests the specific issues mentioned by the user.
"""

import asyncio
import logging
import sys
import os
from typing import Dict, Any, List
import traceback

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('critical_fixes_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class CriticalFixesTester:
    """Test all critical fixes mentioned by the user."""
    
    def __init__(self):
        self.results = {}
        self.errors = []
    
    async def run_all_tests(self):
        """Run all critical fix tests."""
        logger.info("🔍 Testing Critical Fixes...")
        
        tests = [
            ("1. Payment Handler Import Fix", self.test_payment_handler_import),
            ("2. Database Schema Fix", self.test_database_schema),
            ("3. Message Parsing Fix", self.test_message_parsing),
            ("4. Language Selection Fix", self.test_language_selection),
            ("5. Missing Localization Keys", self.test_localization_keys),
            ("6. Referral Navigation Enhancement", self.test_referral_navigation),
            ("7. State-Based Reply Messages", self.test_state_based_messages),
        ]
        
        for test_name, test_func in tests:
            logger.info(f"🔍 Running: {test_name}")
            try:
                result = await test_func()
                self.results[test_name] = result
                if result.get('success', False):
                    logger.info(f"✅ {test_name}: PASSED")
                else:
                    logger.error(f"❌ {test_name}: FAILED - {result.get('error', 'Unknown error')}")
                    self.errors.append(f"{test_name}: {result.get('error', 'Unknown error')}")
            except Exception as e:
                error_msg = f"Exception in {test_name}: {str(e)}"
                logger.error(f"💥 {error_msg}")
                self.errors.append(error_msg)
                self.results[test_name] = {'success': False, 'error': str(e)}
        
        # Generate summary report
        await self.generate_report()
    
    async def test_payment_handler_import(self) -> Dict[str, Any]:
        """Test 1: Payment Handler Import Error Fix."""
        try:
            # Test importing PaymentHandler from payments module
            from bot.handlers.payments import PaymentHandler
            
            # Test instantiation
            payment_handler = PaymentHandler()
            
            # Test required methods exist
            required_methods = [
                'handle_stars_payment',
                'create_card_payment', 
                'create_crypto_payment',
                'create_ton_payment'
            ]
            
            missing_methods = []
            available_methods = []
            
            for method in required_methods:
                if hasattr(payment_handler, method):
                    available_methods.append(method)
                else:
                    missing_methods.append(method)
            
            return {
                'success': len(missing_methods) == 0,
                'details': {
                    'class_imported': True,
                    'instance_created': True,
                    'available_methods': available_methods,
                    'missing_methods': missing_methods
                },
                'error': f"Missing methods: {missing_methods}" if missing_methods else None
            }
            
        except ImportError as e:
            return {
                'success': False,
                'error': f"Import error: {str(e)}"
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"PaymentHandler test error: {str(e)}"
            }
    
    async def test_database_schema(self) -> Dict[str, Any]:
        """Test 2: Database Schema Error Fix."""
        try:
            # Check if database schema has proper PostgreSQL syntax
            with open('database_schema.sql', 'r') as f:
                schema_content = f.read()
            
            issues = []
            fixes = []
            
            # Check for MySQL syntax that should be fixed
            if 'AUTO_INCREMENT' in schema_content:
                issues.append("MySQL AUTO_INCREMENT found (should be SERIAL)")
            else:
                fixes.append("MySQL AUTO_INCREMENT replaced with SERIAL")
            
            # Check for PostgreSQL features
            if 'SERIAL' in schema_content:
                fixes.append("PostgreSQL SERIAL types used")
            else:
                issues.append("Missing PostgreSQL SERIAL types")
            
            # Check for channels table with is_active column
            if 'CREATE TABLE channels' in schema_content and 'is_active BOOLEAN' in schema_content:
                fixes.append("Channels table has is_active column")
            else:
                issues.append("Channels table missing is_active column")
            
            # Check for proper indexes
            if 'CREATE INDEX' in schema_content:
                fixes.append("PostgreSQL indexes defined")
            else:
                issues.append("Missing PostgreSQL indexes")
            
            return {
                'success': len(issues) == 0,
                'details': {
                    'fixes_applied': fixes,
                    'remaining_issues': issues
                },
                'error': f"Schema issues: {issues}" if issues else None
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Database schema test error: {str(e)}"
            }
    
    async def test_message_parsing(self) -> Dict[str, Any]:
        """Test 3: Message Parsing Error Fix."""
        try:
            from bot.utils.helpers import get_text
            
            # Test localization strings for malformed Markdown
            test_keys = [
                'welcome.title',
                'welcome.description', 
                'settings.language_selection',
                'payment_successful_message',
                'qr_code_caption'
            ]
            
            parsing_issues = []
            successful_keys = []
            
            for key in test_keys:
                try:
                    text = get_text(key, 'en')
                    if text and not text.startswith('Translation error'):
                        # Check for unclosed Markdown tags
                        if text.count('**') % 2 != 0:
                            parsing_issues.append(f"{key}: Unclosed ** tags")
                        elif text.count('*') % 2 != 0:
                            parsing_issues.append(f"{key}: Unclosed * tags")
                        else:
                            successful_keys.append(key)
                    else:
                        parsing_issues.append(f"{key}: Missing or error text")
                except Exception as e:
                    parsing_issues.append(f"{key}: Exception - {str(e)}")
            
            return {
                'success': len(parsing_issues) == 0,
                'details': {
                    'successful_keys': successful_keys,
                    'parsing_issues': parsing_issues,
                    'keys_tested': len(test_keys)
                },
                'error': f"Parsing issues: {parsing_issues}" if parsing_issues else None
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Message parsing test error: {str(e)}"
            }
    
    async def test_language_selection(self) -> Dict[str, Any]:
        """Test 4: Language Selection Issues Fix."""
        try:
            from bot.utils.buttons import ReplyKeyboardBuilder
            from bot.handlers.commands import UserState
            
            # Test main menu doesn't have language buttons
            main_menu = ReplyKeyboardBuilder.create_main_menu('en')
            main_menu_text = str(main_menu.keyboard)
            
            language_in_main = any(lang in main_menu_text for lang in ['🇮🇷', '🇺🇸', '🇷🇺', '🇨🇳'])
            
            # Test settings menu exists
            settings_menu = ReplyKeyboardBuilder.create_settings_menu('en')
            
            # Test language selection menu exists
            lang_menu = ReplyKeyboardBuilder.create_language_selection_menu('en')
            lang_menu_text = str(lang_menu.keyboard)
            
            language_in_lang_menu = any(lang in lang_menu_text for lang in ['🇮🇷', '🇺🇸', '🇷🇺', '🇨🇳'])
            
            # Test UserState has LANGUAGE_SELECTION
            has_lang_state = hasattr(UserState, 'LANGUAGE_SELECTION')
            
            issues = []
            fixes = []
            
            if language_in_main:
                issues.append("Language buttons still in main menu")
            else:
                fixes.append("Language buttons removed from main menu")
            
            if language_in_lang_menu:
                fixes.append("Language buttons in language selection menu")
            else:
                issues.append("Language buttons missing from language selection menu")
            
            if has_lang_state:
                fixes.append("LANGUAGE_SELECTION state defined")
            else:
                issues.append("LANGUAGE_SELECTION state missing")
            
            return {
                'success': len(issues) == 0,
                'details': {
                    'fixes_applied': fixes,
                    'remaining_issues': issues,
                    'language_in_main_menu': language_in_main,
                    'language_in_lang_menu': language_in_lang_menu,
                    'has_language_state': has_lang_state
                },
                'error': f"Language selection issues: {issues}" if issues else None
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Language selection test error: {str(e)}"
            }
    
    async def test_localization_keys(self) -> Dict[str, Any]:
        """Test 5: Missing Localization Keys Fix."""
        try:
            from bot.utils.helpers import get_text
            
            # Test new keys that were added
            new_keys = [
                'buttons.earn',
                'buttons.change_language',
                'buttons.notifications',
                'settings.language_changed',
                'settings.invalid_language',
                'settings.notifications_enabled',
                'settings.notifications_disabled'
            ]
            
            languages = ['en', 'fa', 'ru', 'zh']
            missing_keys = []
            successful_keys = []
            
            for lang in languages:
                for key in new_keys:
                    try:
                        text = get_text(key, lang)
                        if text and not text.startswith('Translation error'):
                            successful_keys.append(f"{lang}.{key}")
                        else:
                            missing_keys.append(f"{lang}.{key}")
                    except Exception as e:
                        missing_keys.append(f"{lang}.{key}: {str(e)}")
            
            return {
                'success': len(missing_keys) == 0,
                'details': {
                    'successful_keys': len(successful_keys),
                    'missing_keys': missing_keys,
                    'languages_tested': languages,
                    'keys_tested': new_keys
                },
                'error': f"Missing keys: {missing_keys}" if missing_keys else None
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Localization keys test error: {str(e)}"
            }
    
    async def test_referral_navigation(self) -> Dict[str, Any]:
        """Test 6: Referral Navigation Enhancement."""
        try:
            from bot.utils.buttons import ReplyKeyboardBuilder
            from bot.utils.helpers import get_text
            
            # Test main menu has referral and earn buttons
            main_menu = ReplyKeyboardBuilder.create_main_menu('en')
            main_menu_text = str(main_menu.keyboard)
            
            has_referral = get_text('buttons.referral', 'en') in main_menu_text
            has_earn = get_text('buttons.earn', 'en') in main_menu_text
            
            # Test referral handler exists
            try:
                from bot.handlers.referral import referral_handler
                referral_handler_exists = True
            except ImportError:
                referral_handler_exists = False
            
            issues = []
            fixes = []
            
            if has_referral:
                fixes.append("Referral button in main menu")
            else:
                issues.append("Referral button missing from main menu")
            
            if has_earn:
                fixes.append("Earn button in main menu")
            else:
                issues.append("Earn button missing from main menu")
            
            if referral_handler_exists:
                fixes.append("Referral handler available")
            else:
                issues.append("Referral handler missing")
            
            return {
                'success': len(issues) == 0,
                'details': {
                    'fixes_applied': fixes,
                    'remaining_issues': issues,
                    'has_referral_button': has_referral,
                    'has_earn_button': has_earn,
                    'referral_handler_exists': referral_handler_exists
                },
                'error': f"Referral navigation issues: {issues}" if issues else None
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Referral navigation test error: {str(e)}"
            }
    
    async def test_state_based_messages(self) -> Dict[str, Any]:
        """Test 7: State-Based Reply Messages."""
        try:
            from bot.handlers.commands import UserState, CommandHandlers
            
            # Test UserState has all required states
            required_states = [
                'MAIN_MENU',
                'PREMIUM_PACKAGES', 
                'PAYMENT_METHODS',
                'SETTINGS',
                'ACCOUNTS',
                'LANGUAGE_SELECTION'
            ]
            
            missing_states = []
            available_states = []
            
            for state in required_states:
                if hasattr(UserState, state):
                    available_states.append(state)
                else:
                    missing_states.append(state)
            
            # Test CommandHandlers has state handling methods
            command_handlers = CommandHandlers()
            
            state_methods = [
                '_handle_main_menu_buttons',
                '_handle_settings_buttons',
                '_handle_language_selection',
                '_handle_back_navigation'
            ]
            
            missing_methods = []
            available_methods = []
            
            for method in state_methods:
                if hasattr(command_handlers, method):
                    available_methods.append(method)
                else:
                    missing_methods.append(method)
            
            return {
                'success': len(missing_states) == 0 and len(missing_methods) == 0,
                'details': {
                    'available_states': available_states,
                    'missing_states': missing_states,
                    'available_methods': available_methods,
                    'missing_methods': missing_methods
                },
                'error': f"Missing states: {missing_states}, Missing methods: {missing_methods}" if missing_states or missing_methods else None
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"State-based messages test error: {str(e)}"
            }
    
    async def generate_report(self):
        """Generate a comprehensive testing report."""
        logger.info("\n" + "="*80)
        logger.info("🔍 CRITICAL FIXES TESTING REPORT")
        logger.info("="*80)
        
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results.values() if r.get('success', False))
        failed_tests = total_tests - passed_tests
        
        logger.info(f"📊 SUMMARY: {passed_tests}/{total_tests} tests passed")
        
        if failed_tests > 0:
            logger.error(f"❌ FAILED TESTS ({failed_tests}):")
            for error in self.errors:
                logger.error(f"   • {error}")
        
        logger.info("\n📋 DETAILED RESULTS:")
        for test_name, result in self.results.items():
            status = "✅ PASS" if result.get('success', False) else "❌ FAIL"
            logger.info(f"   {status} {test_name}")
            if result.get('details'):
                for key, value in result['details'].items():
                    if isinstance(value, list) and len(value) > 3:
                        logger.info(f"      {key}: {len(value)} items")
                    else:
                        logger.info(f"      {key}: {value}")
        
        logger.info("\n" + "="*80)
        
        if failed_tests == 0:
            logger.info("🎉 ALL CRITICAL FIXES VERIFIED! Bot is ready for deployment.")
        else:
            logger.error(f"⚠️  {failed_tests} ISSUES REMAIN. Please review the errors above.")
        
        logger.info("="*80)


async def main():
    """Main function to run the critical fixes testing."""
    tester = CriticalFixesTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
