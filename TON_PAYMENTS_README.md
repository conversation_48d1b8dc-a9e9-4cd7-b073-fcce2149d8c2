# TON Payments Integration

This VPN Telegram Bot now supports **TON (The Open Network)** cryptocurrency payments, providing users with a fast, secure, and decentralized payment option.

## 🚀 Features

- **Native TON Support**: Direct integration with TON blockchain
- **Real-time Price Updates**: Automatic USD to TON conversion
- **Secure Transactions**: Cryptographic verification of payments
- **User-Friendly Interface**: Simple payment flow within Telegram
- **Automatic Processing**: Payments are verified and processed automatically
- **Transaction History**: Complete payment tracking and history

## 💡 How It Works

1. **User selects a premium plan** and chooses "Pay with TON"
2. **<PERSON><PERSON> generates payment details**:
   - Unique wallet address for the payment
   - Exact TON amount (calculated from USD price)
   - Unique memo for payment identification
   - Payment expiration time (30 minutes by default)
3. **User sends TON** from their wallet to the provided address
4. **<PERSON><PERSON> monitors the blockchain** for incoming transactions
5. **Payment is automatically verified** and the premium subscription is activated

## 🔧 Quick Setup

### 1. Install Dependencies

```bash
pip install pytoniq pytoniq-core pytonconnect
```

### 2. Configure Environment Variables

Add to your `.env` file:

```bash
# TON Payment Configuration
TON_MASTER_WALLET=your_ton_master_wallet_address
TON_MASTER_PRIVATE_KEY=your_ton_master_private_key
TON_NETWORK=mainnet
TON_PAYMENT_TIMEOUT_MINUTES=30
```

### 3. Run Database Migration

```bash
psql -h $POSTGRES_HOST -U $POSTGRES_USER -d $POSTGRES_DATABASE -f migrations/add_ton_payments_table.sql
```

### 4. Restart Your Bot

```bash
python -m bot.main
```

## 📱 User Experience

### Payment Flow

1. User navigates to `/premium` command
2. Selects desired plan (Basic, Standard, or Premium)
3. Chooses "💎 Pay with TON" option
4. Receives payment instructions:
   ```
   💎 TON Payment Details
   
   Plan: Premium Monthly ($29.99)
   Amount: 1.234 TON
   
   📍 Send TON to:
   UQAbc123...xyz789
   
   📝 Memo: PAY_abc123def456
   
   ⏰ Expires: 30 minutes
   ```
5. Sends payment from their TON wallet
6. Clicks "🔍 Check Payment Status"
7. Receives confirmation and premium access

### Supported Wallets

Users can pay from any TON-compatible wallet:
- **TON Wallet** (wallet.ton.org)
- **Tonkeeper** (tonkeeper.com)
- **TON Space** (Telegram's built-in wallet)
- **MyTonWallet** (mytonwallet.io)
- **OpenMask** (Browser extension)

## 🛠️ Technical Implementation

### Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Telegram Bot  │    │   TON Service    │    │  TON Blockchain │
│                 │    │                  │    │                 │
│ ┌─────────────┐ │    │ ┌──────────────┐ │    │ ┌─────────────┐ │
│ │  Callbacks  │◄┼────┼►│ Payment      │◄┼────┼►│ Transaction │ │
│ │  Handler    │ │    │ │ Processing   │ │    │ │ Monitoring  │ │
│ └─────────────┘ │    │ └──────────────┘ │    │ └─────────────┘ │
│                 │    │                  │    │                 │
│ ┌─────────────┐ │    │ ┌──────────────┐ │    │ ┌─────────────┐ │
│ │   Payment   │◄┼────┼►│ Price        │◄┼────┼►│   Price     │ │
│ │   Service   │ │    │ │ Fetching     │ │    │ │   Oracle    │ │
│ └─────────────┘ │    │ └──────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### Key Components

#### 1. TON Service (`bot/services/ton_service.py`)
- **Price Management**: Fetches real-time TON/USD rates
- **Payment Creation**: Generates unique payment addresses and memos
- **Transaction Verification**: Monitors blockchain for payments
- **Status Tracking**: Updates payment status in database

#### 2. Payment Integration (`bot/services/payment_service.py`)
- **Unified Interface**: Integrates TON with existing payment methods
- **Invoice Generation**: Creates TON payment invoices
- **Status Checking**: Provides payment status updates
- **Success Processing**: Handles successful payment completion

#### 3. User Interface (`bot/handlers/callbacks.py`)
- **Payment Selection**: Adds TON option to payment methods
- **Payment Display**: Shows payment details and instructions
- **Status Updates**: Provides real-time payment status
- **Error Handling**: Manages payment failures and timeouts

### Database Schema

```sql
CREATE TABLE ton_payments (
    payment_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id BIGINT NOT NULL,
    plan_id UUID NOT NULL,
    wallet_address VARCHAR(255) NOT NULL,
    ton_amount DECIMAL(20, 9) NOT NULL,
    usd_amount DECIMAL(10, 2) NOT NULL,
    memo VARCHAR(255) NOT NULL UNIQUE,
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    transaction_hash VARCHAR(255),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    confirmed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

## 🔒 Security Features

### Payment Security
- **Unique Memos**: Each payment has a unique identifier
- **Time-based Expiration**: Payments expire after 30 minutes
- **Amount Verification**: Exact amount matching required
- **Blockchain Verification**: Cryptographic proof of payment

### Wallet Security
- **Master Wallet Isolation**: Dedicated wallet for bot operations
- **Private Key Protection**: Secure environment variable storage
- **Network Selection**: Mainnet/testnet configuration
- **Transaction Monitoring**: Real-time blockchain monitoring

### Data Security
- **Encrypted Storage**: Sensitive data encrypted at rest
- **Audit Logging**: Complete payment audit trail
- **Access Control**: Role-based access to payment data
- **Compliance**: GDPR-compliant data handling

## 📊 Monitoring and Analytics

### Payment Metrics
- **Success Rate**: Percentage of successful payments
- **Average Processing Time**: Time from payment to confirmation
- **Popular Plans**: Most purchased premium plans
- **Revenue Tracking**: TON and USD revenue analytics

### System Health
- **Blockchain Connectivity**: TON network connection status
- **Price Feed Status**: Real-time price update monitoring
- **Database Performance**: Payment processing performance
- **Error Rates**: Payment failure analysis

## 🚨 Troubleshooting

### Common Issues

#### "TON master wallet not configured"
**Cause**: Missing `TON_MASTER_WALLET` environment variable  
**Solution**: Add your TON wallet address to `.env` file

#### "Payment not found"
**Cause**: Transaction not yet confirmed on blockchain  
**Solution**: Wait for blockchain confirmation (usually 1-2 minutes)

#### "Invalid memo"
**Cause**: User didn't include the required memo  
**Solution**: Instruct user to include the exact memo in transaction

#### "Payment expired"
**Cause**: Payment sent after 30-minute timeout  
**Solution**: Generate new payment with fresh expiration time

### Debug Commands

```bash
# Check TON service status
python -c "from bot.services.ton_service import TONService; print('TON service OK')"

# Verify database connection
psql -h $POSTGRES_HOST -U $POSTGRES_USER -d $POSTGRES_DATABASE -c "SELECT COUNT(*) FROM ton_payments;"

# Test price fetching
curl "https://api.coingecko.com/api/v3/simple/price?ids=the-open-network&vs_currencies=usd"
```

## 🔄 Migration from Other Payment Methods

TON payments work alongside existing payment methods:

- **Telegram Stars**: Native Telegram payments
- **Card Payments**: Traditional payment processors
- **Crypto Payments**: NowPayments integration
- **TON Payments**: Direct TON blockchain integration

Users can choose their preferred payment method, and all methods lead to the same premium subscription benefits.

## 🌟 Benefits for Users

### For End Users
- **Fast Transactions**: TON blockchain's high speed
- **Low Fees**: Minimal transaction costs
- **Privacy**: Pseudonymous transactions
- **Global Access**: No geographic restrictions
- **24/7 Availability**: Blockchain never sleeps

### For Bot Operators
- **Reduced Fees**: Lower payment processing costs
- **Instant Settlement**: No waiting for payment processors
- **Global Reach**: Accept payments from anywhere
- **Transparency**: All transactions on public blockchain
- **Innovation**: Cutting-edge payment technology

## 📈 Future Enhancements

### Planned Features
- **TON Connect Integration**: One-click wallet connection
- **Subscription Management**: Automatic recurring payments
- **Multi-currency Support**: Additional cryptocurrency options
- **Advanced Analytics**: Detailed payment insights
- **Mobile Optimization**: Enhanced mobile payment experience

### Community Contributions
We welcome contributions to improve TON payment integration:
- **Bug Reports**: Report issues on GitHub
- **Feature Requests**: Suggest new features
- **Code Contributions**: Submit pull requests
- **Documentation**: Improve setup guides

## 📞 Support

For TON payment support:

1. **Check Documentation**: Review setup guides and troubleshooting
2. **Search Issues**: Look for similar problems on GitHub
3. **Community Help**: Ask in TON developer communities
4. **Create Issue**: Report bugs or request features

---

**Ready to accept TON payments?** Follow the [TON Payments Setup Guide](docs/guides/TON_PAYMENTS_SETUP.md) to get started!