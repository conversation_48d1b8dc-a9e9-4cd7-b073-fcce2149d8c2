"""Redis client configuration and initialization."""

import redis.asyncio as redis
import logging
from typing import Optional
from bot.config import settings

logger = logging.getLogger(__name__)

# Global Redis client instance
_redis_client: Optional[redis.Redis] = None


async def init_redis() -> redis.Redis:
    """Initialize Redis connection."""
    global _redis_client
    
    try:
        # Create Redis client
        _redis_client = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            password=getattr(settings, 'REDIS_PASSWORD', None),
            db=settings.REDIS_DB,
            decode_responses=True,
            socket_connect_timeout=5,
            socket_timeout=5,
            retry_on_timeout=True,
            health_check_interval=30
        )
        
        # Test connection
        await _redis_client.ping()
        logger.info(f"Redis connected successfully to {settings.REDIS_HOST}:{settings.REDIS_PORT}")
        
        return _redis_client
        
    except Exception as e:
        logger.error(f"Failed to connect to Redis: {e}")
        # For development, we can continue without Redis
        logger.warning("Continuing without Redis - some features may not work properly")
        _redis_client = None
        return None


def get_redis() -> Optional[redis.Redis]:
    """Get Redis client instance."""
    return _redis_client


async def close_redis():
    """Close Redis connection."""
    global _redis_client
    
    if _redis_client:
        try:
            await _redis_client.close()
            logger.info("Redis connection closed")
        except Exception as e:
            logger.error(f"Error closing Redis connection: {e}")
        finally:
            _redis_client = None


class RedisManager:
    """Redis manager for common operations."""
    
    def __init__(self):
        self.client = get_redis()
    
    async def set_with_expiry(self, key: str, value: str, expiry: int) -> bool:
        """Set key with expiry time."""
        if not self.client:
            return False
        
        try:
            await self.client.setex(key, expiry, value)
            return True
        except Exception as e:
            logger.error(f"Redis set error: {e}")
            return False
    
    async def get(self, key: str) -> Optional[str]:
        """Get value by key."""
        if not self.client:
            return None
        
        try:
            return await self.client.get(key)
        except Exception as e:
            logger.error(f"Redis get error: {e}")
            return None
    
    async def delete(self, key: str) -> bool:
        """Delete key."""
        if not self.client:
            return False
        
        try:
            result = await self.client.delete(key)
            return result > 0
        except Exception as e:
            logger.error(f"Redis delete error: {e}")
            return False
    
    async def increment(self, key: str, amount: int = 1) -> Optional[int]:
        """Increment key value."""
        if not self.client:
            return None
        
        try:
            return await self.client.incr(key, amount)
        except Exception as e:
            logger.error(f"Redis increment error: {e}")
            return None
    
    async def exists(self, key: str) -> bool:
        """Check if key exists."""
        if not self.client:
            return False
        
        try:
            result = await self.client.exists(key)
            return result > 0
        except Exception as e:
            logger.error(f"Redis exists error: {e}")
            return False


# Global Redis manager instance
redis_manager = RedisManager()

# Backward compatibility
redis_client = get_redis