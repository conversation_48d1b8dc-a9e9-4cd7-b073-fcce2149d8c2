"""Celery tasks for the VPN bot."""

import logging
from celery import Celery
from bot.config import settings
from bot.database import get_db_session
from bot.models import User, VPNAccount, ChannelSubscription, Channel, VPNPanel
from bot.marzban_api import Mar<PERSON>banAP<PERSON>
from sqlalchemy.future import select
from telegram import Bo<PERSON>
from sqlalchemy.ext.asyncio import AsyncSession

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create Celery app
app = Celery('tasks')

# Configure Celery
app.conf.update(
    broker_url=settings.REDIS_URL,
    result_backend=settings.REDIS_URL,
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    task_routes={
        'bot.tasks.*': {'queue': 'default'},
    }
)

# Auto-discover tasks
app.autodiscover_tasks(['bot.tasks'])

async def get_marzban_api_for_panel(panel_id, db_session):
    panel = await db_session.get(VPNPanel, panel_id)
    if not panel:
        return None
    return Marzban<PERSON>I(panel.base_url, panel.api_username, panel.api_password)

@app.task
async def check_user_channel_membership_task(user_id: int, channel_id: str, db_session: AsyncSession):
    """Check if a user is a member of a specific channel."""
    bot = Bot(token=settings.BOT_TOKEN)
    try:
        member = await bot.get_chat_member(chat_id=channel_id, user_id=user_id)
        if member.status in ['member', 'administrator', 'creator']:
            stmt = select(ChannelSubscription).where(ChannelSubscription.user_id == user_id, ChannelSubscription.channel_id == channel_id)
            result = await db_session.execute(stmt)
            subscription = result.scalar_one_or_none()
            if not subscription:
                new_subscription = ChannelSubscription(user_id=user_id, channel_id=channel_id, status='active')
                db_session.add(new_subscription)
                await db_session.commit()
            return True
        else:
            stmt = select(ChannelSubscription).where(ChannelSubscription.user_id == user_id, ChannelSubscription.channel_id == channel_id)
            result = await db_session.execute(stmt)
            subscription = result.scalar_one_or_none()
            if subscription:
                await db_session.delete(subscription)
                await db_session.commit()
            return False
    except Exception as e:
        logger.error(f"Error checking channel membership for user {user_id} in {channel_id}: {e}")
        return False

@app.task
async def check_all_users_channel_subscriptions():
    """Periodically check all users' channel subscriptions."""
    bot = Bot(token=settings.BOT_TOKEN)
    async with get_db_session() as db_session:
        required_channels_stmt = select(Channel).where(Channel.is_required == True)
        required_channels_result = await db_session.execute(required_channels_stmt)
        required_channels = required_channels_result.scalars().all()

        if not required_channels:
            return

        users_stmt = select(User).where(User.is_active == True)
        users_result = await db_session.execute(users_stmt)
        users = users_result.scalars().all()

        for user in users:
            for channel in required_channels:
                try:
                    member = await bot.get_chat_member(chat_id=channel.channel_id, user_id=user.telegram_id)
                    if member.status not in ['member', 'administrator', 'creator']:
                        # User left the channel, find their trial account and deactivate it
                        trial_account_stmt = select(VPNAccount).where(VPNAccount.user_id == user.id, VPNAccount.is_trial == True)
                        trial_account_result = await db_session.execute(trial_account_stmt)
                        trial_account = trial_account_result.scalar_one_or_none()

                        if trial_account and trial_account.is_active:
                            trial_account.is_active = False
                            await db_session.commit()
                            await bot.send_message(chat_id=user.telegram_id, text=f"You have left the required channel {channel.channel_id}. Your trial account has been deactivated. Please purchase a premium plan to continue using the service.")
                except Exception as e:
                    logger.error(f"Error checking membership for user {user.telegram_id} in channel {channel.channel_id}: {e}")

@app.task
async def create_trial_vpn_account_task(user_id: int):
    """Create a trial VPN account for a user."""
    async with get_db_session() as db_session:
        user = await db_session.get(User, user_id)
        if not user or user.has_used_trial:
            return "User not eligible for trial."

        required_channels_stmt = select(Channel).where(Channel.is_required == True)
        required_channels_result = await db_session.execute(required_channels_stmt)
        required_channels = required_channels_result.scalars().all()

        for channel in required_channels:
            is_member = await check_user_channel_membership_task(user.telegram_id, channel.channel_id, db_session)
            if not is_member:
                return f"User must join the required channel: {channel.channel_id}"
        if not is_member:
            return "User must join the trial channel."

        # Logic to create a trial account using MarzbanAPI
        # This is a simplified example. You'll need to fetch panel details.
        # For now, let's assume a default panel or logic to select one.
        # You would also need a predefined trial plan.
        # marzban_api = await get_marzban_api_for_panel(panel_id, db_session)
        # trial_plan = ...
        # new_account_info = await marzban_api.add_user(...)

        user.has_used_trial = True
        await db_session.commit()
        logger.info(f"Trial VPN account created for user {user_id}")
        return f"Trial VPN account created for user {user_id}"

@app.task
async def reset_all_trial_users_task():
    """Reset trial status for all users."""
    async with get_db_session() as db_session:
        stmt = select(User).where(User.has_used_trial == True)
        result = await db_session.execute(stmt)
        users = result.scalars().all()
        for user in users:
            user.has_used_trial = False
        await db_session.commit()
        logger.info("Reset trial status for all users.")
        return "All trial users have been reset."

@app.task
def backup_database_task():
    """Backup database task."""
    logger.info("Database backup task executed")
    return "Database backup completed"

@app.task
def check_channel_subscriptions_task():
    """Check channel subscriptions task."""
    logger.info("Channel subscriptions check task executed")
    return "Channel subscriptions checked"

@app.task
def check_vpn_expiries_task():
    """Check VPN expiries task."""
    logger.info("VPN expiries check task executed")
    return "VPN expiries checked"

@app.task
async def create_free_vpn_account_task(user_id: int):
    """Create free VPN account task."""
    logger.info(f"Creating free VPN account for user {user_id}")
    return f"Free VPN account created for user {user_id}"

@app.task
async def create_premium_vpn_account_task(user_id: int, plan_id: int):
    """Create premium VPN account task."""
    logger.info(f"Creating premium VPN account for user {user_id} with plan {plan_id}")
    return f"Premium VPN account created for user {user_id}"

@app.task
async def deactivate_vpn_account_task(account_id: int):
    """Deactivate VPN account task."""
    logger.info(f"Deactivating VPN account {account_id}")
    return f"VPN account {account_id} deactivated"

@app.task
async def delete_vpn_account_task(account_id: int):
    """Delete VPN account task."""
    logger.info(f"Deleting VPN account {account_id}")
    return f"VPN account {account_id} deleted"

@app.task
def get_user_usage_report_task(user_id: int):
    """Get user usage report task."""
    logger.info(f"Getting usage report for user {user_id}")
    return f"Usage report generated for user {user_id}"

@app.task
async def reset_vpn_account_usage_task(account_id: int):
    """Reset VPN account usage task."""
    logger.info(f"Resetting usage for VPN account {account_id}")
    return f"Usage reset for VPN account {account_id}"

if __name__ == '__main__':
    app.start()