<?php

declare(strict_types=1);

namespace VpnBot\Models;

use VpnBot\Database\Connection;
use PDO;

class User
{
    public ?int $id = null;
    public int $telegram_id;
    public ?string $username = null;
    public ?string $first_name = null;
    public ?string $last_name = null;
    public string $language_code = 'en';
    public bool $is_admin = false;
    public bool $is_premium = false;
    public bool $has_used_trial = false;
    public int $trial_count = 0;
    public ?string $last_trial_at = null;
    public ?string $created_at = null;
    public ?string $updated_at = null;
    public bool $is_active = true;
    public ?string $last_active = null;
    public ?string $last_seen = null;
    public bool $notification_enabled = true;
    public int $total_data_used = 0;
    public int $command_count = 0;
    public ?string $referral_code = null;
    public ?int $referred_by = null;
    public int $referral_count = 0;
    public int $total_referral_rewards = 0;

    public static function findByTelegramId(int $telegramId): ?User
    {
        $pdo = Connection::getInstance();
        $stmt = $pdo->prepare('SELECT * FROM users WHERE telegram_id = ?');
        $stmt->execute([$telegramId]);
        
        $data = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!$data) {
            return null;
        }

        return self::fromArray($data);
    }

    public static function findById(int $id): ?User
    {
        $pdo = Connection::getInstance();
        $stmt = $pdo->prepare('SELECT * FROM users WHERE id = ?');
        $stmt->execute([$id]);
        
        $data = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!$data) {
            return null;
        }

        return self::fromArray($data);
    }

    public function save(): bool
    {
        $pdo = Connection::getInstance();
        
        if ($this->id === null) {
            return $this->insert($pdo);
        } else {
            return $this->update($pdo);
        }
    }

    private function insert(PDO $pdo): bool
    {
        $sql = '
            INSERT INTO users (
                telegram_id, username, first_name, last_name, language_code,
                is_admin, is_premium, has_used_trial, trial_count, last_trial_at,
                is_active, last_active, last_seen, notification_enabled,
                total_data_used, command_count, referral_code, referred_by,
                referral_count, total_referral_rewards, created_at, updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW()
            )
        ';

        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            $this->telegram_id,
            $this->username,
            $this->first_name,
            $this->last_name,
            $this->language_code,
            $this->is_admin,
            $this->is_premium,
            $this->has_used_trial,
            $this->trial_count,
            $this->last_trial_at,
            $this->is_active,
            $this->last_active,
            $this->last_seen,
            $this->notification_enabled,
            $this->total_data_used,
            $this->command_count,
            $this->referral_code,
            $this->referred_by,
            $this->referral_count,
            $this->total_referral_rewards,
        ]);

        if ($result) {
            $this->id = (int)$pdo->lastInsertId();
        }

        return $result;
    }

    private function update(PDO $pdo): bool
    {
        $sql = '
            UPDATE users SET
                username = ?, first_name = ?, last_name = ?, language_code = ?,
                is_admin = ?, is_premium = ?, has_used_trial = ?, trial_count = ?,
                last_trial_at = ?, is_active = ?, last_active = ?, last_seen = ?,
                notification_enabled = ?, total_data_used = ?, command_count = ?,
                referral_code = ?, referred_by = ?, referral_count = ?,
                total_referral_rewards = ?, updated_at = NOW()
            WHERE id = ?
        ';

        $stmt = $pdo->prepare($sql);
        return $stmt->execute([
            $this->username,
            $this->first_name,
            $this->last_name,
            $this->language_code,
            $this->is_admin,
            $this->is_premium,
            $this->has_used_trial,
            $this->trial_count,
            $this->last_trial_at,
            $this->is_active,
            $this->last_active,
            $this->last_seen,
            $this->notification_enabled,
            $this->total_data_used,
            $this->command_count,
            $this->referral_code,
            $this->referred_by,
            $this->referral_count,
            $this->total_referral_rewards,
            $this->id,
        ]);
    }

    public static function fromArray(array $data): User
    {
        $user = new User();
        $user->id = $data['id'] ?? null;
        $user->telegram_id = (int)$data['telegram_id'];
        $user->username = $data['username'];
        $user->first_name = $data['first_name'];
        $user->last_name = $data['last_name'];
        $user->language_code = $data['language_code'] ?? 'en';
        $user->is_admin = (bool)$data['is_admin'];
        $user->is_premium = (bool)$data['is_premium'];
        $user->has_used_trial = (bool)$data['has_used_trial'];
        $user->trial_count = (int)$data['trial_count'];
        $user->last_trial_at = $data['last_trial_at'];
        $user->created_at = $data['created_at'];
        $user->updated_at = $data['updated_at'];
        $user->is_active = (bool)$data['is_active'];
        $user->last_active = $data['last_active'];
        $user->last_seen = $data['last_seen'];
        $user->notification_enabled = (bool)$data['notification_enabled'];
        $user->total_data_used = (int)$data['total_data_used'];
        $user->command_count = (int)$data['command_count'];
        $user->referral_code = $data['referral_code'];
        $user->referred_by = $data['referred_by'] ? (int)$data['referred_by'] : null;
        $user->referral_count = (int)$data['referral_count'];
        $user->total_referral_rewards = (int)$data['total_referral_rewards'];

        return $user;
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'telegram_id' => $this->telegram_id,
            'username' => $this->username,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'language_code' => $this->language_code,
            'is_admin' => $this->is_admin,
            'is_premium' => $this->is_premium,
            'has_used_trial' => $this->has_used_trial,
            'trial_count' => $this->trial_count,
            'last_trial_at' => $this->last_trial_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'is_active' => $this->is_active,
            'last_active' => $this->last_active,
            'last_seen' => $this->last_seen,
            'notification_enabled' => $this->notification_enabled,
            'total_data_used' => $this->total_data_used,
            'command_count' => $this->command_count,
            'referral_code' => $this->referral_code,
            'referred_by' => $this->referred_by,
            'referral_count' => $this->referral_count,
            'total_referral_rewards' => $this->total_referral_rewards,
        ];
    }

    public function generateReferralCode(): string
    {
        if ($this->referral_code === null) {
            $this->referral_code = 'ref_' . $this->telegram_id . '_' . substr(md5((string)time()), 0, 8);
        }
        return $this->referral_code;
    }

    public function updateLastSeen(): void
    {
        $this->last_seen = date('Y-m-d H:i:s');
        $this->last_active = date('Y-m-d H:i:s');
        $this->command_count++;
    }
}
