import React from 'react';
import { useUsers, usePanels, usePlans } from '../services/api';

interface DashboardStats {
  totalUsers: number;
  activeUsers: number;
  totalPanels: number;
  activePanels: number;
  totalPremiumPlans: number;
  totalRevenue: number;
}

const Dashboard: React.FC = () => {
  const { data: users, isLoading: usersLoading, error: usersError } = useUsers();
  const { data: panels, isLoading: panelsLoading, error: panelsError } = usePanels();
  const { data: plans, isLoading: plansLoading, error: plansError } = usePlans();

  const isLoading = usersLoading || panelsLoading || plansLoading;
  const error = usersError || panelsError || plansError;

  const stats: DashboardStats = {
    totalUsers: users?.length || 0,
    activeUsers: users?.filter(user => user.is_premium || user.data_used > 0).length || 0,
    totalPanels: panels?.length || 0,
    activePanels: panels?.filter(panel => panel.is_active).length || 0,
    totalPremiumPlans: plans?.filter(plan => plan.is_active).length || 0,
    totalRevenue: 0, // Revenue calculation would need additional data
  };

  if (isLoading) {
    return <div className="loading">Loading dashboard...</div>;
  }

  return (
    <div>
      <div className="page-header">
        <h1>Dashboard</h1>
        <p>Overview of your VPN bot administration</p>
      </div>

      {error && <div className="error">Failed to fetch dashboard data</div>}

      <div className="stats-grid" style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1.5rem' }}>
        <div className="card">
          <h3 style={{ color: '#3498db', marginBottom: '0.5rem' }}>Total Users</h3>
          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#2c3e50' }}>{stats.totalUsers}</div>
          <p style={{ color: '#7f8c8d', marginTop: '0.5rem' }}>Registered users</p>
        </div>

        <div className="card">
          <h3 style={{ color: '#27ae60', marginBottom: '0.5rem' }}>Active Users</h3>
          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#2c3e50' }}>{stats.activeUsers}</div>
          <p style={{ color: '#7f8c8d', marginTop: '0.5rem' }}>Active users</p>
        </div>

        <div className="card">
          <h3 style={{ color: '#e74c3c', marginBottom: '0.5rem' }}>Active Panels</h3>
          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#2c3e50' }}>{stats.activePanels}</div>
          <p style={{ color: '#7f8c8d', marginTop: '0.5rem' }}>Active panels</p>
        </div>

        <div className="card">
          <h3 style={{ color: '#f39c12', marginBottom: '0.5rem' }}>Premium Plans</h3>
          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#2c3e50' }}>{stats.totalPremiumPlans}</div>
          <p style={{ color: '#7f8c8d', marginTop: '0.5rem' }}>Premium plans</p>
        </div>

        <div className="card">
          <h3 style={{ color: '#9b59b6', marginBottom: '0.5rem' }}>Total Revenue</h3>
          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#2c3e50' }}>${stats.totalRevenue}</div>
          <p style={{ color: '#7f8c8d', marginTop: '0.5rem' }}>Total revenue</p>
        </div>
      </div>

      <div className="card" style={{ marginTop: '2rem' }}>
        <h3 style={{ marginBottom: '1rem' }}>Quick Actions</h3>
        <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>
          <a href="/panels" className="btn btn-primary">Manage Panels</a>
          <a href="/plans" className="btn btn-success">Manage Plans</a>
          <a href="/users" className="btn btn-warning">View Users</a>
          <a href="/settings" className="btn btn-secondary" style={{ backgroundColor: '#6c757d' }}>Settings</a>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;