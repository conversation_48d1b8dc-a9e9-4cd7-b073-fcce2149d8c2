<?php

declare(strict_types=1);

namespace VpnBot\Bot\Commands;

use <PERSON><PERSON>\TelegramBot\Commands\UserCommand;
use <PERSON><PERSON>\TelegramBot\Entities\ServerResponse;
use <PERSON><PERSON>\TelegramBot\Request;
use VpnBot\Models\User;
use VpnBot\Utils\Localization;
use VpnBot\Bot\Keyboards\InlineKeyboardBuilder;

class MenuCommand extends UserCommand
{
    protected $name = 'menu';
    protected $description = 'Show main menu';
    protected $usage = '/menu';
    protected $version = '1.0.0';

    public function execute(): ServerResponse
    {
        $message = $this->getMessage();
        $user = $message->getFrom();
        $chat = $message->getChat();

        // Get user from database
        $dbUser = User::findByTelegramId($user->getId());
        if (!$dbUser) {
            return Request::sendMessage([
                'chat_id' => $chat->getId(),
                'text' => 'Please start the bot first with /start',
            ]);
        }

        $dbUser->updateLastSeen();
        $dbUser->save();

        $language = $dbUser->language_code;
        $localization = Localization::getInstance();
        $keyboardBuilder = new InlineKeyboardBuilder();

        $menuText = $localization->get('menu.title', $language);
        $menuText .= "\n\n" . $localization->get('menu.select_option', $language);

        return Request::sendMessage([
            'chat_id' => $chat->getId(),
            'text' => $menuText,
            'reply_markup' => $keyboardBuilder->createMainMenu($language),
            'parse_mode' => 'Markdown',
        ]);
    }
}
