{"name": "admin-panel", "version": "0.1.0", "private": true, "dependencies": {"@tanstack/react-query": "^5.59.0", "@tanstack/react-query-devtools": "^5.59.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/node": "^22.0.0", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "axios": "^1.11.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.26.0", "react-scripts": "5.0.1", "typescript": "^5.6.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}