from sqlalchemy import Column, Integer, BigInteger, String, Boolean, DateTime, Text, JSON, DECIMAL, ForeignKey, UniqueConstraint, Index, Float, Enum
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, validates
from sqlalchemy.sql import func
from datetime import datetime
import re

Base = declarative_base()

class User(Base):
    __tablename__ = "users"
    
    id = Column(BigInteger, primary_key=True)
    telegram_id = Column(BigInteger, unique=True, nullable=False, index=True)
    username = Column(String(255), nullable=True, index=True)
    first_name = Column(String(255), nullable=True)
    last_name = Column(String(255), nullable=True)
    language_code = Column(String(10), nullable=True)
    is_admin = Column(Boolean, default=False, index=True)
    is_premium = Column(Boolean, default=False, index=True)
    has_used_trial = Column(Boolean, default=False)
    trial_count = Column(Integer, default=0)
    last_trial_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Additional tracking fields
    is_active = Column(Boolean, default=True, index=True)
    last_active = Column(DateTime, default=func.now())
    last_seen = Column(DateTime, default=func.now())
    notification_enabled = Column(Boolean, default=True)
    total_data_used = Column(BigInteger, default=0)
    command_count = Column(Integer, default=0)

    # Referral fields
    referral_code = Column(String(50), unique=True, nullable=True, index=True)
    referred_by = Column(BigInteger, ForeignKey("users.id", ondelete='SET NULL'), nullable=True, index=True)
    referral_count = Column(Integer, default=0)
    total_referral_rewards = Column(BigInteger, default=0)

    # Relationships
    vpn_accounts = relationship("VPNAccount", back_populates="user", cascade="all, delete-orphan")
    premium_subscriptions = relationship("PremiumSubscription", back_populates="user", cascade="all, delete-orphan")
    channel_subscriptions = relationship("ChannelSubscription", back_populates="user", cascade="all, delete-orphan")

    # Referral relationships
    referrer = relationship("User", remote_side=[id], backref="referred_users")
    referrals_made = relationship("Referral", foreign_keys="Referral.referrer_id", back_populates="referrer")
    referrals_received = relationship("Referral", foreign_keys="Referral.referred_id", back_populates="referred")
    referral_codes_created = relationship("ReferralCode", back_populates="user")
    referral_rewards = relationship("ReferralReward", back_populates="user")
    
    # Indexes
    __table_args__ = (
        Index('idx_user_telegram_active', 'telegram_id', 'is_active'),
        Index('idx_user_premium_active', 'is_premium', 'is_active'),
        Index('idx_user_created_at', 'created_at'),
    )
    
    @validates('telegram_id')
    def validate_telegram_id(self, key, telegram_id):
        if telegram_id <= 0:
            raise ValueError("Telegram ID must be positive")
        return telegram_id
    
    @validates('username')
    def validate_username(self, key, username):
        if username and not re.match(r'^[a-zA-Z0-9_]{5,32}$', username):
            raise ValueError("Invalid username format")
        return username
    
    def to_dict(self):
        return {
            "id": self.id,
            "telegram_id": self.telegram_id,
            "username": self.username,
            "first_name": self.first_name,
            "last_name": self.last_name,
            "language_code": self.language_code,
            "is_premium": self.is_premium,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

class Payment(Base):
    __tablename__ = "payments"
    
    id = Column(Integer, primary_key=True)
    user_id = Column(BigInteger, ForeignKey("users.id"), nullable=False, index=True)
    plan_id = Column(Integer, ForeignKey("premium_plans.id"), nullable=True, index=True)
    amount = Column(DECIMAL(10, 2), nullable=False)
    currency = Column(String(10), default="USD")
    payment_method = Column(String(50), nullable=False)  # crypto, telegram_stars, etc.
    transaction_id = Column(String(255), unique=True, nullable=False)
    external_payment_id = Column(String(255), nullable=True)  # Payment provider ID
    status = Column(String(50), default="pending", index=True)  # pending, completed, failed, refunded
    payment_data = Column(JSON)  # Additional payment metadata
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    user = relationship("User")
    plan = relationship("PremiumPlan")
    
    # Indexes
    __table_args__ = (
        Index('idx_payment_user_status', 'user_id', 'status'),
        Index('idx_payment_created_at', 'created_at'),
    )
    
    def to_dict(self):
        return {
            "id": self.id,
            "user_id": self.user_id,
            "plan_id": self.plan_id,
            "amount": float(self.amount) if self.amount else None,
            "currency": self.currency,
            "payment_method": self.payment_method,
            "transaction_id": self.transaction_id,
            "external_payment_id": self.external_payment_id,
            "status": self.status,
            "payment_data": self.payment_data,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

class VPNUsageLog(Base):
    __tablename__ = "vpn_usage_logs"
    
    id = Column(Integer, primary_key=True)
    user_id = Column(BigInteger, ForeignKey("users.id"), nullable=False, index=True)
    vpn_account_id = Column(Integer, ForeignKey("vpn_accounts.id"), nullable=False, index=True)
    data_used = Column(BigInteger, default=0)  # bytes used in this session
    session_start = Column(DateTime, nullable=False)
    session_end = Column(DateTime, nullable=True)
    ip_address = Column(String(45), nullable=True)  # IPv4 or IPv6
    location = Column(String(255), nullable=True)  # Country/City
    device_info = Column(JSON)  # Device/client information
    created_at = Column(DateTime, default=func.now())
    
    # Relationships
    user = relationship("User")
    vpn_account = relationship("VPNAccount")
    
    # Indexes
    __table_args__ = (
        Index('idx_usage_user_date', 'user_id', 'created_at'),
        Index('idx_usage_account_date', 'vpn_account_id', 'created_at'),
        Index('idx_usage_session_start', 'session_start'),
    )
    
    def to_dict(self):
        return {
            "id": self.id,
            "user_id": self.user_id,
            "vpn_account_id": self.vpn_account_id,
            "data_used": self.data_used,
            "session_start": self.session_start.isoformat() if self.session_start else None,
            "session_end": self.session_end.isoformat() if self.session_end else None,
            "ip_address": self.ip_address,
            "location": self.location,
            "device_info": self.device_info,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

class VPNPanel(Base):
    __tablename__ = "vpn_panels"
    
    id = Column(Integer, primary_key=True)
    name = Column(String(255), unique=True, nullable=False)
    base_url = Column(String(255), nullable=False)
    api_username = Column(String(255), nullable=False)
    api_password = Column(String(255), nullable=False)
    proxies = Column(JSON)
    inbounds = Column(JSON)
    is_active = Column(Boolean, default=True, index=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Panel statistics and health
    max_users = Column(Integer, default=1000)
    current_users = Column(Integer, default=0)
    total_accounts_created = Column(Integer, default=0)
    last_health_check = Column(DateTime)
    is_healthy = Column(Boolean, default=True)
    response_time_ms = Column(Float)
    
    # Relationships
    vpn_accounts = relationship("VPNAccount", back_populates="vpn_panel", cascade="all, delete-orphan")
    
    # Indexes
    __table_args__ = (
        Index('idx_panel_active_healthy', 'is_active', 'is_healthy'),
        Index('idx_panel_created_at', 'created_at'),
    )
    
    @validates('base_url')
    def validate_base_url(self, key, base_url):
        if not base_url.startswith(('http://', 'https://')):
            raise ValueError("Base URL must start with http:// or https://")
        return base_url.rstrip('/')
    
    @validates('max_users')
    def validate_max_users(self, key, max_users):
        if max_users <= 0:
            raise ValueError("Max users must be positive")
        return max_users
    
    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "base_url": self.base_url,
            "api_username": self.api_username,
            "api_password": self.api_password,
            "proxies": self.proxies,
            "inbounds": self.inbounds,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

class VPNAccount(Base):
    __tablename__ = "vpn_accounts"
    
    id = Column(Integer, primary_key=True)
    user_id = Column(BigInteger, ForeignKey("users.id", ondelete='CASCADE'), nullable=False, index=True)
    vpn_panel_id = Column(Integer, ForeignKey("vpn_panels.id", ondelete='CASCADE'), nullable=False, index=True)
    plan_id = Column(Integer, ForeignKey("premium_plans.id"), nullable=True, index=True)
    username = Column(String(255), unique=True, nullable=False, index=True)
    uuid = Column(String(36), unique=True, nullable=True, index=True) # Marzban UUID
    data_limit = Column(BigInteger, nullable=False)  # bytes
    used_data = Column(BigInteger, default=0)  # bytes
    expire_date = Column(DateTime, nullable=True, index=True)  # Changed from expire_at to match database
    status = Column(String(50), default="active", index=True) # active, expired, disabled
    is_active = Column(Boolean, default=True, index=True)  # Changed from is_free to match database
    is_trial = Column(Boolean, default=False, index=True)  # New field for trial accounts
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Enhanced tracking
    last_usage_check = Column(DateTime)
    last_connection = Column(DateTime)
    connection_count = Column(Integer, default=0)
    config_data = Column(JSON)
    
    # Relationships
    user = relationship("User", back_populates="vpn_accounts")
    vpn_panel = relationship("VPNPanel", back_populates="vpn_accounts")
    plan = relationship("PremiumPlan", foreign_keys=[plan_id])
    
    # Indexes and constraints
    __table_args__ = (
        Index('idx_vpn_user_active', 'user_id', 'is_active'),
        Index('idx_vpn_panel_active', 'vpn_panel_id', 'is_active'),
        Index('idx_vpn_status_active', 'status', 'is_active'),
        Index('idx_vpn_expire_active', 'expire_date', 'is_active'),
        Index('idx_vpn_trial_active', 'is_trial', 'is_active'),
        UniqueConstraint('user_id', 'vpn_panel_id', 'is_trial', name='uq_user_panel_trial'),
    )
    
    @validates('status')
    def validate_status(self, key, status):
        valid_statuses = ['active', 'expired', 'disabled', 'suspended']
        if status not in valid_statuses:
            raise ValueError(f"Status must be one of: {valid_statuses}")
        return status
    
    @validates('data_limit')
    def validate_data_limit(self, key, data_limit):
        if data_limit is not None and data_limit <= 0:
            raise ValueError("Data limit must be positive")
        return data_limit
    
    @property
    def usage_percentage(self):
        if not self.data_limit:
            return 0
        return min(100, (self.used_data / self.data_limit) * 100)
    
    @property
    def is_expired(self):
        if not self.expire_date:
            return False
        return datetime.now() > self.expire_date

    @property
    def days_until_expiry(self):
        if not self.expire_date:
            return None
        delta = self.expire_date - datetime.now()
        return max(0, delta.days)
    
    def to_dict(self):
        return {
            "id": self.id,
            "user_id": self.user_id,
            "vpn_panel_id": self.vpn_panel_id,
            "username": self.username,
            "uuid": self.uuid,
            "data_limit": self.data_limit,
            "used_data": self.used_data,
            "expire_date": self.expire_date.isoformat() if self.expire_date else None,
            "status": self.status,
            "is_active": self.is_active,
            "is_trial": self.is_trial,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

class Channel(Base):
    __tablename__ = "channels"

    id = Column(Integer, primary_key=True)
    channel_id = Column(String(255), unique=True, nullable=False) # Telegram channel ID or username
    channel_name = Column(String(255), nullable=False)
    channel_url = Column(Text, nullable=True)
    invite_link = Column(Text, nullable=True)  # Invite link for the channel
    is_required = Column(Boolean, default=True)
    is_active = Column(Boolean, default=True)  # Enable/disable channel
    priority = Column(Integer, default=0)  # Display priority (lower = higher priority)
    description = Column(Text, nullable=True)  # Channel description for advertising
    subscriber_count = Column(Integer, default=0)  # Track subscriber count
    advertising_enabled = Column(Boolean, default=False)  # Enable advertising for this channel
    advertising_message = Column(Text, nullable=True)  # Custom advertising message
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    subscriptions = relationship("ChannelSubscription", back_populates="channel")

    def to_dict(self):
        return {
            "id": self.id,
            "channel_id": self.channel_id,
            "channel_name": self.channel_name,
            "channel_url": self.channel_url,
            "invite_link": self.invite_link,
            "is_required": self.is_required,
            "is_active": self.is_active,
            "priority": self.priority,
            "description": self.description,
            "subscriber_count": self.subscriber_count,
            "advertising_enabled": self.advertising_enabled,
            "advertising_message": self.advertising_message,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

class ChannelSubscription(Base):
    __tablename__ = "channel_subscriptions"

    id = Column(Integer, primary_key=True)
    user_id = Column(BigInteger, ForeignKey("users.id", ondelete='CASCADE'), nullable=False, index=True)
    channel_id = Column(String(255), nullable=False, index=True)  # Changed to match Telegram channel ID format
    status = Column(Enum('active', 'inactive', 'pending', name='subscription_status'), default='pending', index=True)
    subscribed_at = Column(DateTime, nullable=True)
    unsubscribed_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="channel_subscriptions")

    __table_args__ = (
        UniqueConstraint('user_id', 'channel_id', name='unique_user_channel'),
        Index('idx_user_channel_status', 'user_id', 'channel_id', 'status'),
    )

class PremiumPlan(Base):
    __tablename__ = "premium_plans"
    
    id = Column(Integer, primary_key=True)
    name = Column(String(255), unique=True, nullable=False)
    description = Column(Text)
    price = Column(DECIMAL(10, 2), nullable=False)
    duration_days = Column(Integer, nullable=False)
    data_limit = Column(BigInteger, nullable=False)  # bytes
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    subscriptions = relationship("PremiumSubscription", back_populates="plan")
    
    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "price": float(self.price) if self.price else None,
            "duration_days": self.duration_days,
            "data_limit": self.data_limit,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

class PremiumSubscription(Base):
    __tablename__ = "premium_subscriptions"
    
    id = Column(Integer, primary_key=True)
    user_id = Column(BigInteger, ForeignKey("users.id"), nullable=False)
    plan_id = Column(Integer, ForeignKey("premium_plans.id"), nullable=False)
    transaction_id = Column(String(255), unique=True, nullable=False)
    amount = Column(DECIMAL(10, 2), nullable=False)
    status = Column(String(50), default="pending") # pending, completed, failed, refunded
    started_at = Column(DateTime, nullable=True)
    expires_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="premium_subscriptions")
    plan = relationship("PremiumPlan", back_populates="subscriptions") 

class BotSettings(Base):
    __tablename__ = "bot_settings"
    
    id = Column(Integer, primary_key=True)
    free_data_limit = Column(BigInteger, nullable=False)
    free_duration_days = Column(Integer, nullable=False)
    required_channels = Column(JSON)
    payment_provider_token = Column(String(255))
    trial_vpn_panel_id = Column(Integer, ForeignKey("vpn_panels.id"), nullable=True)
    max_trials_per_user = Column(Integer, default=1)
    trial_reset_days = Column(Integer, default=30)
    auto_notify_trial_reset = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    trial_vpn_panel = relationship("VPNPanel", foreign_keys=[trial_vpn_panel_id])


class Referral(Base):
    __tablename__ = "referrals"

    id = Column(Integer, primary_key=True)
    referrer_id = Column(BigInteger, ForeignKey("users.id", ondelete='CASCADE'), nullable=False, index=True)
    referred_id = Column(BigInteger, ForeignKey("users.id", ondelete='CASCADE'), nullable=False, index=True)
    referral_code = Column(String(50), nullable=False, index=True)
    status = Column(Enum('pending', 'completed', 'rewarded', name='referral_status'), default='pending', index=True)
    reward_type = Column(Enum('data', 'time', 'premium', 'trial_reset', name='reward_type'), default='data')
    reward_amount = Column(BigInteger, default=0)
    reward_given_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    referrer = relationship("User", foreign_keys=[referrer_id], back_populates="referrals_made")
    referred = relationship("User", foreign_keys=[referred_id], back_populates="referrals_received")

    # Unique constraint
    __table_args__ = (
        UniqueConstraint('referrer_id', 'referred_id', name='unique_referral'),
    )

    def to_dict(self):
        return {
            "id": self.id,
            "referrer_id": self.referrer_id,
            "referred_id": self.referred_id,
            "referral_code": self.referral_code,
            "status": self.status,
            "reward_type": self.reward_type,
            "reward_amount": self.reward_amount,
            "reward_given_at": self.reward_given_at.isoformat() if self.reward_given_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }


class ReferralCode(Base):
    __tablename__ = "referral_codes"

    id = Column(Integer, primary_key=True)
    user_id = Column(BigInteger, ForeignKey("users.id", ondelete='CASCADE'), nullable=False, index=True)
    code = Column(String(50), unique=True, nullable=False, index=True)
    uses_count = Column(Integer, default=0)
    max_uses = Column(Integer, default=0)  # 0 = unlimited
    is_active = Column(Boolean, default=True, index=True)
    expires_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="referral_codes_created")

    def to_dict(self):
        return {
            "id": self.id,
            "user_id": self.user_id,
            "code": self.code,
            "uses_count": self.uses_count,
            "max_uses": self.max_uses,
            "is_active": self.is_active,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }


class ReferralReward(Base):
    __tablename__ = "referral_rewards"

    id = Column(Integer, primary_key=True)
    user_id = Column(BigInteger, ForeignKey("users.id", ondelete='CASCADE'), nullable=False, index=True)
    referral_id = Column(Integer, ForeignKey("referrals.id", ondelete='CASCADE'), nullable=False, index=True)
    reward_type = Column(Enum('data', 'time', 'premium', 'trial_reset', name='reward_type'), nullable=False)
    reward_amount = Column(BigInteger, nullable=False)
    description = Column(Text, nullable=True)
    is_claimed = Column(Boolean, default=False, index=True)
    claimed_at = Column(DateTime, nullable=True)
    expires_at = Column(DateTime, nullable=True, index=True)
    created_at = Column(DateTime, default=func.now())

    # Relationships
    user = relationship("User", back_populates="referral_rewards")
    referral = relationship("Referral")

    def to_dict(self):
        return {
            "id": self.id,
            "user_id": self.user_id,
            "referral_id": self.referral_id,
            "reward_type": self.reward_type,
            "reward_amount": self.reward_amount,
            "description": self.description,
            "is_claimed": self.is_claimed,
            "claimed_at": self.claimed_at.isoformat() if self.claimed_at else None,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }
    
    def to_dict(self):
        return {
            "id": self.id,
            "free_data_limit": self.free_data_limit,
            "free_duration_days": self.free_duration_days,
            "required_channels": self.required_channels,
            "payment_provider_token": self.payment_provider_token,
            "trial_vpn_panel_id": self.trial_vpn_panel_id,
            "max_trials_per_user": self.max_trials_per_user,
            "trial_reset_days": self.trial_reset_days,
            "auto_notify_trial_reset": self.auto_notify_trial_reset,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }