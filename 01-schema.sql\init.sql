-- Telegram VPN Bot Database Schema
-- Optimized for PostgreSQL 16+

-- Create extensions if needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    telegram_id BIGINT UNIQUE NOT NULL,
    username <PERSON><PERSON><PERSON><PERSON>(255),
    first_name <PERSON><PERSON><PERSON><PERSON>(255),
    last_name <PERSON><PERSON><PERSON><PERSON>(255),
    language_code VA<PERSON><PERSON><PERSON>(10),
    is_premium BOOLEAN DEFAULT FALSE,
    has_used_trial BOOLEAN DEFAULT FALSE,
    trial_count INTEGER DEFAULT 0,
    last_trial_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    -- Additional tracking fields
    is_active BOOLEAN DEFAULT TRUE,
    last_active TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notification_enabled BOOLEAN DEFAULT TRUE,
    total_data_used BIGINT DEFAULT 0,
    command_count INTEGER DEFAULT 0
);

-- Create indexes for users
CREATE INDEX idx_users_telegram_id ON users(telegram_id);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_is_premium ON users(is_premium);
CREATE INDEX idx_user_telegram_active ON users(telegram_id, is_active);
CREATE INDEX idx_user_premium_active ON users(is_premium, is_active);
CREATE INDEX idx_user_created_at ON users(created_at);

-- VPN Panels table
CREATE TABLE vpn_panels (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    base_url VARCHAR(255) NOT NULL,
    api_username VARCHAR(255) NOT NULL,
    api_password VARCHAR(255) NOT NULL,
    proxies JSONB,
    inbounds JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    -- Panel statistics and health
    max_users INTEGER DEFAULT 1000,
    current_users INTEGER DEFAULT 0,
    total_accounts_created INTEGER DEFAULT 0,
    last_health_check TIMESTAMP,
    is_healthy BOOLEAN DEFAULT TRUE,
    response_time_ms REAL
);

-- Create indexes for vpn_panels
CREATE INDEX idx_panel_active_healthy ON vpn_panels(is_active, is_healthy);
CREATE INDEX idx_panel_created_at ON vpn_panels(created_at);

-- VPN Accounts table
CREATE TABLE vpn_accounts (
    id SERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    vpn_panel_id INTEGER NOT NULL REFERENCES vpn_panels(id) ON DELETE CASCADE,
    username VARCHAR(255) UNIQUE NOT NULL,
    uuid VARCHAR(36) UNIQUE,
    data_limit BIGINT NOT NULL,
    used_data BIGINT DEFAULT 0,
    expire_date TIMESTAMP,
    status VARCHAR(50) DEFAULT 'active',
    is_active BOOLEAN DEFAULT TRUE,
    is_trial BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    -- Enhanced tracking
    last_usage_check TIMESTAMP,
    last_connection TIMESTAMP,
    connection_count INTEGER DEFAULT 0,
    config_data JSONB
);

-- Create indexes for vpn_accounts
CREATE INDEX idx_vpn_user_id ON vpn_accounts(user_id);
CREATE INDEX idx_vpn_username ON vpn_accounts(username);
CREATE INDEX idx_vpn_uuid ON vpn_accounts(uuid);
CREATE INDEX idx_vpn_expire_date ON vpn_accounts(expire_date);
CREATE INDEX idx_vpn_status ON vpn_accounts(status);
CREATE INDEX idx_vpn_user_active ON vpn_accounts(user_id, is_active);
CREATE INDEX idx_vpn_panel_active ON vpn_accounts(vpn_panel_id, is_active);
CREATE INDEX idx_vpn_status_active ON vpn_accounts(status, is_active);
CREATE INDEX idx_vpn_expire_active ON vpn_accounts(expire_date, is_active);
CREATE INDEX idx_vpn_trial_active ON vpn_accounts(is_trial, is_active);

-- Add unique constraint
ALTER TABLE vpn_accounts ADD CONSTRAINT uq_user_panel_trial UNIQUE (user_id, vpn_panel_id, is_trial);

-- Channels table
CREATE TABLE channels (
    id SERIAL PRIMARY KEY,
    channel_id VARCHAR(255) UNIQUE NOT NULL, -- Telegram internal channel ID or username
    channel_name VARCHAR(255) NOT NULL,
    channel_url TEXT,
    description TEXT,
    is_required BOOLEAN DEFAULT TRUE,
    is_active BOOLEAN DEFAULT TRUE,
    is_advertising_enabled BOOLEAN DEFAULT FALSE,
    advertising_message TEXT,
    advertising_frequency INTEGER DEFAULT 24, -- hours
    last_advertised_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for channels table
CREATE INDEX idx_channels_channel_id ON channels(channel_id);
CREATE INDEX idx_channels_is_required ON channels(is_required);
CREATE INDEX idx_channels_is_active ON channels(is_active);
CREATE INDEX idx_channels_is_advertising_enabled ON channels(is_advertising_enabled);

-- Channel Subscriptions table
CREATE TABLE channel_subscriptions (
    id SERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    channel_id INTEGER NOT NULL REFERENCES channels(id) ON DELETE CASCADE,
    is_subscribed BOOLEAN DEFAULT FALSE,
    verified_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, channel_id)
);

-- Premium Plans table
CREATE TABLE premium_plans (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    price DECIMAL(10, 2) NOT NULL,
    duration_days INTEGER NOT NULL,
    data_limit BIGINT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Premium Subscriptions table
CREATE TABLE premium_subscriptions (
    id SERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    plan_id INTEGER NOT NULL REFERENCES premium_plans(id) ON DELETE RESTRICT,
    transaction_id VARCHAR(255) UNIQUE NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    started_at TIMESTAMP,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- VPN Usage Logs table
CREATE TABLE vpn_usage_logs (
    id SERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    vpn_account_id INTEGER NOT NULL REFERENCES vpn_accounts(id) ON DELETE CASCADE,
    data_used_bytes BIGINT DEFAULT 0,
    session_start TIMESTAMP,
    session_end TIMESTAMP,
    ip_address VARCHAR(45),
    location VARCHAR(255),
    device_info JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for vpn_usage_logs
CREATE INDEX idx_usage_user_id ON vpn_usage_logs(user_id);
CREATE INDEX idx_usage_account_id ON vpn_usage_logs(vpn_account_id);
CREATE INDEX idx_usage_created_at ON vpn_usage_logs(created_at);
CREATE INDEX idx_usage_user_date ON vpn_usage_logs(user_id, created_at);

-- Payments table
CREATE TABLE payments (
    id SERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    plan_id INTEGER REFERENCES premium_plans(id) ON DELETE SET NULL,
    amount DECIMAL(10, 2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'USD',
    payment_method VARCHAR(50) NOT NULL,
    transaction_id VARCHAR(255) UNIQUE NOT NULL,
    external_payment_id VARCHAR(255),
    telegram_payment_charge_id VARCHAR(255),
    provider_payment_charge_id VARCHAR(255),
    payload TEXT,
    status VARCHAR(50) DEFAULT 'pending',
    payment_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for payments
CREATE INDEX idx_payments_user_id ON payments(user_id);
CREATE INDEX idx_payments_status ON payments(status);
CREATE INDEX idx_payments_created_at ON payments(created_at);

-- Crypto Payments table
CREATE TABLE crypto_payments (
    id SERIAL PRIMARY KEY,
    payment_id VARCHAR(255) UNIQUE NOT NULL,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    plan_id INTEGER REFERENCES premium_plans(id) ON DELETE SET NULL,
    order_id VARCHAR(255) NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'USD',
    pay_currency VARCHAR(10) NOT NULL,
    pay_amount DECIMAL(20, 8) NOT NULL,
    status VARCHAR(50) DEFAULT 'waiting',
    payment_url TEXT,
    pay_address VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for crypto_payments
CREATE INDEX idx_crypto_payments_user_id ON crypto_payments(user_id);
CREATE INDEX idx_crypto_payments_status ON crypto_payments(status);
CREATE INDEX idx_crypto_payments_payment_id ON crypto_payments(payment_id);

-- Bot Settings table
CREATE TABLE bot_settings (
    id SERIAL PRIMARY KEY,
    free_data_limit BIGINT NOT NULL,
    free_duration_days INTEGER NOT NULL,
    required_channels JSONB,
    payment_provider_token VARCHAR(255),
    trial_vpn_panel_id INTEGER REFERENCES vpn_panels(id) ON DELETE SET NULL,
    max_trials_per_user INTEGER DEFAULT 1,
    trial_reset_days INTEGER DEFAULT 30,
    auto_notify_trial_reset BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_vpn_panels_updated_at BEFORE UPDATE ON vpn_panels FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_vpn_accounts_updated_at BEFORE UPDATE ON vpn_accounts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_channels_updated_at BEFORE UPDATE ON channels FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_premium_plans_updated_at BEFORE UPDATE ON premium_plans FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_premium_subscriptions_updated_at BEFORE UPDATE ON premium_subscriptions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_vpn_usage_logs_updated_at BEFORE UPDATE ON vpn_usage_logs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_payments_updated_at BEFORE UPDATE ON payments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_crypto_payments_updated_at BEFORE UPDATE ON crypto_payments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_bot_settings_updated_at BEFORE UPDATE ON bot_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default bot settings
INSERT INTO bot_settings (free_data_limit, free_duration_days, required_channels, max_trials_per_user, trial_reset_days, auto_notify_trial_reset)
VALUES (1073741824, 30, '[]'::jsonb, 1, 30, true);