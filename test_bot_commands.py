#!/usr/bin/env python3
"""
Comprehensive bot command testing script.
Tests all bot commands and functionality to ensure production readiness.
"""

import asyncio
import logging
import sys
import os
from typing import Dict, Any, List
import traceback

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot_command_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class BotCommandTester:
    """Comprehensive bot command testing class."""
    
    def __init__(self):
        self.results = {}
        self.errors = []
    
    async def run_all_tests(self):
        """Run all bot command tests."""
        logger.info("🚀 Starting comprehensive bot command testing...")
        
        tests = [
            ("Import Tests", self.test_imports),
            ("Command Handler Registration", self.test_command_registration),
            ("Localization Keys", self.test_localization_keys),
            ("Payment Handler Integration", self.test_payment_handlers),
            ("Referral System Integration", self.test_referral_integration),
            ("Database Models", self.test_database_models),
            ("Service Integration", self.test_service_integration),
            ("Error Handling", self.test_error_handling),
            ("Bot Configuration", self.test_bot_configuration),
        ]
        
        for test_name, test_func in tests:
            logger.info(f"🔍 Running: {test_name}")
            try:
                result = await test_func()
                self.results[test_name] = result
                if result.get('success', False):
                    logger.info(f"✅ {test_name}: PASSED")
                else:
                    logger.error(f"❌ {test_name}: FAILED - {result.get('error', 'Unknown error')}")
                    self.errors.append(f"{test_name}: {result.get('error', 'Unknown error')}")
            except Exception as e:
                error_msg = f"Exception in {test_name}: {str(e)}"
                logger.error(f"💥 {error_msg}")
                self.errors.append(error_msg)
                self.results[test_name] = {'success': False, 'error': str(e)}
        
        # Generate summary report
        await self.generate_report()
    
    async def test_imports(self) -> Dict[str, Any]:
        """Test all critical imports."""
        try:
            import_tests = [
                ('bot.main', 'Main bot module'),
                ('bot.handlers.commands', 'Command handlers'),
                ('bot.handlers.payments', 'Payment handlers'),
                ('bot.handlers.referral', 'Referral handlers'),
                ('bot.services.payment_service', 'Payment service'),
                ('bot.services.referral_service', 'Referral service'),
                ('bot.utils.helpers', 'Helper utilities'),
                ('bot.utils.buttons', 'Button utilities'),
                ('bot.models', 'Database models'),
                ('bot.config', 'Configuration'),
                ('bot.database', 'Database connection'),
            ]
            
            successful_imports = []
            failed_imports = []
            
            for module_name, description in import_tests:
                try:
                    __import__(module_name)
                    successful_imports.append(f"{description} ({module_name})")
                except Exception as e:
                    failed_imports.append(f"{description} ({module_name}): {str(e)}")
            
            return {
                'success': len(failed_imports) == 0,
                'details': {
                    'successful_imports': successful_imports,
                    'failed_imports': failed_imports
                },
                'error': f"Failed imports: {failed_imports}" if failed_imports else None
            }
        except Exception as e:
            return {'success': False, 'error': f"Import test error: {str(e)}"}
    
    async def test_command_registration(self) -> Dict[str, Any]:
        """Test command handler registration."""
        try:
            from bot.handlers.commands import CommandHandlers
            from bot.handlers.payments import PaymentHandler
            from bot.handlers.referral import referral_handler
            
            # Test command handler instantiation
            command_handlers = CommandHandlers()
            payment_handler = PaymentHandler()
            
            # Check if handlers have required methods
            required_methods = [
                'start_command', 'menu_command', 'handle_reply_keyboard'
            ]
            
            missing_methods = []
            for method in required_methods:
                if not hasattr(command_handlers, method):
                    missing_methods.append(f"CommandHandlers.{method}")
            
            # Check payment handler methods
            payment_methods = ['handle_successful_payment', 'handle_pre_checkout_query']
            for method in payment_methods:
                if not hasattr(payment_handler, method):
                    missing_methods.append(f"PaymentHandler.{method}")
            
            # Check referral handler methods
            referral_methods = ['handle_referral_command', 'handle_referral_link_command']
            for method in referral_methods:
                if not hasattr(referral_handler, method):
                    missing_methods.append(f"ReferralHandler.{method}")
            
            return {
                'success': len(missing_methods) == 0,
                'details': {
                    'handlers_tested': ['CommandHandlers', 'PaymentHandler', 'ReferralHandler'],
                    'missing_methods': missing_methods
                },
                'error': f"Missing methods: {missing_methods}" if missing_methods else None
            }
        except Exception as e:
            return {'success': False, 'error': f"Command registration test error: {str(e)}"}
    
    async def test_localization_keys(self) -> Dict[str, Any]:
        """Test localization keys for completeness."""
        try:
            from bot.utils.helpers import get_text
            
            # Test critical localization keys
            critical_keys = [
                'welcome.title',
                'welcome.description',
                'buttons.trial_vpn',
                'buttons.premium',
                'buttons.main_menu',
                'buttons.back',
                'errors.general_error',
                'errors.auth_required',
                'payment.price',
                'payment.duration',
                'dashboard.title',
                'help.commands_title',
                'vpn.use_account_details',
                'referral.stats_header',
                'trial.success',
                'premium.title'
            ]
            
            languages = ['en', 'fa', 'ru', 'zh']
            missing_keys = []
            
            for lang in languages:
                for key in critical_keys:
                    try:
                        text = get_text(key, lang)
                        if not text or text.startswith('Translation error'):
                            missing_keys.append(f"{lang}.{key}")
                    except Exception as e:
                        missing_keys.append(f"{lang}.{key}: {str(e)}")
            
            return {
                'success': len(missing_keys) == 0,
                'details': {
                    'languages_tested': languages,
                    'keys_tested': len(critical_keys),
                    'missing_keys': missing_keys
                },
                'error': f"Missing localization keys: {missing_keys}" if missing_keys else None
            }
        except Exception as e:
            return {'success': False, 'error': f"Localization test error: {str(e)}"}
    
    async def test_payment_handlers(self) -> Dict[str, Any]:
        """Test payment handler integration."""
        try:
            from bot.handlers.payments import PaymentHandler
            from bot.services.payment_service import payment_service
            
            # Test payment handler instantiation
            payment_handler = PaymentHandler()
            
            # Test payment service methods
            service_methods = [
                'create_invoice', 'process_successful_payment', 
                'validate_payment_data', 'get_payment_status'
            ]
            
            missing_service_methods = []
            for method in service_methods:
                if not hasattr(payment_service, method):
                    missing_service_methods.append(f"payment_service.{method}")
            
            # Test handler methods
            handler_methods = [
                'handle_successful_payment', 'handle_pre_checkout_query',
                'handle_stars_payment'
            ]
            
            missing_handler_methods = []
            for method in handler_methods:
                if not hasattr(payment_handler, method):
                    missing_handler_methods.append(f"PaymentHandler.{method}")
            
            all_missing = missing_service_methods + missing_handler_methods
            
            return {
                'success': len(all_missing) == 0,
                'details': {
                    'service_methods_tested': service_methods,
                    'handler_methods_tested': handler_methods,
                    'missing_methods': all_missing
                },
                'error': f"Missing payment methods: {all_missing}" if all_missing else None
            }
        except Exception as e:
            return {'success': False, 'error': f"Payment handler test error: {str(e)}"}
    
    async def test_referral_integration(self) -> Dict[str, Any]:
        """Test referral system integration."""
        try:
            from bot.handlers.referral import referral_handler
            from bot.services.referral_service import referral_service
            from bot.services.referral_analytics_service import referral_analytics_service
            
            # Test referral service methods
            service_methods = [
                'generate_referral_code', 'process_referral', 
                'complete_referral', 'get_user_referral_stats'
            ]
            
            missing_service_methods = []
            for method in service_methods:
                if not hasattr(referral_service, method):
                    missing_service_methods.append(f"referral_service.{method}")
            
            # Test analytics service methods
            analytics_methods = [
                'get_comprehensive_analytics', 'get_referral_leaderboard'
            ]
            
            missing_analytics_methods = []
            for method in analytics_methods:
                if not hasattr(referral_analytics_service, method):
                    missing_analytics_methods.append(f"referral_analytics_service.{method}")
            
            # Test handler methods
            handler_methods = [
                'handle_referral_command', 'handle_referral_link_command',
                'process_referral_start'
            ]
            
            missing_handler_methods = []
            for method in handler_methods:
                if not hasattr(referral_handler, method):
                    missing_handler_methods.append(f"referral_handler.{method}")
            
            all_missing = missing_service_methods + missing_analytics_methods + missing_handler_methods
            
            return {
                'success': len(all_missing) == 0,
                'details': {
                    'service_methods_tested': service_methods,
                    'analytics_methods_tested': analytics_methods,
                    'handler_methods_tested': handler_methods,
                    'missing_methods': all_missing
                },
                'error': f"Missing referral methods: {all_missing}" if all_missing else None
            }
        except Exception as e:
            return {'success': False, 'error': f"Referral integration test error: {str(e)}"}
    
    async def test_database_models(self) -> Dict[str, Any]:
        """Test database model integrity."""
        try:
            from bot.models import User, VPNAccount, Channel, VPNPanel, PremiumPlan
            from bot.models import Referral, ReferralCode, ReferralReward
            
            # Test model instantiation
            models_tested = []
            model_errors = []
            
            try:
                user = User(telegram_id=*********, first_name="Test User")
                models_tested.append("User")
            except Exception as e:
                model_errors.append(f"User model: {str(e)}")
            
            try:
                vpn_account = VPNAccount(
                    user_id=1, vpn_panel_id=1, username="test", data_limit=**********
                )
                models_tested.append("VPNAccount")
            except Exception as e:
                model_errors.append(f"VPNAccount model: {str(e)}")
            
            try:
                channel = Channel(channel_id="@test", channel_name="Test Channel")
                models_tested.append("Channel")
            except Exception as e:
                model_errors.append(f"Channel model: {str(e)}")
            
            return {
                'success': len(model_errors) == 0,
                'details': {
                    'models_tested': models_tested,
                    'model_errors': model_errors
                },
                'error': f"Model errors: {model_errors}" if model_errors else None
            }
        except Exception as e:
            return {'success': False, 'error': f"Database model test error: {str(e)}"}
    
    async def test_service_integration(self) -> Dict[str, Any]:
        """Test service layer integration."""
        try:
            services = [
                ('bot.services.auth_service', 'Authentication service'),
                ('bot.services.channel_service', 'Channel service'),
                ('bot.services.vpn_service', 'VPN service'),
                ('bot.services.dashboard_service', 'Dashboard service'),
                ('bot.services.advertising_service', 'Advertising service'),
            ]
            
            successful_services = []
            failed_services = []
            
            for service_module, description in services:
                try:
                    module = __import__(service_module, fromlist=[''])
                    successful_services.append(description)
                except Exception as e:
                    failed_services.append(f"{description}: {str(e)}")
            
            return {
                'success': len(failed_services) == 0,
                'details': {
                    'successful_services': successful_services,
                    'failed_services': failed_services
                },
                'error': f"Failed services: {failed_services}" if failed_services else None
            }
        except Exception as e:
            return {'success': False, 'error': f"Service integration test error: {str(e)}"}
    
    async def test_error_handling(self) -> Dict[str, Any]:
        """Test error handling mechanisms."""
        try:
            from bot.handlers.errors import error_handler
            from bot.utils.helpers import get_text
            
            # Test error handler exists
            if not hasattr(error_handler, 'handle_error'):
                return {
                    'success': False,
                    'error': 'Error handler missing handle_error method'
                }
            
            # Test error localization keys
            error_keys = [
                'errors.general_error',
                'errors.auth_required',
                'errors.unknown_command',
                'errors.payment_error'
            ]
            
            missing_error_keys = []
            for key in error_keys:
                try:
                    text = get_text(key, 'en')
                    if not text or text.startswith('Translation error'):
                        missing_error_keys.append(key)
                except:
                    missing_error_keys.append(key)
            
            return {
                'success': len(missing_error_keys) == 0,
                'details': {
                    'error_handler_exists': True,
                    'error_keys_tested': error_keys,
                    'missing_error_keys': missing_error_keys
                },
                'error': f"Missing error keys: {missing_error_keys}" if missing_error_keys else None
            }
        except Exception as e:
            return {'success': False, 'error': f"Error handling test error: {str(e)}"}
    
    async def test_bot_configuration(self) -> Dict[str, Any]:
        """Test bot configuration completeness."""
        try:
            from bot.config import settings
            
            required_settings = [
                'BOT_TOKEN', 'POSTGRES_HOST', 'POSTGRES_USER', 
                'POSTGRES_PASSWORD', 'POSTGRES_DATABASE'
            ]
            
            missing_settings = []
            configured_settings = []
            
            for setting in required_settings:
                if hasattr(settings, setting) and getattr(settings, setting):
                    configured_settings.append(setting)
                else:
                    missing_settings.append(setting)
            
            return {
                'success': len(missing_settings) == 0,
                'details': {
                    'configured_settings': configured_settings,
                    'missing_settings': missing_settings
                },
                'error': f"Missing settings: {missing_settings}" if missing_settings else None
            }
        except Exception as e:
            return {'success': False, 'error': f"Bot configuration test error: {str(e)}"}
    
    async def generate_report(self):
        """Generate a comprehensive testing report."""
        logger.info("\n" + "="*80)
        logger.info("🔍 COMPREHENSIVE BOT COMMAND TESTING REPORT")
        logger.info("="*80)
        
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results.values() if r.get('success', False))
        failed_tests = total_tests - passed_tests
        
        logger.info(f"📊 SUMMARY: {passed_tests}/{total_tests} tests passed")
        
        if failed_tests > 0:
            logger.error(f"❌ FAILED TESTS ({failed_tests}):")
            for error in self.errors:
                logger.error(f"   • {error}")
        
        logger.info("\n📋 DETAILED RESULTS:")
        for test_name, result in self.results.items():
            status = "✅ PASS" if result.get('success', False) else "❌ FAIL"
            logger.info(f"   {status} {test_name}")
            if result.get('details'):
                for key, value in result['details'].items():
                    if isinstance(value, list) and len(value) > 3:
                        logger.info(f"      {key}: {len(value)} items")
                    else:
                        logger.info(f"      {key}: {value}")
        
        logger.info("\n" + "="*80)
        
        if failed_tests == 0:
            logger.info("🎉 ALL TESTS PASSED! The bot is ready for production deployment.")
        else:
            logger.error(f"⚠️  {failed_tests} ISSUES FOUND. Please review the errors above.")
        
        logger.info("="*80)


async def main():
    """Main function to run the bot command testing suite."""
    tester = BotCommandTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
