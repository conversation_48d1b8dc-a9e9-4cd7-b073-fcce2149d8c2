-- Create database tables for PHP VPN Bot

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    telegram_id BIGINT UNIQUE NOT NULL,
    username VA<PERSON><PERSON><PERSON>(255),
    first_name <PERSON><PERSON><PERSON><PERSON>(255),
    last_name <PERSON><PERSON><PERSON><PERSON>(255),
    language_code VARCHAR(10) DEFAULT 'en',
    is_admin BOOLEAN DEFAULT FALSE,
    is_premium BOOLEAN DEFAULT FALSE,
    has_used_trial BOOLEAN DEFAULT FALSE,
    trial_count INTEGER DEFAULT 0,
    last_trial_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    last_active TIMESTAMP,
    last_seen TIMESTAMP,
    notification_enabled BOOLEAN DEFAULT TRUE,
    total_data_used BIGINT DEFAULT 0,
    command_count INTEGER DEFAULT 0,
    referral_code VARCHAR(255) UNIQUE,
    referred_by INTEGER REFERENCES users(id),
    referral_count INTEGER DEFAULT 0,
    total_referral_rewards INTEGER DEFAULT 0
);

-- VPN Panels table
CREATE TABLE IF NOT EXISTS vpn_panels (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    base_url VARCHAR(500) NOT NULL,
    api_username VARCHAR(255) NOT NULL,
    api_password VARCHAR(255) NOT NULL,
    api_token TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    max_users INTEGER DEFAULT 1000,
    current_users INTEGER DEFAULT 0,
    panel_type VARCHAR(50) DEFAULT 'marzban',
    settings JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Premium Plans table
CREATE TABLE IF NOT EXISTS premium_plans (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'USD',
    duration_days INTEGER NOT NULL,
    data_limit BIGINT NOT NULL,
    max_connections INTEGER DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    features JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- VPN Accounts table
CREATE TABLE IF NOT EXISTS vpn_accounts (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    vpn_panel_id INTEGER NOT NULL REFERENCES vpn_panels(id),
    plan_id INTEGER REFERENCES premium_plans(id),
    username VARCHAR(255) UNIQUE NOT NULL,
    uuid VARCHAR(255),
    data_limit BIGINT NOT NULL,
    used_data BIGINT DEFAULT 0,
    expire_date TIMESTAMP,
    status VARCHAR(50) DEFAULT 'active',
    is_active BOOLEAN DEFAULT TRUE,
    is_trial BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_usage_check TIMESTAMP,
    last_connection TIMESTAMP,
    connection_count INTEGER DEFAULT 0,
    config_data JSONB
);

-- Channels table
CREATE TABLE IF NOT EXISTS channels (
    id SERIAL PRIMARY KEY,
    channel_id VARCHAR(255) UNIQUE NOT NULL,
    channel_name VARCHAR(255) NOT NULL,
    channel_url VARCHAR(500),
    invite_link VARCHAR(500),
    is_required BOOLEAN DEFAULT TRUE,
    is_active BOOLEAN DEFAULT TRUE,
    priority INTEGER DEFAULT 0,
    description TEXT,
    subscriber_count INTEGER DEFAULT 0,
    advertising_enabled BOOLEAN DEFAULT FALSE,
    advertising_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Payments table
CREATE TABLE IF NOT EXISTS payments (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    plan_id INTEGER REFERENCES premium_plans(id),
    payment_id VARCHAR(255) UNIQUE NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'USD',
    payment_method VARCHAR(50) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    provider_payment_id VARCHAR(255),
    payment_data JSONB,
    paid_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Referrals table
CREATE TABLE IF NOT EXISTS referrals (
    id SERIAL PRIMARY KEY,
    referrer_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    referred_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    referral_code VARCHAR(255) NOT NULL,
    status VARCHAR(50) DEFAULT 'active',
    reward_amount DECIMAL(10,2) DEFAULT 0.00,
    reward_paid BOOLEAN DEFAULT FALSE,
    reward_paid_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(referrer_id, referred_id)
);

-- User Sessions table (for state management)
CREATE TABLE IF NOT EXISTS user_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_data JSONB,
    current_state VARCHAR(100),
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id)
);

-- Bot Statistics table
CREATE TABLE IF NOT EXISTS bot_statistics (
    id SERIAL PRIMARY KEY,
    date DATE UNIQUE NOT NULL,
    total_users INTEGER DEFAULT 0,
    new_users INTEGER DEFAULT 0,
    active_users INTEGER DEFAULT 0,
    premium_users INTEGER DEFAULT 0,
    trial_users INTEGER DEFAULT 0,
    total_commands INTEGER DEFAULT 0,
    total_payments DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_telegram_id ON users(telegram_id);
CREATE INDEX IF NOT EXISTS idx_users_referral_code ON users(referral_code);
CREATE INDEX IF NOT EXISTS idx_vpn_accounts_user_id ON vpn_accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_vpn_accounts_username ON vpn_accounts(username);
CREATE INDEX IF NOT EXISTS idx_payments_user_id ON payments(user_id);
CREATE INDEX IF NOT EXISTS idx_payments_payment_id ON payments(payment_id);
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);
CREATE INDEX IF NOT EXISTS idx_referrals_referrer_id ON referrals(referrer_id);
CREATE INDEX IF NOT EXISTS idx_referrals_referred_id ON referrals(referred_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_channels_channel_id ON channels(channel_id);
CREATE INDEX IF NOT EXISTS idx_channels_is_required ON channels(is_required, is_active);

-- Insert default data
INSERT INTO premium_plans (name, description, price, duration_days, data_limit, features) VALUES
('1 Month Premium', '1 Month Premium VPN Access', 9.99, 30, ************, '{"high_speed": true, "support": "24/7"}'),
('3 Months Premium', '3 Months Premium VPN Access', 24.99, 90, ************, '{"high_speed": true, "support": "24/7", "discount": "17%"}'),
('6 Months Premium', '6 Months Premium VPN Access', 44.99, 180, 644245094400, '{"high_speed": true, "support": "24/7", "discount": "25%"}'),
('1 Year Premium', '1 Year Premium VPN Access', 79.99, 365, -1, '{"unlimited_data": true, "ultra_speed": true, "priority_support": true, "discount": "33%"}')
ON CONFLICT DO NOTHING;

-- Insert default VPN panel (you'll need to update this with your actual Marzban details)
INSERT INTO vpn_panels (name, base_url, api_username, api_password, panel_type) VALUES
('Default Marzban Panel', 'https://your-marzban-panel.com', 'admin', 'your-password', 'marzban')
ON CONFLICT DO NOTHING;
