{% extends "base.html" %}

{% block title %}Manage Channels{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="mt-4">Manage Required Channels</h1>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-plus me-1"></i>
            Add New Channel
        </div>
        <div class="card-body">
            <form action="/api/channels" method="post">
                <div class="mb-3">
                    <label for="channel_id" class="form-label">Channel ID (e.g., @channelname or -100123456789)</label>
                    <input type="text" class="form-control" id="channel_id" name="channel_id" required>
                </div>
                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" id="is_required" name="is_required" checked>
                    <label class="form-check-label" for="is_required">
                        Required for Trial
                    </label>
                </div>
                <button type="submit" class="btn btn-primary">Add Channel</button>
            </form>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            Existing Channels
        </div>
        <div class="card-body">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>Channel ID</th>
                        <th>Is Required</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for channel in channels %}
                    <tr>
                        <td>{{ channel.channel_id }}</td>
                        <td>{{ 'Yes' if channel.is_required else 'No' }}</td>
                        <td>
                            <form action="/api/channels/{{ channel.id }}/delete" method="post" onsubmit="return confirm('Are you sure you want to delete this channel?');">
                                <button type="submit" class="btn btn-danger btn-sm">Delete</button>
                            </form>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}