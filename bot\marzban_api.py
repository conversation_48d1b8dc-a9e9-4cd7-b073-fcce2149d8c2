import aiohttp
import asyncio
import logging
from typing import Optional, Dict, Any, List, Tuple
from datetime import datetime, timedelta
import json
from urllib.parse import urljoin
from bot.models import VPNPanel
from bot.database import get_db_session
from sqlalchemy.future import select
from bot.redis import redis_client

logger = logging.getLogger(__name__)

MARZBAN_TOKEN_PREFIX = "marzban_token:"

class MarzbanAPI:
    def __init__(self, base_url: str, username: str, password: str, timeout: int = 30):
        self.base_url = base_url.rstrip('/')
        self.username = username
        self.password = password
        self.token = None
        self.token_expires = None
        self.session = None
        self.timeout = aiohttp.ClientTimeout(total=timeout)
        self.max_retries = 3
        self.retry_delay = 1

    async def _get_session(self):
        if self.session is None or self.session.closed:
            connector = aiohttp.TCPConnector(
                limit=10,
                limit_per_host=5,
                ttl_dns_cache=300,
                use_dns_cache=True,
            )
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=self.timeout,
                headers={
                    'User-Agent': 'VPN-Bot/1.0',
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            )
        return self.session

    async def authenticate(self) -> bool:
        """Authenticate with Marzban API and cache token in Redis"""
        redis_key = f"{MARZBAN_TOKEN_PREFIX}{self.base_url}:{self.username}"
        cached_token = await redis_client.get(redis_key)
        if cached_token:
            self.token = cached_token
            self.token_expires = datetime.utcnow() + timedelta(hours=23)  # Assume it's valid
            logger.info("Using cached token for Marzban API")
            return True

        for attempt in range(self.max_retries):
            try:
                session = await self._get_session()
                
                auth_data = {
                    'username': self.username,
                    'password': self.password
                }
                
                url = urljoin(self.base_url, '/api/admin/token')
                
                async with session.post(url, data=auth_data) as response:
                    if response.status == 200:
                        data = await response.json()
                        self.token = data.get('access_token')
                        self.token_expires = datetime.utcnow() + timedelta(hours=23)
                        await redis_client.set(redis_key, self.token, ex=82800)  # Cache for 23 hours
                        logger.info("Successfully authenticated and cached token for Marzban API")
                        return True
                    elif response.status == 401:
                        logger.error("Authentication failed: Invalid credentials")
                        return False
                    elif response.status == 422:
                        error_detail = await response.text()
                        logger.error(f"Authentication failed: Validation error - {error_detail}")
                        return False
                    else:
                        logger.warning(f"Authentication attempt {attempt + 1} failed: HTTP {response.status}")
                        if attempt < self.max_retries - 1:
                            await asyncio.sleep(self.retry_delay * (attempt + 1))
                            continue
                        return False
                        
            except aiohttp.ClientError as e:
                logger.warning(f"Authentication attempt {attempt + 1} failed: Network error - {e}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(self.retry_delay * (attempt + 1))
                    continue
                logger.error(f"Authentication failed after {self.max_retries} attempts")
                return False
            except Exception as e:
                logger.error(f"Unexpected authentication error: {e}")
                return False
        
        return False

    async def _ensure_authenticated(self) -> bool:
        """Ensure we have a valid token"""
        if self.token and self.token_expires and self.token_expires > datetime.utcnow() + timedelta(minutes=5):
            return True
        return await self.authenticate()

    async def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None, params: Optional[Dict] = None) -> Tuple[bool, Optional[Dict], Optional[str]]:
        """Make authenticated request to Marzban API with enhanced error handling"""
        if not await self._ensure_authenticated():
            return False, None, "Authentication failed"
        
        for attempt in range(self.max_retries):
            try:
                session = await self._get_session()
                headers = {'Authorization': f'Bearer {self.token}'}
                
                url = urljoin(self.base_url, endpoint)
                
                async with session.request(
                    method,
                    url,
                    headers=headers,
                    json=data if data else None,
                    params=params if params else None
                ) as response:
                    response_text = await response.text()
                    
                    if response.status in [200, 201]:
                        try:
                            response_data = json.loads(response_text) if response_text else {}
                            return True, response_data, None
                        except json.JSONDecodeError:
                            return True, {"message": "Success"}, None
                    
                    elif response.status == 401:
                        # Token expired, try to re-authenticate
                        logger.info("Token expired, attempting re-authentication")
                        self.token = None
                        if await self.authenticate():
                            # Retry the request with new token
                            headers = {'Authorization': f'Bearer {self.token}'}
                            async with session.request(
                                method,
                                url,
                                headers=headers,
                                json=data if data else None,
                                params=params if params else None
                            ) as retry_response:
                                retry_text = await retry_response.text()
                                if retry_response.status in [200, 201]:
                                    try:
                                        retry_data = json.loads(retry_text) if retry_text else {}
                                        return True, retry_data, None
                                    except json.JSONDecodeError:
                                        return True, {"message": "Success"}, None
                                else:
                                    return False, None, f"HTTP {retry_response.status}: {retry_text}"
                        return False, None, "Re-authentication failed"
                    
                    elif response.status == 404:
                        return False, None, "Resource not found"
                    elif response.status == 409:
                        return False, None, "Resource already exists or conflict"
                    elif response.status == 422:
                        try:
                            error_data = json.loads(response_text)
                            error_msg = error_data.get('detail', response_text)
                            return False, None, f"Validation error: {error_msg}"
                        except json.JSONDecodeError:
                            return False, None, f"Validation error: {response_text}"
                    elif response.status >= 500:
                        logger.warning(f"Server error (attempt {attempt + 1}): HTTP {response.status}")
                        if attempt < self.max_retries - 1:
                            await asyncio.sleep(self.retry_delay * (attempt + 1))
                            continue
                        return False, None, f"Server error: HTTP {response.status}"
                    else:
                        return False, None, f"HTTP {response.status}: {response_text}"
                        
            except aiohttp.ClientError as e:
                logger.warning(f"Network error (attempt {attempt + 1}): {e}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(self.retry_delay * (attempt + 1))
                    continue
                return False, None, f"Network error: {e}"
            except Exception as e:
                logger.error(f"Unexpected request error: {e}")
                return False, None, f"Unexpected error: {e}"
        
        return False, None, f"Request failed after {self.max_retries} attempts"

    async def reset_user_usage(self, username: str) -> bool:
        """Reset user traffic usage."""
        success, data, error = await self._make_request('POST', f'/api/user/{username}/reset')
        if success:
            logger.info(f"Successfully reset usage for user {username}")
            return True
        else:
            logger.error(f"Failed to reset usage for user {username}: {error}")
            return False
    
    async def get_user(self, username: str) -> Optional[Dict]:
        """Get user information from Marzban."""
        success, data, error = await self._make_request('GET', f'/api/user/{username}')
        if success:
            return data
        else:
            logger.error(f"Failed to get user {username}: {error}")
            return None

    async def add_user(self, username: str, data_limit: int, expire_duration_days: int, proxies: Dict, inbounds: Optional[Dict] = None) -> Optional[Dict]:
        """Add a new user to Marzban."""
        expire_timestamp = (datetime.now() + timedelta(days=expire_duration_days)).timestamp() if expire_duration_days > 0 else 0
        
        user_data = {
            "username": username,
            "proxies": proxies,
            "data_limit": data_limit * 1024**3,  # Convert GB to bytes
            "expire": int(expire_timestamp)
        }
        if inbounds:
            user_data["inbounds"] = inbounds

        success, data, error = await self._make_request('POST', '/api/user', data=user_data)
        if success:
            logger.info(f"Successfully added user {username}")
            return data
        else:
            logger.error(f"Failed to add user {username}: {error}")
            return None

    async def remove_user(self, username: str) -> bool:
        """Remove a user from Marzban."""
        success, _, error = await self._make_request('DELETE', f'/api/user/{username}')
        if success:
            logger.info(f"Successfully removed user {username}")
            return True
        else:
            logger.error(f"Failed to remove user {username}: {error}")
            return False

    async def modify_user(self, username: str, new_data: Dict) -> Optional[Dict]:
        """Modify an existing user in Marzban."""
        success, data, error = await self._make_request('PUT', f'/api/user/{username}', data=new_data)
        if success:
            logger.info(f"Successfully modified user {username}")
            return data
        else:
            logger.error(f"Failed to modify user {username}: {error}")
            return None

    async def get_system_stats(self) -> Optional[Dict]:
        """Get system statistics from Marzban."""
        success, data, error = await self._make_request('GET', '/api/system')
        if success:
            return data
        else:
            logger.error(f"Failed to get system stats: {error}")
            return None
    
    async def create_user(self, user_data: Dict) -> Optional[Dict]:
        """Create a new user in Marzban."""
        success, data, error = await self._make_request('POST', '/api/user', data=user_data)
        if success:
            logger.info(f"Successfully created user {user_data.get('username')}")
            return data
        else:
            logger.error(f"Failed to create user {user_data.get('username')}: {error}")
            return None
    
    async def update_user(self, username: str, user_data: Dict) -> Optional[Dict]:
        """Update user in Marzban."""
        success, data, error = await self._make_request('PUT', f'/api/user/{username}', data=user_data)
        if success:
            logger.info(f"Successfully updated user {username}")
            return data
        else:
            logger.error(f"Failed to update user {username}: {error}")
            return None
    
    async def delete_user(self, username: str) -> bool:
        """Delete user from Marzban."""
        success, data, error = await self._make_request('DELETE', f'/api/user/{username}')
        if success:
            logger.info(f"Successfully deleted user {username}")
            return True
        else:
            logger.error(f"Failed to delete user {username}: {error}")
            return False
    
    async def get_user_usage(self, username: str) -> Optional[Dict]:
        """Get user usage statistics from Marzban."""
        success, data, error = await self._make_request('GET', f'/api/user/{username}')
        if success:
            return {
                'used_traffic': data.get('used_traffic', 0),
                'data_limit': data.get('data_limit', 0),
                'status': data.get('status', 'inactive'),
                'online_at': data.get('online_at')
            }
        else:
            logger.error(f"Failed to get usage for user {username}: {error}")
            return None
    
    async def get_user_config(self, username: str) -> Optional[str]:
        """Get user configuration from Marzban."""
        success, data, error = await self._make_request('GET', f'/api/user/{username}/config')
        if success:
            return data.get('config', '')
        else:
            logger.error(f"Failed to get config for user {username}: {error}")
            return None

    async def close(self):
        """Close the session"""
        if self.session and not self.session.closed:
            await self.session.close()

async def get_marzban_token(panel_id: int, panel_url: str, username_panel: str, password_panel: str):
    # Try to get token from Redis cache
    cached_token = await redis_client.get(f"{MARZBAN_TOKEN_PREFIX}{panel_id}")
    if cached_token:
        token_data = json.loads(cached_token)
        # Check if token is still valid (e.g., within 5 minutes of expiry)
        expires_at = datetime.fromisoformat(token_data['expires_at'])
        if expires_at > datetime.utcnow() + timedelta(minutes=5):
            return token_data['access_token']

    # If not in cache or expired, get new token from Marzban
    url = f"{panel_url}/api/admin/token"
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'accept': 'application/json'
    }
    data = {
        'username': username_panel,
        'password': password_panel
    }
    
    async with aiohttp.ClientSession() as session:
        async with session.post(url, headers=headers, data=data) as response:
            if response.status == 200:
                body = await response.json()
                if 'access_token' in body:
                    access_token = body['access_token']
                    # Marzban tokens typically expire in 1 hour (3600 seconds)
                    expires_at = datetime.utcnow() + timedelta(seconds=3500) # Give a small buffer
                    token_data = {
                        'access_token': access_token,
                        'expires_at': expires_at.isoformat()
                    }
                    await redis_client.set(f"{MARZBAN_TOKEN_PREFIX}{panel_id}", json.dumps(token_data), ex=3500)
                    return access_token
            logger.error(f"Failed to get Marzban token: {await response.text()}")
            return None

async def _make_marzban_request(panel_id: int, panel_url: str, username_panel: str, password_panel: str, method: str, endpoint: str, data: dict = None):
    token = await get_marzban_token(panel_id, panel_url, username_panel, password_panel)
    if not token:
        return {"error": "Failed to obtain Marzban token"}

    url = f"{panel_url}/api/{endpoint}"
    headers = {
        'Accept': 'application/json',
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }

    async with aiohttp.ClientSession() as session:
        try:
            if method == 'GET':
                async with session.get(url, headers=headers) as response:
                    return await response.json()
            elif method == 'POST':
                async with session.post(url, headers=headers, json=data) as response:
                    return await response.json()
            elif method == 'PUT':
                async with session.put(url, headers=headers, json=data) as response:
                    return await response.json()
            elif method == 'DELETE':
                async with session.delete(url, headers=headers) as response:
                    return await response.json()
        except aiohttp.ClientError as e:
            print(f"Marzban API request failed: {e}")
            return {"error": str(e)}

async def get_user_from_marzban(panel: VPNPanel, username: str):
    return await _make_marzban_request(panel.id, panel.url, panel.username_panel, panel.password_panel, 'GET', f'user/{username}')

async def reset_user_usage_marzban(panel: VPNPanel, username: str):
    return await _make_marzban_request(panel.id, panel.url, panel.username_panel, panel.password_panel, 'POST', f'user/{username}/reset')

async def add_user_to_marzban(panel: VPNPanel, user_data: dict):
    return await _make_marzban_request(panel.id, panel.url, panel.username_panel, panel.password_panel, 'POST', 'user', data=user_data)

async def remove_user_from_marzban(panel: VPNPanel, username: str):
    return await _make_marzban_request(panel.id, panel.url, panel.username_panel, panel.password_panel, 'DELETE', f'user/{username}')

async def modify_user_on_marzban(panel: VPNPanel, username: str, user_data: dict):
    return await _make_marzban_request(panel.id, panel.url, panel.username_panel, panel.password_panel, 'PUT', f'user/{username}', data=user_data)

async def get_system_stats_from_marzban(panel: VPNPanel):
    return await _make_marzban_request(panel.id, panel.url, panel.username_panel, panel.password_panel, 'GET', 'system')

async def revoke_sub_marzban(panel: VPNPanel, username: str):
    return await _make_marzban_request(panel.id, panel.url, panel.username_panel, panel.password_panel, 'POST', f'user/{username}/revoke_sub')