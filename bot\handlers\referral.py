"""Referral system handlers for the Telegram bot."""

import logging
from telegram import Update
from telegram.ext import ContextTypes
from bot.services.referral_service import referral_service
from bot.utils.helpers import get_text
from bot.utils.buttons import ReplyKeyboardBuilder

logger = logging.getLogger(__name__)


class ReferralHandler:
    """Handler for referral system commands and interactions."""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def handle_referral_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /referral command to show referral information."""
        try:
            user_data = context.user_data.get('user', {})
            user_id = user_data.get('id')
            language = context.user_data.get('language', 'en')
            
            if not user_id:
                await update.message.reply_text(
                    get_text('errors.auth_required', language),
                    reply_markup=ReplyKeyboardBuilder.create_main_menu(language)
                )
                return
            
            # Get user's referral stats
            stats = await referral_service.get_user_referral_stats(user_id)
            
            if not stats:
                await update.message.reply_text(
                    get_text('referral.error_loading', language),
                    reply_markup=ReplyKeyboardBuilder.create_main_menu(language)
                )
                return
            
            # Generate referral code if user doesn't have one
            referral_code = stats.get('referral_code')
            if not referral_code:
                referral_code = await referral_service.generate_referral_code(user_id)
                if not referral_code:
                    await update.message.reply_text(
                        get_text('referral.code_generation_failed', language),
                        reply_markup=ReplyKeyboardBuilder.create_main_menu(language)
                    )
                    return
                stats['referral_code'] = referral_code
            
            # Format referral message
            message = await self._format_referral_stats_message(stats, language, context.bot.username)
            
            await update.message.reply_text(
                message,
                reply_markup=ReplyKeyboardBuilder.create_referral_menu(language),
                parse_mode='Markdown'
            )
        
        except Exception as e:
            self.logger.error(f"Error handling referral command: {e}")
            await update.message.reply_text(
                get_text('errors.general_error', language),
                reply_markup=ReplyKeyboardBuilder.create_main_menu(language)
            )
    
    async def handle_referral_link_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /referral_link command to get shareable referral link."""
        try:
            user_data = context.user_data.get('user', {})
            user_id = user_data.get('id')
            language = context.user_data.get('language', 'en')
            
            if not user_id:
                await update.message.reply_text(
                    get_text('errors.auth_required', language),
                    reply_markup=ReplyKeyboardBuilder.create_main_menu(language)
                )
                return
            
            # Get or generate referral code
            stats = await referral_service.get_user_referral_stats(user_id)
            referral_code = stats.get('referral_code')
            
            if not referral_code:
                referral_code = await referral_service.generate_referral_code(user_id)
                if not referral_code:
                    await update.message.reply_text(
                        get_text('referral.code_generation_failed', language),
                        reply_markup=ReplyKeyboardBuilder.create_main_menu(language)
                    )
                    return
            
            # Create referral link
            bot_username = context.bot.username
            referral_link = f"https://t.me/{bot_username}?start=ref_{referral_code}"
            
            message = get_text('referral.share_link', language).format(
                referral_code=referral_code,
                referral_link=referral_link
            )
            
            await update.message.reply_text(
                message,
                reply_markup=ReplyKeyboardBuilder.create_referral_menu(language),
                parse_mode='Markdown'
            )
        
        except Exception as e:
            self.logger.error(f"Error handling referral link command: {e}")
            await update.message.reply_text(
                get_text('errors.general_error', language),
                reply_markup=ReplyKeyboardBuilder.create_main_menu(language)
            )
    
    async def process_referral_start(self, update: Update, context: ContextTypes.DEFAULT_TYPE, referral_code: str):
        """Process referral when user starts bot with referral code."""
        try:
            user_data = context.user_data.get('user', {})
            user_id = user_data.get('id')
            language = context.user_data.get('language', 'en')
            
            if not user_id:
                return False
            
            # Process the referral
            result = await referral_service.process_referral(user_id, referral_code)
            
            if result['success']:
                # Send welcome message with referral info
                message = get_text('referral.welcome_referred', language).format(
                    referrer_name=result['referrer']['first_name'],
                    referrer_username=result['referrer']['username'] or 'N/A'
                )
                
                await update.message.reply_text(
                    message,
                    reply_markup=ReplyKeyboardBuilder.create_main_menu(language),
                    parse_mode='Markdown'
                )
                
                # Complete the referral with default reward (1GB data)
                await referral_service.complete_referral(
                    result['referral_id'], 
                    'data', 
                    1073741824  # 1GB in bytes
                )
                
                return True
            else:
                self.logger.warning(f"Referral processing failed: {result['error']}")
                return False
        
        except Exception as e:
            self.logger.error(f"Error processing referral start: {e}")
            return False
    
    async def _format_referral_stats_message(self, stats: dict, language: str, bot_username: str) -> str:
        """Format referral statistics message."""
        try:
            referral_code = stats.get('referral_code', 'N/A')
            total_referrals = stats.get('total_referrals', 0)
            total_rewards = stats.get('total_rewards', 0)
            referrals = stats.get('referrals', [])
            pending_rewards = stats.get('pending_rewards', [])
            
            # Create referral link
            referral_link = f"https://t.me/{bot_username}?start=ref_{referral_code}"
            
            # Format rewards in human-readable format
            rewards_text = self._format_data_amount(total_rewards)
            
            message = get_text('referral.stats_header', language).format(
                referral_code=referral_code,
                referral_link=referral_link,
                total_referrals=total_referrals,
                total_rewards=rewards_text
            )
            
            # Add recent referrals
            if referrals:
                message += f"\n\n{get_text('referral.recent_referrals', language)}\n"
                for i, referral in enumerate(referrals[:5], 1):
                    status_emoji = "✅" if referral['status'] == 'completed' else "⏳"
                    message += f"{i}. {status_emoji} {referral['first_name']} (@{referral['username'] or 'N/A'})\n"
            
            # Add pending rewards
            if pending_rewards:
                message += f"\n{get_text('referral.pending_rewards', language)}\n"
                for reward in pending_rewards:
                    reward_text = self._format_reward(reward, language)
                    message += f"• {reward_text}\n"
            
            message += f"\n{get_text('referral.how_to_earn', language)}"
            
            return message
        
        except Exception as e:
            self.logger.error(f"Error formatting referral stats message: {e}")
            return get_text('referral.error_loading', language)
    
    def _format_data_amount(self, bytes_amount: int) -> str:
        """Format data amount in human-readable format."""
        if bytes_amount >= 1073741824:  # 1GB
            return f"{bytes_amount / 1073741824:.1f} GB"
        elif bytes_amount >= 1048576:  # 1MB
            return f"{bytes_amount / 1048576:.1f} MB"
        else:
            return f"{bytes_amount} bytes"
    
    def _format_reward(self, reward: dict, language: str) -> str:
        """Format reward information."""
        reward_type = reward['reward_type']
        amount = reward['reward_amount']
        
        if reward_type == 'data':
            return f"{self._format_data_amount(amount)} {get_text('referral.reward_data', language)}"
        elif reward_type == 'time':
            return f"{amount} {get_text('referral.reward_days', language)}"
        elif reward_type == 'trial_reset':
            return get_text('referral.reward_trial_reset', language)
        else:
            return f"{reward_type}: {amount}"


# Global instance
referral_handler = ReferralHandler()
