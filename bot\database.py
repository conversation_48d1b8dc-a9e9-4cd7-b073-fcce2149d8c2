from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from bot.config import settings
import contextlib # Import contextlib
import asyncpg
from bot.models import Base

DATABASE_URL = f"postgresql+asyncpg://{settings.POSTGRES_USER}:{settings.POSTGRES_PASSWORD}@{settings.POSTGRES_HOST}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DATABASE}"

engine = create_async_engine(
    DATABASE_URL,
    echo=False, # Set to True for debugging SQL queries
    pool_size=10, # Adjust as needed
    max_overflow=20, # Adjust as needed
    pool_timeout=30, # seconds
    pool_recycle=3600 # seconds
)

AsyncSessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
    class_=AsyncSession,
    expire_on_commit=False
)

@contextlib.asynccontextmanager # Add this decorator
async def get_db_session():
    async with AsyncSessionLocal() as session:
        yield session

# FastAPI dependency function
async def get_db():
    async with Async<PERSON>essionLocal() as session:
        try:
            yield session
        finally:
            await session.close()

@contextlib.asynccontextmanager
async def get_db_connection():
    """Get a raw asyncpg database connection for backward compatibility."""
    conn = await asyncpg.connect(
        user=settings.POSTGRES_USER,
        password=settings.POSTGRES_PASSWORD,
        database=settings.POSTGRES_DATABASE,
        host=settings.POSTGRES_HOST,
        port=settings.POSTGRES_PORT
    )
    try:
        yield conn
    finally:
        await conn.close()

async def init_db():
    async with engine.begin() as conn:
        # await conn.run_sync(Base.metadata.drop_all) # Use with caution for development - COMMENTED OUT TO USE init.sql
        # await conn.run_sync(Base.metadata.create_all) # COMMENTED OUT TO USE init.sql
        pass # Database schema is created by init.sql