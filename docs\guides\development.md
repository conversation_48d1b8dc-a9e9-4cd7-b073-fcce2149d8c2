# Development Guide

This guide covers development workflow, best practices, and coding standards for the VPN Telegram Bot project.

## Development Environment Setup

### 1. Prerequisites

```bash
# Python 3.11+
python --version

# Node.js 18+ and pnpm
node --version
pnpm --version

# Git
git --version

# Docker (optional)
docker --version
docker-compose --version
```

### 2. IDE Configuration

#### VS Code Extensions
```json
{
  "recommendations": [
    "ms-python.python",
    "ms-python.black-formatter",
    "ms-python.isort",
    "ms-python.pylint",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next"
  ]
}
```

#### Python Settings
```json
{
  "python.defaultInterpreterPath": "./venv/bin/python",
  "python.formatting.provider": "black",
  "python.linting.enabled": true,
  "python.linting.pylintEnabled": true,
  "editor.formatOnSave": true,
  "python.sortImports.args": ["--profile", "black"]
}
```

### 3. Virtual Environment

```bash
# Create virtual environment
python -m venv venv

# Activate (Windows)
venv\Scripts\activate

# Activate (Linux/macOS)
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt
```

### 4. Pre-commit Hooks

```bash
# Install pre-commit
pip install pre-commit

# Install hooks
pre-commit install

# Run on all files
pre-commit run --all-files
```

## Project Structure

```
vpn-telegram-bot/
├── bot/                    # Main bot application
│   ├── handlers/          # Telegram update handlers
│   ├── middleware/        # Request middleware
│   ├── services/          # Business logic services
│   ├── utils/            # Utility functions
│   ├── config.py         # Configuration management
│   ├── database.py       # Database connection
│   ├── models.py         # Data models
│   └── main.py           # Application entry point
├── admin-panel/          # React admin interface
│   ├── src/
│   ├── public/
│   └── package.json
├── docs/                 # Documentation
├── tests/               # Test suite
├── logs/                # Application logs
├── requirements.txt     # Python dependencies
├── docker-compose.yml   # Docker configuration
└── .env                 # Environment variables
```

## Coding Standards

### Python Code Style

#### 1. Formatting
- **Black**: Code formatting
- **isort**: Import sorting
- **Line Length**: 88 characters (Black default)

```python
# Good
from telegram import Update
from telegram.ext import ContextTypes

from bot.services.auth_service import AuthService
from bot.utils.helpers import format_user_mention


async def start_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle the /start command."""
    user = update.effective_user
    message = f"Welcome {format_user_mention(user)}!"
    await update.message.reply_text(message)
```

#### 2. Type Hints

```python
from typing import Optional, List, Dict, Any
from telegram import Update
from telegram.ext import ContextTypes

# Function signatures
async def create_vpn_account(
    user_id: int,
    account_type: str,
    duration_days: int = 30
) -> Optional[Dict[str, Any]]:
    """Create a VPN account for the user."""
    pass

# Class definitions
class VPNService:
    def __init__(self, api_client: MarzbanAPI) -> None:
        self.api_client = api_client
        self.logger = logging.getLogger(__name__)
    
    async def get_user_accounts(self, user_id: int) -> List[Dict[str, Any]]:
        """Get all VPN accounts for a user."""
        pass
```

#### 3. Documentation

```python
class PaymentService:
    """Service for handling premium subscriptions and payments."""
    
    async def create_invoice(
        self,
        user_id: int,
        plan_id: str,
        amount: int
    ) -> Dict[str, Any]:
        """Create a payment invoice for a premium plan.
        
        Args:
            user_id: Telegram user ID
            plan_id: Premium plan identifier
            amount: Payment amount in smallest currency unit
            
        Returns:
            Invoice data including payment URL and invoice ID
            
        Raises:
            PaymentError: If invoice creation fails
            ValidationError: If input parameters are invalid
        """
        pass
```

#### 4. Error Handling

```python
from bot.utils.decorators import handle_errors
from bot.exceptions import VPNServiceError, ValidationError

@handle_errors
async def trial_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle trial VPN account creation."""
    try:
        user_id = update.effective_user.id
        
        # Validate trial eligibility
        if not await vpn_service.is_trial_eligible(user_id):
            await update.message.reply_text("You are not eligible for a trial account.")
            return
        
        # Create trial account
        account = await vpn_service.create_trial_account(user_id)
        
        # Send account details
        await send_account_details(update, account)
        
    except VPNServiceError as e:
        logger.error(f"VPN service error: {e}")
        await update.message.reply_text("Failed to create trial account. Please try again later.")
    
    except ValidationError as e:
        logger.warning(f"Validation error: {e}")
        await update.message.reply_text("Invalid request. Please check your input.")
```

### JavaScript/TypeScript Code Style

#### 1. Prettier Configuration

```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false
}
```

#### 2. ESLint Configuration

```json
{
  "extends": [
    "react-app",
    "react-app/jest",
    "@typescript-eslint/recommended"
  ],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/explicit-function-return-type": "warn",
    "react-hooks/exhaustive-deps": "warn"
  }
}
```

#### 3. Component Structure

```typescript
import React, { useState, useEffect } from 'react';
import { User } from '../types/User';
import { userService } from '../services/userService';

interface UserDashboardProps {
  userId: number;
}

const UserDashboard: React.FC<UserDashboardProps> = ({ userId }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUser = async (): Promise<void> => {
      try {
        const userData = await userService.getUser(userId);
        setUser(userData);
      } catch (err) {
        setError('Failed to load user data');
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, [userId]);

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;
  if (!user) return <div>User not found</div>;

  return (
    <div className="user-dashboard">
      <h1>Welcome, {user.firstName}!</h1>
      {/* Dashboard content */}
    </div>
  );
};

export default UserDashboard;
```

## Development Workflow

### 1. Feature Development

```bash
# Create feature branch
git checkout -b feature/new-feature-name

# Make changes
# ...

# Run tests
pytest tests/
pnpm test  # For admin panel

# Format code
black bot/
isort bot/
prettier --write admin-panel/src/

# Commit changes
git add .
git commit -m "feat: add new feature description"

# Push branch
git push origin feature/new-feature-name

# Create pull request
```

### 2. Commit Message Convention

```
type(scope): description

Types:
- feat: New feature
- fix: Bug fix
- docs: Documentation
- style: Code style changes
- refactor: Code refactoring
- test: Adding tests
- chore: Maintenance tasks

Examples:
feat(auth): add user registration middleware
fix(payments): handle payment webhook errors
docs(api): update service documentation
```

### 3. Testing Strategy

#### Unit Tests

```python
# tests/test_auth_service.py
import pytest
from unittest.mock import AsyncMock, patch

from bot.services.auth_service import AuthService
from bot.exceptions import UserNotFoundError


class TestAuthService:
    @pytest.fixture
    def auth_service(self):
        return AuthService(db_pool=AsyncMock())
    
    @pytest.mark.asyncio
    async def test_get_user_success(self, auth_service):
        # Arrange
        user_data = {'id': 1, 'telegram_id': 123456}
        auth_service.db_pool.fetchrow.return_value = user_data
        
        # Act
        result = await auth_service.get_user(123456)
        
        # Assert
        assert result == user_data
        auth_service.db_pool.fetchrow.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_user_not_found(self, auth_service):
        # Arrange
        auth_service.db_pool.fetchrow.return_value = None
        
        # Act & Assert
        with pytest.raises(UserNotFoundError):
            await auth_service.get_user(123456)
```

#### Integration Tests

```python
# tests/test_handlers_integration.py
import pytest
from telegram import Update, User, Message, Chat
from telegram.ext import ContextTypes

from bot.handlers.commands import start_command


class TestCommandHandlers:
    @pytest.mark.asyncio
    async def test_start_command_new_user(self, mock_context):
        # Arrange
        user = User(id=123456, first_name="Test", is_bot=False)
        chat = Chat(id=123456, type="private")
        message = Message(
            message_id=1,
            date=None,
            chat=chat,
            from_user=user,
            text="/start"
        )
        update = Update(update_id=1, message=message)
        
        # Act
        await start_command(update, mock_context)
        
        # Assert
        mock_context.bot.send_message.assert_called_once()
        args = mock_context.bot.send_message.call_args
        assert "Welcome" in args[1]['text']
```

### 4. Database Migrations

```python
# migrations/001_initial_schema.py
from bot.database import get_db_pool

async def upgrade():
    """Apply migration."""
    pool = await get_db_pool()
    
    async with pool.acquire() as conn:
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id BIGSERIAL PRIMARY KEY,
                telegram_id BIGINT UNIQUE NOT NULL,
                username VARCHAR(255),
                first_name VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)

async def downgrade():
    """Rollback migration."""
    pool = await get_db_pool()
    
    async with pool.acquire() as conn:
        await conn.execute("DROP TABLE IF EXISTS users;")
```

### 5. Environment Management

#### Development Environment

```env
# .env.development
DEBUG=true
LOG_LEVEL=DEBUG
DATABASE_URL=postgresql://dev_user:dev_pass@localhost:5432/vpn_bot_dev
REDIS_URL=redis://localhost:6379/1
MARZBAN_URL=http://localhost:8000
```

#### Testing Environment

```env
# .env.test
DEBUG=true
LOG_LEVEL=DEBUG
DATABASE_URL=postgresql://test_user:test_pass@localhost:5432/vpn_bot_test
REDIS_URL=redis://localhost:6379/2
```

## Debugging

### 1. Logging Configuration

```python
# bot/config.py
import logging
from logging.handlers import RotatingFileHandler

def setup_logging():
    """Configure application logging."""
    # Create formatters
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    
    # File handler
    file_handler = RotatingFileHandler(
        'logs/bot.log',
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    file_handler.setFormatter(formatter)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
```

### 2. Debug Utilities

```python
# bot/utils/debug.py
import functools
import time
import logging

logger = logging.getLogger(__name__)

def debug_timing(func):
    """Decorator to measure function execution time."""
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            return result
        finally:
            execution_time = time.time() - start_time
            logger.debug(f"{func.__name__} executed in {execution_time:.2f}s")
    return wrapper

def debug_args(func):
    """Decorator to log function arguments."""
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        logger.debug(f"{func.__name__} called with args={args}, kwargs={kwargs}")
        return await func(*args, **kwargs)
    return wrapper
```

### 3. Database Debugging

```python
# Enable SQL query logging
import logging
logging.getLogger('asyncpg').setLevel(logging.DEBUG)

# Query performance monitoring
async def log_slow_queries(query, *args):
    start_time = time.time()
    result = await conn.fetch(query, *args)
    execution_time = time.time() - start_time
    
    if execution_time > 1.0:  # Log queries taking more than 1 second
        logger.warning(f"Slow query ({execution_time:.2f}s): {query}")
    
    return result
```

## Performance Optimization

### 1. Database Optimization

```python
# Connection pooling
from asyncpg import create_pool

async def create_db_pool():
    return await create_pool(
        dsn=DATABASE_URL,
        min_size=5,
        max_size=20,
        command_timeout=60,
        server_settings={
            'application_name': 'vpn_bot',
            'jit': 'off'
        }
    )

# Batch operations
async def create_multiple_accounts(accounts_data):
    async with pool.acquire() as conn:
        await conn.executemany(
            "INSERT INTO vpn_accounts (user_id, username, expires_at) VALUES ($1, $2, $3)",
            accounts_data
        )
```

### 2. Caching Strategy

```python
# Redis caching
from bot.redis import redis_manager

async def get_user_with_cache(user_id: int):
    # Try cache first
    cache_key = f"user:{user_id}"
    cached_user = await redis_manager.get(cache_key)
    
    if cached_user:
        return json.loads(cached_user)
    
    # Fetch from database
    user = await auth_service.get_user(user_id)
    
    # Cache for 1 hour
    await redis_manager.set_with_expiry(
        cache_key,
        json.dumps(user),
        3600
    )
    
    return user
```

### 3. Async Best Practices

```python
# Concurrent operations
import asyncio

async def process_multiple_users(user_ids):
    tasks = []
    for user_id in user_ids:
        task = asyncio.create_task(process_user(user_id))
        tasks.append(task)
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    return results

# Semaphore for rate limiting
semaphore = asyncio.Semaphore(10)  # Max 10 concurrent operations

async def rate_limited_operation():
    async with semaphore:
        # Perform operation
        pass
```

## Security Best Practices

### 1. Input Validation

```python
from bot.utils.validators import DataValidator

@DataValidator.validate_telegram_user_id('user_id')
@DataValidator.validate_string('message', min_length=1, max_length=1000)
async def send_message_to_user(user_id: int, message: str):
    # Function implementation
    pass
```

### 2. Secret Management

```python
# Never hardcode secrets
BOT_TOKEN = os.getenv('BOT_TOKEN')
if not BOT_TOKEN:
    raise ValueError("BOT_TOKEN environment variable is required")

# Use environment-specific configurations
class Config:
    def __init__(self):
        self.bot_token = self._get_required_env('BOT_TOKEN')
        self.database_url = self._get_required_env('DATABASE_URL')
    
    def _get_required_env(self, key: str) -> str:
        value = os.getenv(key)
        if not value:
            raise ValueError(f"{key} environment variable is required")
        return value
```

### 3. Error Information Disclosure

```python
# Don't expose internal errors to users
try:
    result = await dangerous_operation()
except InternalError as e:
    logger.error(f"Internal error: {e}")
    await update.message.reply_text("An error occurred. Please try again later.")
except ValidationError as e:
    await update.message.reply_text(f"Invalid input: {e.user_message}")
```

## Deployment

### 1. Docker Development

```dockerfile
# Dockerfile.dev
FROM python:3.11-slim

WORKDIR /app

# Install dependencies
COPY requirements.txt .
RUN pip install -r requirements.txt

# Copy source code
COPY bot/ ./bot/

# Development command
CMD ["python", "-m", "bot.main"]
```

### 2. Hot Reloading

```python
# Development server with auto-reload
if __name__ == "__main__":
    import watchdog
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler
    
    class ReloadHandler(FileSystemEventHandler):
        def on_modified(self, event):
            if event.src_path.endswith('.py'):
                logger.info("Code changed, restarting...")
                # Restart logic
    
    if settings.DEBUG:
        observer = Observer()
        observer.schedule(ReloadHandler(), 'bot/', recursive=True)
        observer.start()
```

This development guide provides a comprehensive foundation for maintaining code quality, following best practices, and ensuring efficient development workflow for the VPN Telegram Bot project.