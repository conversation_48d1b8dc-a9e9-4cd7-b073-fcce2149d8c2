# Deployment Guide

This guide covers production deployment strategies, server configuration, and maintenance procedures for the VPN Telegram Bot.

## Deployment Options

### 1. Docker Deployment (Recommended)

#### Production Docker Compose

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  bot:
    build:
      context: .
      dockerfile: Dockerfile.prod
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - BOT_TOKEN=${BOT_TOKEN}
    depends_on:
      - postgres
      - redis
    networks:
      - vpn-bot-network
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data

  admin-panel:
    build:
      context: ./admin-panel
      dockerfile: Dockerfile.prod
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - REACT_APP_API_URL=${API_URL}
    networks:
      - vpn-bot-network

  postgres:
    image: postgres:15-alpine
    restart: unless-stopped
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    networks:
      - vpn-bot-network
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - vpn-bot-network
    ports:
      - "6379:6379"

  nginx:
    image: nginx:alpine
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - admin-panel
    networks:
      - vpn-bot-network

  prometheus:
    image: prom/prometheus:latest
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - vpn-bot-network

  grafana:
    image: grafana/grafana:latest
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning
    networks:
      - vpn-bot-network

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  vpn-bot-network:
    driver: bridge
```

#### Production Dockerfile

```dockerfile
# Dockerfile.prod
FROM python:3.11-slim as builder

# Install build dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Create virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Production stage
FROM python:3.11-slim

# Create non-root user
RUN groupadd -r botuser && useradd -r -g botuser botuser

# Copy virtual environment
COPY --from=builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Set working directory
WORKDIR /app

# Copy application code
COPY bot/ ./bot/
COPY migrations/ ./migrations/

# Create necessary directories
RUN mkdir -p logs data && chown -R botuser:botuser /app

# Switch to non-root user
USER botuser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import asyncio; import aiohttp; asyncio.run(aiohttp.ClientSession().get('http://localhost:8080/health').close())"

# Start application
CMD ["python", "-m", "bot.main"]
```

### 2. VPS Deployment

#### Server Requirements

```bash
# Minimum specifications
CPU: 2 cores
RAM: 4GB
Storage: 50GB SSD
Bandwidth: 100Mbps
OS: Ubuntu 22.04 LTS
```

#### Server Setup Script

```bash
#!/bin/bash
# setup-server.sh

set -e

echo "Setting up VPN Bot server..."

# Update system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install -y \
    curl \
    wget \
    git \
    nginx \
    certbot \
    python3-certbot-nginx \
    ufw \
    fail2ban \
    htop \
    unzip

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Configure firewall
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw --force enable

# Configure fail2ban
sudo systemctl enable fail2ban
sudo systemctl start fail2ban

# Create application directory
sudo mkdir -p /opt/vpn-bot
sudo chown $USER:$USER /opt/vpn-bot

echo "Server setup completed!"
echo "Please reboot the server and then deploy the application."
```

#### Application Deployment

```bash
#!/bin/bash
# deploy.sh

set -e

APP_DIR="/opt/vpn-bot"
REPO_URL="https://github.com/your-username/vpn-telegram-bot.git"
BRANCH="main"

echo "Deploying VPN Bot application..."

# Clone or update repository
if [ -d "$APP_DIR/.git" ]; then
    cd $APP_DIR
    git fetch origin
    git reset --hard origin/$BRANCH
else
    git clone -b $BRANCH $REPO_URL $APP_DIR
    cd $APP_DIR
fi

# Copy environment file
if [ ! -f ".env" ]; then
    cp .env.example .env
    echo "Please configure .env file before continuing"
    exit 1
fi

# Build and start services
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml build
docker-compose -f docker-compose.prod.yml up -d

# Wait for services to start
echo "Waiting for services to start..."
sleep 30

# Run database migrations
docker-compose -f docker-compose.prod.yml exec bot python -m bot.migrations.migrate

# Check service status
docker-compose -f docker-compose.prod.yml ps

echo "Deployment completed!"
```

## SSL/TLS Configuration

### Nginx Configuration

```nginx
# nginx/nginx.conf
events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;

    # Upstream servers
    upstream admin_panel {
        server admin-panel:3000;
    }

    # HTTP to HTTPS redirect
    server {
        listen 80;
        server_name your-domain.com www.your-domain.com;
        return 301 https://$server_name$request_uri;
    }

    # HTTPS server
    server {
        listen 443 ssl http2;
        server_name your-domain.com www.your-domain.com;

        # SSL configuration
        ssl_certificate /etc/nginx/ssl/fullchain.pem;
        ssl_certificate_key /etc/nginx/ssl/privkey.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        # Admin panel
        location / {
            proxy_pass http://admin_panel;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Rate limiting
            limit_req zone=api burst=20 nodelay;
        }

        # API endpoints
        location /api/ {
            proxy_pass http://admin_panel;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Rate limiting
            limit_req zone=api burst=10 nodelay;
        }

        # Login endpoint with stricter rate limiting
        location /api/auth/login {
            proxy_pass http://admin_panel;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            limit_req zone=login burst=5 nodelay;
        }

        # Health check
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
```

### SSL Certificate Setup

```bash
#!/bin/bash
# setup-ssl.sh

DOMAIN="your-domain.com"
EMAIL="<EMAIL>"

# Install Certbot
sudo apt install certbot python3-certbot-nginx -y

# Obtain SSL certificate
sudo certbot --nginx -d $DOMAIN -d www.$DOMAIN --email $EMAIL --agree-tos --non-interactive

# Set up auto-renewal
sudo crontab -l | { cat; echo "0 12 * * * /usr/bin/certbot renew --quiet"; } | sudo crontab -

# Test renewal
sudo certbot renew --dry-run

echo "SSL certificate setup completed!"
```

## Environment Configuration

### Production Environment Variables

```env
# .env.production

# Application
NODE_ENV=production
DEBUG=false
LOG_LEVEL=INFO

# Bot Configuration
BOT_TOKEN=your_bot_token_here
BOT_USERNAME=your_bot_username
WEBHOOK_URL=https://your-domain.com/webhook
WEBHOOK_SECRET=your_webhook_secret

# Database
DATABASE_URL=********************************************/vpn_bot
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# Redis
REDIS_URL=redis://:password@redis:6379/0
REDIS_PASSWORD=your_redis_password

# Marzban Integration
MARZBAN_URL=https://your-marzban-panel.com
MARZBAN_USERNAME=admin
MARZBAN_PASSWORD=your_marzban_password

# Payment Configuration
PAYMENT_PROVIDER_TOKEN=your_payment_token
PAYMENT_WEBHOOK_SECRET=your_payment_webhook_secret

# Channel Configuration
REQUIRED_CHANNELS=@channel1,@channel2
ADMIN_CHAT_ID=-1001234567890

# Security
SECRET_KEY=your_secret_key_here
JWT_SECRET=your_jwt_secret
ENCRYPTION_KEY=your_encryption_key

# Monitoring
PROMETHEUS_PORT=9090
GRAFANA_PASSWORD=your_grafana_password

# Backup
BACKUP_ENCRYPTION_KEY=your_backup_encryption_key
S3_BUCKET=your-backup-bucket
S3_ACCESS_KEY=your_s3_access_key
S3_SECRET_KEY=your_s3_secret_key
```

### Configuration Validation

```python
# bot/config/validator.py
import os
from typing import List, Optional

class ConfigValidator:
    """Validate production configuration."""
    
    REQUIRED_VARS = [
        'BOT_TOKEN',
        'DATABASE_URL',
        'REDIS_URL',
        'MARZBAN_URL',
        'SECRET_KEY'
    ]
    
    @classmethod
    def validate(cls) -> List[str]:
        """Validate all required configuration variables."""
        errors = []
        
        # Check required variables
        for var in cls.REQUIRED_VARS:
            if not os.getenv(var):
                errors.append(f"Missing required environment variable: {var}")
        
        # Validate specific formats
        bot_token = os.getenv('BOT_TOKEN')
        if bot_token and not cls._is_valid_bot_token(bot_token):
            errors.append("Invalid BOT_TOKEN format")
        
        database_url = os.getenv('DATABASE_URL')
        if database_url and not database_url.startswith('postgresql://'):
            errors.append("DATABASE_URL must be a PostgreSQL connection string")
        
        return errors
    
    @staticmethod
    def _is_valid_bot_token(token: str) -> bool:
        """Validate Telegram bot token format."""
        parts = token.split(':')
        return len(parts) == 2 and parts[0].isdigit() and len(parts[1]) == 35
```

## Database Management

### Migration System

```python
# bot/migrations/migrate.py
import asyncio
import logging
from pathlib import Path
from bot.database import get_db_pool

logger = logging.getLogger(__name__)

class MigrationManager:
    """Handle database migrations."""
    
    def __init__(self, pool):
        self.pool = pool
        self.migrations_dir = Path(__file__).parent / 'versions'
    
    async def run_migrations(self):
        """Run all pending migrations."""
        await self._ensure_migration_table()
        
        applied_migrations = await self._get_applied_migrations()
        available_migrations = self._get_available_migrations()
        
        pending_migrations = [
            m for m in available_migrations 
            if m not in applied_migrations
        ]
        
        if not pending_migrations:
            logger.info("No pending migrations")
            return
        
        logger.info(f"Running {len(pending_migrations)} migrations")
        
        for migration in pending_migrations:
            await self._run_migration(migration)
            logger.info(f"Applied migration: {migration}")
    
    async def _ensure_migration_table(self):
        """Create migrations table if it doesn't exist."""
        async with self.pool.acquire() as conn:
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS migrations (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(255) UNIQUE NOT NULL,
                    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
    
    async def _get_applied_migrations(self):
        """Get list of applied migrations."""
        async with self.pool.acquire() as conn:
            rows = await conn.fetch("SELECT name FROM migrations ORDER BY name")
            return [row['name'] for row in rows]
    
    def _get_available_migrations(self):
        """Get list of available migration files."""
        migration_files = sorted(self.migrations_dir.glob('*.py'))
        return [f.stem for f in migration_files if not f.name.startswith('__')]
    
    async def _run_migration(self, migration_name):
        """Run a specific migration."""
        migration_module = __import__(
            f'bot.migrations.versions.{migration_name}',
            fromlist=['upgrade']
        )
        
        async with self.pool.acquire() as conn:
            async with conn.transaction():
                await migration_module.upgrade(conn)
                await conn.execute(
                    "INSERT INTO migrations (name) VALUES ($1)",
                    migration_name
                )

async def main():
    """Run migrations."""
    pool = await get_db_pool()
    manager = MigrationManager(pool)
    await manager.run_migrations()
    await pool.close()

if __name__ == '__main__':
    asyncio.run(main())
```

### Backup Strategy

```bash
#!/bin/bash
# backup.sh

set -e

BACKUP_DIR="/opt/vpn-bot/backups"
DATE=$(date +%Y%m%d_%H%M%S)
DATABASE_NAME="vpn_bot"
S3_BUCKET="your-backup-bucket"

# Create backup directory
mkdir -p $BACKUP_DIR

# Database backup
echo "Creating database backup..."
docker-compose exec -T postgres pg_dump -U postgres $DATABASE_NAME | gzip > $BACKUP_DIR/db_backup_$DATE.sql.gz

# Redis backup
echo "Creating Redis backup..."
docker-compose exec -T redis redis-cli --rdb /data/dump_$DATE.rdb
docker cp $(docker-compose ps -q redis):/data/dump_$DATE.rdb $BACKUP_DIR/
gzip $BACKUP_DIR/dump_$DATE.rdb

# Application data backup
echo "Creating application data backup..."
tar -czf $BACKUP_DIR/app_data_$DATE.tar.gz -C /opt/vpn-bot data/ logs/

# Upload to S3 (optional)
if command -v aws &> /dev/null; then
    echo "Uploading backups to S3..."
    aws s3 cp $BACKUP_DIR/db_backup_$DATE.sql.gz s3://$S3_BUCKET/database/
    aws s3 cp $BACKUP_DIR/dump_$DATE.rdb.gz s3://$S3_BUCKET/redis/
    aws s3 cp $BACKUP_DIR/app_data_$DATE.tar.gz s3://$S3_BUCKET/application/
fi

# Clean up old backups (keep last 7 days)
find $BACKUP_DIR -name "*.gz" -mtime +7 -delete

echo "Backup completed: $DATE"
```

## Monitoring and Logging

### Prometheus Configuration

```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'vpn-bot'
    static_configs:
      - targets: ['bot:8080']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']
```

### Application Metrics

```python
# bot/monitoring/metrics.py
from prometheus_client import Counter, Histogram, Gauge, start_http_server
import time
import functools

# Metrics
REQUEST_COUNT = Counter('bot_requests_total', 'Total bot requests', ['method', 'endpoint'])
REQUEST_DURATION = Histogram('bot_request_duration_seconds', 'Request duration')
ACTIVE_USERS = Gauge('bot_active_users', 'Number of active users')
VPN_ACCOUNTS = Gauge('bot_vpn_accounts_total', 'Total VPN accounts', ['status'])
ERROR_COUNT = Counter('bot_errors_total', 'Total errors', ['error_type'])

def track_metrics(func):
    """Decorator to track function metrics."""
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            REQUEST_COUNT.labels(method=func.__name__, endpoint='success').inc()
            return result
        except Exception as e:
            ERROR_COUNT.labels(error_type=type(e).__name__).inc()
            raise
        finally:
            REQUEST_DURATION.observe(time.time() - start_time)
    return wrapper

def start_metrics_server(port=8080):
    """Start Prometheus metrics server."""
    start_http_server(port)
```

### Log Aggregation

```yaml
# docker-compose.logging.yml
version: '3.8'

services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"

  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    volumes:
      - ./monitoring/logstash.conf:/usr/share/logstash/pipeline/logstash.conf
    depends_on:
      - elasticsearch

  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch

volumes:
  elasticsearch_data:
```

## Security Hardening

### System Security

```bash
#!/bin/bash
# security-hardening.sh

set -e

echo "Applying security hardening..."

# Update system
sudo apt update && sudo apt upgrade -y

# Install security tools
sudo apt install -y fail2ban ufw rkhunter chkrootkit

# Configure fail2ban
sudo tee /etc/fail2ban/jail.local > /dev/null <<EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[sshd]
enabled = true
port = ssh
logpath = /var/log/auth.log
maxretry = 3

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
logpath = /var/log/nginx/error.log
maxretry = 3
EOF

# Configure UFW
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw --force enable

# Secure SSH
sudo sed -i 's/#PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
sudo sed -i 's/#PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config
sudo systemctl restart ssh

# Set up automatic security updates
sudo apt install -y unattended-upgrades
sudo dpkg-reconfigure -plow unattended-upgrades

echo "Security hardening completed!"
```

### Application Security

```python
# bot/security/middleware.py
import time
import hashlib
from collections import defaultdict
from telegram import Update
from telegram.ext import ContextTypes

class SecurityMiddleware:
    """Security middleware for bot requests."""
    
    def __init__(self):
        self.rate_limits = defaultdict(list)
        self.blocked_users = set()
    
    async def process_update(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Process security checks for incoming updates."""
        user_id = update.effective_user.id if update.effective_user else None
        
        if not user_id:
            return False
        
        # Check if user is blocked
        if user_id in self.blocked_users:
            return False
        
        # Rate limiting
        if not self._check_rate_limit(user_id):
            await self._handle_rate_limit_exceeded(update, context)
            return False
        
        # Content filtering
        if not self._check_content_safety(update):
            await self._handle_unsafe_content(update, context)
            return False
        
        return True
    
    def _check_rate_limit(self, user_id: int, limit: int = 10, window: int = 60) -> bool:
        """Check if user exceeds rate limit."""
        now = time.time()
        user_requests = self.rate_limits[user_id]
        
        # Remove old requests
        user_requests[:] = [req_time for req_time in user_requests if now - req_time < window]
        
        # Check limit
        if len(user_requests) >= limit:
            return False
        
        # Add current request
        user_requests.append(now)
        return True
    
    def _check_content_safety(self, update: Update) -> bool:
        """Check if content is safe."""
        if update.message and update.message.text:
            text = update.message.text.lower()
            # Add your content filtering logic here
            suspicious_patterns = ['spam', 'hack', 'exploit']
            return not any(pattern in text for pattern in suspicious_patterns)
        return True
    
    async def _handle_rate_limit_exceeded(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle rate limit exceeded."""
        await update.message.reply_text("Too many requests. Please try again later.")
    
    async def _handle_unsafe_content(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle unsafe content."""
        await update.message.reply_text("Your message contains inappropriate content.")
```

## Maintenance Procedures

### Health Checks

```python
# bot/health.py
import asyncio
import aiohttp
from bot.database import get_db_pool
from bot.redis import get_redis

class HealthChecker:
    """Application health checker."""
    
    async def check_all(self) -> dict:
        """Run all health checks."""
        results = {
            'database': await self.check_database(),
            'redis': await self.check_redis(),
            'marzban': await self.check_marzban(),
            'telegram': await self.check_telegram()
        }
        
        results['overall'] = all(results.values())
        return results
    
    async def check_database(self) -> bool:
        """Check database connectivity."""
        try:
            pool = await get_db_pool()
            async with pool.acquire() as conn:
                await conn.fetchval('SELECT 1')
            return True
        except Exception:
            return False
    
    async def check_redis(self) -> bool:
        """Check Redis connectivity."""
        try:
            redis = await get_redis()
            await redis.ping()
            return True
        except Exception:
            return False
    
    async def check_marzban(self) -> bool:
        """Check Marzban API connectivity."""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{MARZBAN_URL}/api/system") as response:
                    return response.status == 200
        except Exception:
            return False
    
    async def check_telegram(self) -> bool:
        """Check Telegram Bot API connectivity."""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"https://api.telegram.org/bot{BOT_TOKEN}/getMe") as response:
                    return response.status == 200
        except Exception:
            return False
```

### Update Procedures

```bash
#!/bin/bash
# update.sh

set -e

APP_DIR="/opt/vpn-bot"
BACKUP_DIR="$APP_DIR/backups"
DATE=$(date +%Y%m%d_%H%M%S)

echo "Starting application update..."

cd $APP_DIR

# Create backup
echo "Creating backup..."
./backup.sh

# Pull latest changes
echo "Pulling latest changes..."
git fetch origin
git checkout main
git pull origin main

# Build new images
echo "Building new images..."
docker-compose -f docker-compose.prod.yml build

# Run database migrations
echo "Running database migrations..."
docker-compose -f docker-compose.prod.yml run --rm bot python -m bot.migrations.migrate

# Update services with zero downtime
echo "Updating services..."
docker-compose -f docker-compose.prod.yml up -d --no-deps bot
docker-compose -f docker-compose.prod.yml up -d --no-deps admin-panel

# Wait for services to be healthy
echo "Waiting for services to be healthy..."
sleep 30

# Verify deployment
echo "Verifying deployment..."
if curl -f http://localhost/health > /dev/null 2>&1; then
    echo "Update completed successfully!"
else
    echo "Update failed! Rolling back..."
    # Rollback logic here
    exit 1
fi

# Clean up old images
docker image prune -f

echo "Update completed!"
```

This deployment guide provides comprehensive instructions for production deployment, security hardening, monitoring setup, and maintenance procedures for the VPN Telegram Bot project.