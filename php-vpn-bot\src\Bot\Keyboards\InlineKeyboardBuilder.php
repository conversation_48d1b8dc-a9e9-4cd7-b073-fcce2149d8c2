<?php

declare(strict_types=1);

namespace VpnBot\Bot\Keyboards;

use <PERSON><PERSON>\TelegramBot\Entities\InlineKeyboard;
use Long<PERSON>\TelegramBot\Entities\InlineKeyboardButton;
use VpnBot\Utils\Localization;

class InlineKeyboardBuilder
{
    private Localization $localization;

    public function __construct()
    {
        $this->localization = Localization::getInstance();
    }

    public function createMainMenu(string $language = 'en'): InlineKeyboard
    {
        return new InlineKeyboard([
            [
                InlineKeyboardButton::make(
                    $this->localization->get('buttons.trial_vpn', $language),
                    callback_data: 'trial_vpn'
                ),
                InlineKeyboardButton::make(
                    $this->localization->get('buttons.premium', $language),
                    callback_data: 'premium'
                ),
            ],
            [
                InlineKeyboardButton::make(
                    $this->localization->get('buttons.dashboard', $language),
                    callback_data: 'dashboard'
                ),
                InlineKeyboardButton::make(
                    $this->localization->get('buttons.my_accounts', $language),
                    callback_data: 'my_accounts'
                ),
            ],
            [
                InlineKeyboardButton::make(
                    $this->localization->get('buttons.referral', $language),
                    callback_data: 'referral'
                ),
                InlineKeyboardButton::make(
                    $this->localization->get('buttons.settings', $language),
                    callback_data: 'settings'
                ),
            ],
            [
                InlineKeyboardButton::make(
                    $this->localization->get('buttons.help', $language),
                    callback_data: 'help'
                ),
                InlineKeyboardButton::make(
                    $this->localization->get('buttons.support', $language),
                    callback_data: 'support'
                ),
            ],
        ]);
    }

    public function createPremiumPlansMenu(string $language = 'en'): InlineKeyboard
    {
        return new InlineKeyboard([
            [
                InlineKeyboardButton::make(
                    '💎 1 Month - $9.99',
                    callback_data: 'plan_1month'
                ),
            ],
            [
                InlineKeyboardButton::make(
                    '💎 3 Months - $24.99',
                    callback_data: 'plan_3months'
                ),
            ],
            [
                InlineKeyboardButton::make(
                    '💎 6 Months - $44.99',
                    callback_data: 'plan_6months'
                ),
            ],
            [
                InlineKeyboardButton::make(
                    '💎 1 Year - $79.99',
                    callback_data: 'plan_1year'
                ),
            ],
            [
                InlineKeyboardButton::make(
                    $this->localization->get('buttons.back', $language),
                    callback_data: 'back_main'
                ),
            ],
        ]);
    }

    public function createPaymentMethodsMenu(string $language = 'en'): InlineKeyboard
    {
        return new InlineKeyboard([
            [
                InlineKeyboardButton::make(
                    $this->localization->get('payment.stars', $language),
                    callback_data: 'pay_stars'
                ),
                InlineKeyboardButton::make(
                    $this->localization->get('payment.card', $language),
                    callback_data: 'pay_card'
                ),
            ],
            [
                InlineKeyboardButton::make(
                    $this->localization->get('payment.crypto', $language),
                    callback_data: 'pay_crypto'
                ),
                InlineKeyboardButton::make(
                    $this->localization->get('payment.ton', $language),
                    callback_data: 'pay_ton'
                ),
            ],
            [
                InlineKeyboardButton::make(
                    $this->localization->get('buttons.back', $language),
                    callback_data: 'back_premium'
                ),
            ],
        ]);
    }

    public function createSettingsMenu(string $language = 'en'): InlineKeyboard
    {
        return new InlineKeyboard([
            [
                InlineKeyboardButton::make(
                    $this->localization->get('buttons.change_language', $language),
                    callback_data: 'change_language'
                ),
            ],
            [
                InlineKeyboardButton::make(
                    '🔔 Notifications',
                    callback_data: 'notifications'
                ),
            ],
            [
                InlineKeyboardButton::make(
                    $this->localization->get('buttons.back', $language),
                    callback_data: 'back_main'
                ),
            ],
        ]);
    }

    public function createLanguageSelectionMenu(string $currentLanguage = 'en'): InlineKeyboard
    {
        $languages = [
            'en' => '🇺🇸 English',
            'fa' => '🇮🇷 فارسی',
            'ru' => '🇷🇺 Русский',
            'zh' => '🇨🇳 中文',
        ];

        $buttons = [];
        foreach ($languages as $code => $name) {
            $text = $code === $currentLanguage ? "✅ $name" : $name;
            $buttons[] = [
                InlineKeyboardButton::make(
                    $text,
                    callback_data: "lang_$code"
                ),
            ];
        }

        $buttons[] = [
            InlineKeyboardButton::make(
                $this->localization->get('buttons.back', $currentLanguage),
                callback_data: 'back_settings'
            ),
        ];

        return new InlineKeyboard($buttons);
    }

    public function createAccountsMenu(string $language = 'en'): InlineKeyboard
    {
        return new InlineKeyboard([
            [
                InlineKeyboardButton::make(
                    '🔄 Refresh',
                    callback_data: 'refresh_accounts'
                ),
                InlineKeyboardButton::make(
                    '📊 Usage Stats',
                    callback_data: 'usage_stats'
                ),
            ],
            [
                InlineKeyboardButton::make(
                    $this->localization->get('buttons.back', $language),
                    callback_data: 'back_main'
                ),
            ],
        ]);
    }

    public function createReferralMenu(string $language = 'en'): InlineKeyboard
    {
        return new InlineKeyboard([
            [
                InlineKeyboardButton::make(
                    '🔗 My Referral Link',
                    callback_data: 'referral_link'
                ),
            ],
            [
                InlineKeyboardButton::make(
                    '📊 Referral Stats',
                    callback_data: 'referral_stats'
                ),
            ],
            [
                InlineKeyboardButton::make(
                    '🎁 Claim Rewards',
                    callback_data: 'claim_rewards'
                ),
            ],
            [
                InlineKeyboardButton::make(
                    $this->localization->get('buttons.back', $language),
                    callback_data: 'back_main'
                ),
            ],
        ]);
    }

    public function createConfirmationMenu(string $language = 'en', string $confirmAction = '', string $cancelAction = 'back_main'): InlineKeyboard
    {
        return new InlineKeyboard([
            [
                InlineKeyboardButton::make(
                    '✅ Confirm',
                    callback_data: $confirmAction
                ),
                InlineKeyboardButton::make(
                    '❌ Cancel',
                    callback_data: $cancelAction
                ),
            ],
        ]);
    }

    public function createBackButton(string $language = 'en', string $action = 'back_main'): InlineKeyboard
    {
        return new InlineKeyboard([
            [
                InlineKeyboardButton::make(
                    $this->localization->get('buttons.back', $language),
                    callback_data: $action
                ),
            ],
        ]);
    }

    public function createChannelSubscriptionMenu(array $channels, string $language = 'en'): InlineKeyboard
    {
        $buttons = [];
        
        foreach ($channels as $channel) {
            $buttons[] = [
                InlineKeyboardButton::make(
                    "📢 {$channel['name']}",
                    url: $channel['url']
                ),
            ];
        }

        $buttons[] = [
            InlineKeyboardButton::make(
                $this->localization->get('buttons.check_subscription', $language),
                callback_data: 'check_subscription'
            ),
        ];

        return new InlineKeyboard($buttons);
    }
}
