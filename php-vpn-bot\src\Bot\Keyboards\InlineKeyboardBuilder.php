<?php

declare(strict_types=1);

namespace VpnBot\Bot\Keyboards;

use <PERSON><PERSON>\TelegramBot\Entities\Keyboard;
use Longman\TelegramBot\Entities\KeyboardButton;
use VpnBot\Utils\Localization;

class ReplyKeyboardBuilder
{
    private Localization $localization;

    public function __construct()
    {
        $this->localization = Localization::getInstance();
    }

    public function createMainMenu(string $language = 'en'): Keyboard
    {
        return new Keyboard([
            [
                KeyboardButton::make($this->localization->get('buttons.trial_vpn', $language)),
                KeyboardButton::make($this->localization->get('buttons.premium', $language)),
            ],
            [
                KeyboardButton::make($this->localization->get('buttons.dashboard', $language)),
                KeyboardButton::make($this->localization->get('buttons.my_accounts', $language)),
            ],
            [
                KeyboardButton::make($this->localization->get('buttons.referral', $language)),
                KeyboardButton::make($this->localization->get('buttons.settings', $language)),
            ],
            [
                KeyboardButton::make($this->localization->get('buttons.help', $language)),
                KeyboardButton::make($this->localization->get('buttons.support', $language)),
            ],
        ], [
            'resize_keyboard' => true,
            'one_time_keyboard' => false,
            'selective' => false,
        ]);
    }

    public function createPremiumPlansMenu(string $language = 'en'): Keyboard
    {
        return new Keyboard([
            [
                KeyboardButton::make('💎 1 Month - $9.99'),
                KeyboardButton::make('💎 3 Months - $24.99'),
            ],
            [
                KeyboardButton::make('💎 6 Months - $44.99'),
                KeyboardButton::make('💎 1 Year - $79.99'),
            ],
            [
                KeyboardButton::make($this->localization->get('buttons.back', $language)),
                KeyboardButton::make($this->localization->get('buttons.main_menu', $language)),
            ],
        ], [
            'resize_keyboard' => true,
            'one_time_keyboard' => false,
            'selective' => false,
        ]);
    }

    public function createPaymentMethodsMenu(string $language = 'en'): Keyboard
    {
        return new Keyboard([
            [
                KeyboardButton::make($this->localization->get('payment.stars', $language)),
                KeyboardButton::make($this->localization->get('payment.card', $language)),
            ],
            [
                KeyboardButton::make($this->localization->get('payment.crypto', $language)),
                KeyboardButton::make($this->localization->get('payment.ton', $language)),
            ],
            [
                KeyboardButton::make($this->localization->get('buttons.back', $language)),
                KeyboardButton::make($this->localization->get('buttons.main_menu', $language)),
            ],
        ], [
            'resize_keyboard' => true,
            'one_time_keyboard' => false,
            'selective' => false,
        ]);
    }

    public function createSettingsMenu(string $language = 'en'): Keyboard
    {
        return new Keyboard([
            [
                KeyboardButton::make($this->localization->get('buttons.change_language', $language)),
            ],
            [
                KeyboardButton::make('🔔 Notifications'),
            ],
            [
                KeyboardButton::make($this->localization->get('buttons.back', $language)),
                KeyboardButton::make($this->localization->get('buttons.main_menu', $language)),
            ],
        ], [
            'resize_keyboard' => true,
            'one_time_keyboard' => false,
            'selective' => false,
        ]);
    }

    public function createLanguageSelectionMenu(string $currentLanguage = 'en'): Keyboard
    {
        $languages = [
            'en' => '🇺🇸 English',
            'fa' => '🇮🇷 فارسی',
            'ru' => '🇷🇺 Русский',
            'zh' => '🇨🇳 中文',
        ];

        $buttons = [];
        foreach ($languages as $code => $name) {
            $text = $code === $currentLanguage ? "✅ $name" : $name;
            $buttons[] = [KeyboardButton::make($text)];
        }

        $buttons[] = [
            KeyboardButton::make($this->localization->get('buttons.back', $currentLanguage)),
            KeyboardButton::make($this->localization->get('buttons.main_menu', $currentLanguage)),
        ];

        return new Keyboard($buttons, [
            'resize_keyboard' => true,
            'one_time_keyboard' => false,
            'selective' => false,
        ]);
    }

    public function createAccountsMenu(string $language = 'en'): Keyboard
    {
        return new Keyboard([
            [
                KeyboardButton::make('🔄 Refresh'),
                KeyboardButton::make('📊 Usage Stats'),
            ],
            [
                KeyboardButton::make($this->localization->get('buttons.back', $language)),
                KeyboardButton::make($this->localization->get('buttons.main_menu', $language)),
            ],
        ], [
            'resize_keyboard' => true,
            'one_time_keyboard' => false,
            'selective' => false,
        ]);
    }

    public function createReferralMenu(string $language = 'en'): Keyboard
    {
        return new Keyboard([
            [
                KeyboardButton::make('🔗 My Referral Link'),
                KeyboardButton::make('📊 Referral Stats'),
            ],
            [
                KeyboardButton::make('🎁 Claim Rewards'),
            ],
            [
                KeyboardButton::make($this->localization->get('buttons.back', $language)),
                KeyboardButton::make($this->localization->get('buttons.main_menu', $language)),
            ],
        ], [
            'resize_keyboard' => true,
            'one_time_keyboard' => false,
            'selective' => false,
        ]);
    }

    public function createConfirmationMenu(string $language = 'en'): Keyboard
    {
        return new Keyboard([
            [
                KeyboardButton::make('✅ Confirm'),
                KeyboardButton::make('❌ Cancel'),
            ],
            [
                KeyboardButton::make($this->localization->get('buttons.back', $language)),
                KeyboardButton::make($this->localization->get('buttons.main_menu', $language)),
            ],
        ], [
            'resize_keyboard' => true,
            'one_time_keyboard' => false,
            'selective' => false,
        ]);
    }

    public function createBackMenu(string $language = 'en'): Keyboard
    {
        return new Keyboard([
            [
                KeyboardButton::make($this->localization->get('buttons.back', $language)),
                KeyboardButton::make($this->localization->get('buttons.main_menu', $language)),
            ],
        ], [
            'resize_keyboard' => true,
            'one_time_keyboard' => false,
            'selective' => false,
        ]);
    }

    public function createChannelSubscriptionMenu(array $channels, string $language = 'en'): Keyboard
    {
        return new Keyboard([
            [
                KeyboardButton::make($this->localization->get('buttons.check_subscription', $language)),
            ],
            [
                KeyboardButton::make($this->localization->get('buttons.back', $language)),
                KeyboardButton::make($this->localization->get('buttons.main_menu', $language)),
            ],
        ], [
            'resize_keyboard' => true,
            'one_time_keyboard' => false,
            'selective' => false,
        ]);
    }
}
