﻿"""Command handlers for the Telegram bot."""

import logging
from typing import Dict, Any, Optional

from telegram import Update, KeyboardButton, ReplyKeyboardMarkup, Reply<PERSON><PERSON>boardRemove
from telegram.ext import ContextTypes
from telegram.error import TelegramError
from bot.services.vpn_service import vpn_service
from bot.services.payment_service import payment_service
from bot.services.channel_service import channel_service
from bot.services.dashboard_service import dashboard_service
from bot.utils.helpers import get_text
from bot.utils.buttons import ReplyKeyboardBuilder
from bot.database import get_db_connection

logger = logging.getLogger(__name__)

# User navigation states
class UserState:
    MAIN_MENU = "main_menu"
    PREMIUM_PACKAGES = "premium_packages"
    PAYMENT_METHODS = "payment_methods"
    SETTINGS = "settings"
    ACCOUNTS = "accounts"
    LANGUAGE_SELECTION = "language_selection"


class CommandHandlers:
    """Handlers for bot commands."""

    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)

    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /start command."""
        try:
            user = update.effective_user

            # Check for referral code in start command
            referral_code = None
            if context.args and len(context.args) > 0:
                arg = context.args[0]
                if arg.startswith('ref_'):
                    referral_code = arg[4:]  # Remove 'ref_' prefix

            # Check if user needs language selection first
            if context.user_data.get('needs_language_selection', False):
                from bot.handlers.language_selection import language_selection_handler
                await language_selection_handler.show_language_selection(update, context)
                # Store referral code for after language selection
                if referral_code:
                    context.user_data['pending_referral_code'] = referral_code
                return

            # Get user language from context (set by auth middleware)
            user_data = context.user_data.get('user', {})
            language = user_data.get('language_code', 'en')

            # Process referral code if present
            if referral_code or context.user_data.get('pending_referral_code'):
                referral_code = referral_code or context.user_data.get('pending_referral_code')
                context.user_data.pop('pending_referral_code', None)

                from bot.handlers.referral import referral_handler
                await referral_handler.process_referral_start(update, context, referral_code)

            # Check if user is subscribed to required channels
            subscription_status = await channel_service.verify_all_subscriptions(
                context.bot, user.id
            )

            if not subscription_status.get('is_subscribed', False):
                # Show subscription requirement message
                message = channel_service.format_subscription_message(
                    subscription_status, language
                )
                message += "\n\n" + get_text('buttons.check_subscription', language)

                await update.message.reply_text(
                    message,
                    parse_mode='Markdown'
                )
                return

            # Enhanced welcome message for subscribed users
            welcome_title = get_text('welcome.title', language, name=user.first_name)
            welcome_description = get_text('welcome.description', language)
            welcome_text = f"{welcome_title}\n\n{welcome_description}"

            # Send message with reply keyboard
            reply_keyboard = self._create_reply_keyboard(language)
            await update.message.reply_text(
                welcome_text,
                reply_markup=reply_keyboard,
                parse_mode='Markdown'
            )

            # Note: Inline keyboard removed - all navigation now uses reply keyboard

        except TelegramError as e:
            self.logger.error("Error in start command: %s", e)
            await update.message.reply_text(
                get_text('errors.general_error', user_data.get('language_code', 'en'))
            )

    async def menu_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /menu command."""
        try:
            user_data = context.user_data.get('user', {})
            language = user_data.get('language_code', 'en')
            
            # Send main menu with reply keyboard
            await update.message.reply_text(
                get_text('menu.title', language),
                reply_markup=self._create_reply_keyboard(language)
            )
            
            # Note: Inline keyboard removed - all navigation now uses reply keyboard
        except TelegramError as e:
            self.logger.error("Error in menu command: %s", e)
            await update.message.reply_text(
                get_text('errors.general_error', context.user_data.get('user', {}).get('language_code', 'en'))
            )



    async def handle_reply_keyboard(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle reply keyboard button presses with state management."""
        try:
            user_data = context.user_data.get('user', {})
            language = user_data.get('language_code', 'en')
            message_text = update.message.text
            current_state = context.user_data.get('navigation_state', UserState.MAIN_MENU)
            
            # Handle language selection (always available)
            if message_text in ["🇮🇷 فارسی", "🇺🇸 English", "🇷🇺 Русский", "🇨🇳 中文"]:
                await self._handle_language_selection(update, context, message_text)
                return
            
            # Handle navigation buttons
            if message_text == get_text('buttons.main_menu', language):
                await self._show_main_menu(update, context, language)
                return
            elif message_text == get_text('buttons.back', language):
                await self._handle_back_navigation(update, context, language)
                return
            
            # Handle state-specific buttons
            if current_state == UserState.MAIN_MENU:
                await self._handle_main_menu_buttons(update, context, message_text, language)
            elif current_state == UserState.PREMIUM_PACKAGES:
                await self._handle_premium_packages_buttons(update, context, message_text, language)
            elif current_state == UserState.PAYMENT_METHODS:
                await self._handle_payment_methods_buttons(update, context, message_text, language)
            elif current_state == UserState.SETTINGS:
                await self._handle_settings_buttons(update, context, message_text, language)
            elif current_state == UserState.ACCOUNTS:
                await self._handle_accounts_buttons(update, context, message_text, language)
            elif current_state == UserState.LANGUAGE_SELECTION:
                await self._handle_language_selection_buttons(update, context, message_text, language)
            else:
                # Unknown state or button - show main menu
                await self._show_main_menu(update, context, language)
                    
        except Exception as e:
            logger.error("Error handling reply keyboard: %s", e)
            language = context.user_data.get('user', {}).get('language_code', 'en')
            await update.message.reply_text(
                get_text('errors.general_error', language),
                reply_markup=ReplyKeyboardBuilder.create_main_menu(language)
            )
            context.user_data['navigation_state'] = UserState.MAIN_MENU











    def _format_account_message_and_keyboard(
            self, accounts: list, language: str
    ) -> tuple[str, list]:
        """Helper to format account message and keyboard."""
        message = get_text('vpn.accounts_title', language).format(count=len(accounts)) + "\n\n"
        
        for i, account in enumerate(accounts, 1):
            status = "🟢" if account.get('is_active') else "🔴"
            acc_type = (
                get_text('vpn.premium', language)
                if account.get('account_type') == 'premium'
                else get_text('vpn.trial', language)
            )

            message += f"{i}. {status} **{account.get('username', 'Unknown')}** ({acc_type})\n"
            if account.get('expires_at'):
                expires_text = get_text('vpn.expires', language)
                message += f"   📅 {expires_text}: {account['expires_at'][:10]}\n"
            
            message += f"   📋 {get_text('vpn.use_account_details', language)}\n\n"

        message += f"\n💡 {get_text('vpn.use_keyboard_navigation', language)}"
        return message, []







    def _create_reply_keyboard(self, language: str) -> ReplyKeyboardMarkup:
        """Create the main reply keyboard."""
        return ReplyKeyboardBuilder.create_main_menu(language)
    
    async def _show_main_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE, language: str):
        """Show the main menu."""
        context.user_data['navigation_state'] = UserState.MAIN_MENU
        await update.message.reply_text(
            get_text('welcome.description', language),
            reply_markup=ReplyKeyboardBuilder.create_main_menu(language),
            reply_to_message_id=None
        )
    
    async def _handle_language_selection(self, update: Update, context: ContextTypes.DEFAULT_TYPE, message_text: str):
        """Handle language selection."""
        language_mapping = {
            "🇮🇷 فارسی": 'fa',
            "🇺🇸 English": 'en',
            "🇷🇺 Русский": 'ru',
            "🇨🇳 中文": 'zh'
        }
        
        language_code = language_mapping.get(message_text)
        if language_code:
            from bot.handlers.language_selection import LanguageSelectionHandler
            language_handler = LanguageSelectionHandler()
            await language_handler.handle_language_selection(update, context, language_code)
    
    async def _handle_back_navigation(self, update: Update, context: ContextTypes.DEFAULT_TYPE, language: str):
        """Handle back button navigation."""
        current_state = context.user_data.get('navigation_state', UserState.MAIN_MENU)
        
        if current_state in [UserState.PREMIUM_PACKAGES, UserState.SETTINGS, UserState.ACCOUNTS]:
            await self._show_main_menu(update, context, language)
        elif current_state == UserState.PAYMENT_METHODS:
            await self._show_premium_packages(update, context, language)
        elif current_state == UserState.LANGUAGE_SELECTION:
            await self._show_settings(update, context, language)
        else:
            await self._show_main_menu(update, context, language)
    
    async def _handle_main_menu_buttons(self, update: Update, context: ContextTypes.DEFAULT_TYPE, message_text: str, language: str):
        """Handle main menu button presses."""
        if message_text == get_text('buttons.trial_vpn', language):
            await self._handle_trial_vpn(update, context, language)
        elif message_text == get_text('buttons.premium', language):
            await self._show_premium_packages(update, context, language)
        elif message_text == get_text('buttons.dashboard', language):
            await self._handle_dashboard(update, context, language)
        elif message_text == get_text('buttons.my_accounts', language):
            await self._show_accounts(update, context, language)
        elif message_text == get_text('buttons.referral', language):
            await self._handle_referral(update, context, language)
        elif message_text == get_text('buttons.earn', language):
            await self._handle_referral(update, context, language)  # Earn button leads to referral system
        elif message_text == get_text('buttons.settings', language):
            await self._show_settings(update, context, language)
        elif message_text == get_text('buttons.help', language):
            await self._handle_help(update, context, language)
        elif message_text == get_text('buttons.support', language):
            await self._handle_support(update, context, language)
        elif message_text == get_text('buttons.referral_link', language):
            await self._handle_referral_link(update, context, language)
        elif message_text == get_text('buttons.referral_stats', language):
            await self._handle_referral_stats(update, context, language)
        else:
            await update.message.reply_text(
                get_text('errors.unknown_command', language),
                reply_markup=ReplyKeyboardBuilder.create_main_menu(language)
            )
    
    async def _show_premium_packages(self, update: Update, context: ContextTypes.DEFAULT_TYPE, language: str):
        """Show premium packages menu."""
        context.user_data['navigation_state'] = UserState.PREMIUM_PACKAGES

        premium_text = get_text('premium.title', language) + "\n\n"
        premium_text += get_text('premium.description', language) + "\n\n"

        # Add package details from localization
        premium_text += f"💎 **{get_text('premium.packages.basic.name', language)}** - {get_text('premium.packages.basic.price', language)}\n"
        premium_text += get_text('premium.packages.basic.features', language) + "\n\n"

        premium_text += f"💎 **{get_text('premium.packages.standard.name', language)}** - {get_text('premium.packages.standard.price', language)}\n"
        premium_text += get_text('premium.packages.standard.features', language) + "\n\n"

        premium_text += f"💎 **{get_text('premium.packages.premium.name', language)}** - {get_text('premium.packages.premium.price', language)}\n"
        premium_text += get_text('premium.packages.premium.features', language) + "\n\n"

        await update.message.reply_text(
            premium_text,
            reply_markup=ReplyKeyboardBuilder.create_premium_packages_menu(language),
            parse_mode='Markdown'
        )
    
    async def _handle_premium_packages_buttons(self, update: Update, context: ContextTypes.DEFAULT_TYPE, message_text: str, language: str):
        """Handle premium package selection."""
        package_mapping = {
            "💎 1 Month - $9.99": {"name": "1 Month Premium", "price": 9.99, "duration": 30},
            "💎 3 Months - $24.99": {"name": "3 Months Premium", "price": 24.99, "duration": 90},
            "💎 6 Months - $44.99": {"name": "6 Months Premium", "price": 44.99, "duration": 180},
            "💎 1 Year - $79.99": {"name": "1 Year Premium", "price": 79.99, "duration": 365}
        }
        
        package = package_mapping.get(message_text)
        if package:
            context.user_data['selected_package'] = package
            await self._show_payment_methods(update, context, language, package)
        else:
            await update.message.reply_text(
                get_text('errors.unknown_command', language),
                reply_markup=ReplyKeyboardBuilder.create_premium_packages_menu(language)
            )
    
    async def _show_payment_methods(self, update: Update, context: ContextTypes.DEFAULT_TYPE, language: str, package: dict):
        """Show payment methods menu."""
        context.user_data['navigation_state'] = UserState.PAYMENT_METHODS
        await update.message.reply_text(
            f"💎 **{package['name']}**\n" +
            f"💰 {get_text('payment.price', language)}: ${package['price']}\n" +
            f"⏰ {get_text('payment.duration', language)}: {package['duration']} {get_text('payment.days', language)}\n\n" +
            get_text('payment_methods.select', language),
            reply_markup=ReplyKeyboardBuilder.create_payment_methods_menu(language),
            parse_mode='Markdown'
        )
    
    async def _handle_payment_methods_buttons(self, update: Update, context: ContextTypes.DEFAULT_TYPE, message_text: str, language: str):
        """Handle payment method selection."""
        package = context.user_data.get('selected_package', {})
        
        if message_text == get_text('buttons.pay_with_stars', language):
            await self._handle_stars_payment(update, context, language, package)
        elif message_text == get_text('buttons.card_payment', language):
            await self._handle_card_payment(update, context, language, package)
        elif message_text == get_text('buttons.crypto_payment', language):
            await self._handle_crypto_payment(update, context, language, package)
        elif message_text == get_text('buttons.ton_payment', language):
            await self._handle_ton_payment(update, context, language, package)
        else:
            await update.message.reply_text(
                get_text('errors.unknown_command', language),
                reply_markup=ReplyKeyboardBuilder.create_payment_methods_menu(language)
            )
    
    async def _show_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE, language: str):
        """Show settings menu."""
        context.user_data['navigation_state'] = UserState.SETTINGS
        await update.message.reply_text(
            get_text('settings.title', language),
            reply_markup=ReplyKeyboardBuilder.create_settings_menu(language)
        )
    
    async def _handle_settings_buttons(self, update: Update, context: ContextTypes.DEFAULT_TYPE, message_text: str, language: str):
        """Handle settings button presses."""
        if message_text == get_text('buttons.change_language', language):
            await update.message.reply_text(
                get_text('settings.language_selection', language),
                reply_markup=ReplyKeyboardBuilder.create_language_selection_menu(language)
            )
            context.user_data['navigation_state'] = UserState.LANGUAGE_SELECTION
        elif message_text == get_text('buttons.notifications', language):
            await update.message.reply_text(
                get_text('notifications.title', language) + "\n\n" + get_text('notifications.description', language),
                reply_markup=ReplyKeyboardBuilder.create_settings_menu(language)
            )
        else:
            await update.message.reply_text(
                get_text('errors.unknown_command', language),
                reply_markup=ReplyKeyboardBuilder.create_settings_menu(language)
            )

    async def _handle_language_selection_buttons(self, update: Update, context: ContextTypes.DEFAULT_TYPE, message_text: str, language: str):
        """Handle language selection buttons in settings menu."""
        new_language = None

        # Map language buttons to language codes
        if message_text in ["🇮🇷 فارسی", "فارسی"]:
            new_language = 'fa'
        elif message_text in ["🇺🇸 English", "English"]:
            new_language = 'en'
        elif message_text in ["🇷🇺 Русский", "Русский"]:
            new_language = 'ru'
        elif message_text in ["🇨🇳 中文", "中文"]:
            new_language = 'zh'

        if new_language:
            # Update user language in database
            try:
                user_data = context.user_data.get('user', {})
                user_id = user_data.get('id')

                if user_id:
                    # Update language in database
                    async with get_db_connection() as conn:
                        await conn.execute(
                            "UPDATE users SET language_code = $1 WHERE id = $2",
                            new_language, user_id
                        )

                    # Update context
                    context.user_data['user']['language_code'] = new_language

                    # Show confirmation and return to main menu
                    await update.message.reply_text(
                        get_text('settings.language_changed', new_language),
                        reply_markup=ReplyKeyboardBuilder.create_main_menu(new_language)
                    )
                    context.user_data['navigation_state'] = UserState.MAIN_MENU
                else:
                    await update.message.reply_text(
                        get_text('errors.auth_required', language),
                        reply_markup=ReplyKeyboardBuilder.create_main_menu(language)
                    )
            except Exception as e:
                self.logger.error(f"Error updating language: {e}")
                await update.message.reply_text(
                    get_text('errors.general_error', language),
                    reply_markup=ReplyKeyboardBuilder.create_settings_menu(language)
                )
        else:
            # Unknown language selection - back to settings
            await update.message.reply_text(
                get_text('settings.invalid_language', language),
                reply_markup=ReplyKeyboardBuilder.create_settings_menu(language)
            )
            context.user_data['navigation_state'] = UserState.SETTINGS

    async def _show_accounts(self, update: Update, context: ContextTypes.DEFAULT_TYPE, language: str):
        """Show accounts menu."""
        context.user_data['navigation_state'] = UserState.ACCOUNTS
        
        # Get user accounts
        try:
            user_data = context.user_data.get('user', {})
            user_id = user_data.get('id')
            
            if user_id:
                accounts = await vpn_service.get_user_accounts(user_id)
                if accounts:
                    accounts_text = get_text('vpn.accounts_title', language).format(count=len(accounts)) + "\n\n"
                    for i, account in enumerate(accounts, 1):
                        accounts_text += f"{i}. **{account.get('username', 'N/A')}**\n"
                        accounts_text += f"📊 Status: {account.get('status', 'Unknown')}\n"
                        accounts_text += f"⏰ Expires: {account.get('expire_date', 'N/A')}\n\n"
                else:
                    accounts_text = get_text('vpn.no_accounts', language)
            else:
                accounts_text = get_text('vpn.no_accounts', language)
                
        except Exception as e:
            logger.error(f"Error getting accounts: {e}")
            accounts_text = get_text('errors.general_error', language)
        
        await update.message.reply_text(
            accounts_text,
            reply_markup=ReplyKeyboardBuilder.create_accounts_menu(language),
            parse_mode='Markdown'
        )
    
    async def _handle_accounts_buttons(self, update: Update, context: ContextTypes.DEFAULT_TYPE, message_text: str, language: str):
        """Handle accounts button presses."""
        if message_text == get_text('buttons.refresh', language):
            await self._show_accounts(update, context, language)
        else:
            await update.message.reply_text(
                get_text('errors.unknown_command', language),
                reply_markup=ReplyKeyboardBuilder.create_accounts_menu(language)
            )
    
    async def _handle_trial_vpn(self, update: Update, context: ContextTypes.DEFAULT_TYPE, language: str):
        """Handle trial VPN request."""
        try:
            user_data = context.user_data.get('user', {})
            user_id = user_data.get('id')
            telegram_id = update.effective_user.id

            if user_id:
                # Check trial eligibility including channel subscriptions
                eligibility = await vpn_service.check_trial_eligibility(telegram_id, context.bot)

                if not eligibility['eligible']:
                    reason = eligibility['reason']

                    if reason == 'Trial already used':
                        await update.message.reply_text(
                            get_text('trial.already_used', language),
                            reply_markup=ReplyKeyboardBuilder.create_main_menu(language)
                        )
                    elif reason == 'Channel subscription required':
                        # Show subscription required message with missing channels
                        missing_channels = eligibility.get('missing_channels', [])
                        subscribed_count = eligibility.get('subscribed_count', 0)
                        total_required = eligibility.get('total_required', 0)

                        message = get_text('trial.subscription_required', language) + "\n\n"
                        message += f"📊 {get_text('trial.subscription_progress', language)}: {subscribed_count}/{total_required}\n\n"
                        message += get_text('trial.missing_channels', language) + "\n"

                        for i, channel in enumerate(missing_channels, 1):
                            channel_name = channel.get('channel_name', f"Channel {i}")
                            if channel.get('invite_link'):
                                message += f"{i}. [{channel_name}]({channel['invite_link']})\n"
                            elif channel.get('channel_url'):
                                message += f"{i}. [{channel_name}]({channel['channel_url']})\n"
                            else:
                                channel_id = channel.get('channel_id', '')
                                if channel_id.startswith('@'):
                                    message += f"{i}. [{channel_name}](https://t.me/{channel_id[1:]})\n"
                                else:
                                    message += f"{i}. {channel_name}\n"

                        message += f"\n{get_text('trial.after_subscription', language)}"

                        await update.message.reply_text(
                            message,
                            reply_markup=ReplyKeyboardBuilder.create_main_menu(language),
                            parse_mode='Markdown',
                            disable_web_page_preview=True
                        )
                    else:
                        await update.message.reply_text(
                            get_text('trial.not_eligible', language).format(reason=reason),
                            reply_markup=ReplyKeyboardBuilder.create_main_menu(language)
                        )
                    return

                # Create trial account
                trial_account = await vpn_service.create_trial_account(user_id)
                if trial_account:
                    await update.message.reply_text(
                        get_text('trial.success', language).format(
                            username=trial_account.get('username', 'N/A'),
                            config=trial_account.get('config', 'N/A')
                        ),
                        reply_markup=ReplyKeyboardBuilder.create_main_menu(language),
                        parse_mode='Markdown'
                    )
                else:
                    await update.message.reply_text(
                        get_text('trial.error', language),
                        reply_markup=ReplyKeyboardBuilder.create_main_menu(language)
                    )
            else:
                await update.message.reply_text(
                    get_text('errors.auth_required', language),
                    reply_markup=ReplyKeyboardBuilder.create_main_menu(language)
                )
        except Exception as e:
            logger.error(f"Error creating trial VPN: {e}")
            await update.message.reply_text(
                get_text('errors.general_error', language),
                reply_markup=ReplyKeyboardBuilder.create_main_menu(language)
            )
    
    async def _handle_dashboard(self, update: Update, context: ContextTypes.DEFAULT_TYPE, language: str):
        """Handle dashboard request."""
        try:
            user_data = context.user_data.get('user', {})
            user_id = user_data.get('id')
            
            if user_id:
                dashboard_data = await dashboard_service.get_user_dashboard(user_id)
                dashboard_text = get_text('dashboard.title', language) + "\n\n"
                dashboard_text += f"👤 {get_text('dashboard.user_id', language)}: {user_id}\n"
                dashboard_text += f"📊 {get_text('dashboard.active_accounts', language)}: {dashboard_data.get('active_accounts', 0)}\n"
                dashboard_text += f"💰 {get_text('dashboard.total_spent', language)}: ${dashboard_data.get('total_spent', 0)}\n"
                dashboard_text += f"📅 {get_text('dashboard.member_since', language)}: {dashboard_data.get('member_since', get_text('unknown', language))}\n"
                
                await update.message.reply_text(
                    dashboard_text,
                    reply_markup=ReplyKeyboardBuilder.create_main_menu(language),
                    parse_mode='Markdown'
                )
            else:
                await update.message.reply_text(
                    get_text('errors.auth_required', language),
                    reply_markup=ReplyKeyboardBuilder.create_main_menu(language)
                )
        except Exception as e:
            logger.error(f"Error getting dashboard: {e}")
            await update.message.reply_text(
                get_text('errors.general_error', language),
                reply_markup=ReplyKeyboardBuilder.create_main_menu(language)
            )
    
    async def _handle_help(self, update: Update, context: ContextTypes.DEFAULT_TYPE, language: str):
        """Handle help request."""
        help_text = get_text('help.title', language) + "\n\n"
        help_text += get_text('help.description', language) + "\n\n"
        help_text += f"📱 **{get_text('help.commands_title', language)}:**\n"
        help_text += get_text('help.commands.start', language) + "\n"
        help_text += get_text('help.commands.menu', language) + "\n"
        help_text += get_text('help.commands.trial', language) + "\n"
        help_text += get_text('help.commands.premium', language) + "\n"
        help_text += get_text('help.commands.dashboard', language) + "\n"
        help_text += get_text('help.commands.accounts', language) + "\n"
        help_text += get_text('help.commands.settings', language) + "\n"
        help_text += get_text('help.commands.support', language) + "\n"
        help_text += get_text('help.commands.help', language) + "\n"

        await update.message.reply_text(
            help_text,
            reply_markup=ReplyKeyboardBuilder.create_main_menu(language),
            parse_mode='Markdown'
        )
    
    async def _handle_support(self, update: Update, context: ContextTypes.DEFAULT_TYPE, language: str):
        """Handle support request."""
        support_text = get_text('support.title', language) + "\n\n"
        support_text += get_text('support.description', language) + "\n\n"
        support_text += get_text('support.contact_info', language)

        await update.message.reply_text(
            support_text,
            reply_markup=ReplyKeyboardBuilder.create_main_menu(language),
            parse_mode='Markdown'
        )

    async def _handle_referral(self, update: Update, context: ContextTypes.DEFAULT_TYPE, language: str):
        """Handle referral menu."""
        try:
            from bot.handlers.referral import referral_handler
            await referral_handler.handle_referral_command(update, context)
        except Exception as e:
            logger.error(f"Error handling referral: {e}")
            await update.message.reply_text(
                get_text('errors.general_error', language),
                reply_markup=ReplyKeyboardBuilder.create_main_menu(language)
            )

    async def _handle_referral_link(self, update: Update, context: ContextTypes.DEFAULT_TYPE, language: str):
        """Handle referral link request."""
        try:
            from bot.handlers.referral import referral_handler
            await referral_handler.handle_referral_link_command(update, context)
        except Exception as e:
            logger.error(f"Error handling referral link: {e}")
            await update.message.reply_text(
                get_text('errors.general_error', language),
                reply_markup=ReplyKeyboardBuilder.create_main_menu(language)
            )

    async def _handle_referral_stats(self, update: Update, context: ContextTypes.DEFAULT_TYPE, language: str):
        """Handle referral stats request."""
        try:
            from bot.handlers.referral import referral_handler
            await referral_handler.handle_referral_command(update, context)
        except Exception as e:
            logger.error(f"Error handling referral stats: {e}")
            await update.message.reply_text(
                get_text('errors.general_error', language),
                reply_markup=ReplyKeyboardBuilder.create_main_menu(language)
            )
    
    async def _handle_stars_payment(self, update: Update, context: ContextTypes.DEFAULT_TYPE, language: str, package: dict):
        """Handle Telegram Stars payment."""
        try:
            from bot.handlers.payments import PaymentHandler
            payment_handler = PaymentHandler()
            await payment_handler.handle_stars_payment(update, context, package)
            
            await update.message.reply_text(
                f"⭐ **Telegram Stars Payment**\n\n"
                f"Package: {package['name']}\n"
                f"Price: {int(package['price'] * 100)} Stars\n\n"
                f"Payment initiated! Please complete the payment.",
                reply_markup=ReplyKeyboardBuilder.create_back_main_menu(language),
                parse_mode='Markdown'
            )
        except Exception as e:
            logger.error(f"Error processing Stars payment: {e}")
            await update.message.reply_text(
                get_text('payment.error', language),
                reply_markup=ReplyKeyboardBuilder.create_payment_methods_menu(language)
            )
    
    async def _handle_card_payment(self, update: Update, context: ContextTypes.DEFAULT_TYPE, language: str, package: dict):
        """Handle card payment."""
        try:
            # Show processing message first
            processing_msg = await update.message.reply_text(
                get_text('creating_card_payment', language),
                reply_markup=ReplyKeyboardBuilder.create_back_main_menu(language)
            )

            from bot.handlers.payments import PaymentHandler
            payment_handler = PaymentHandler()
            payment_url = await payment_handler.create_card_payment(update, context, package)

            if payment_url:
                # Format the payment message with localization
                payment_message = get_text('card_payment_details', language).format(
                    package_name=package['name'],
                    price=package['price'],
                    payment_url=payment_url
                )

                await processing_msg.edit_text(
                    payment_message,
                    reply_markup=ReplyKeyboardBuilder.create_back_main_menu(language),
                    parse_mode='Markdown'
                )

                # Send confirmation message
                await update.message.reply_text(
                    get_text('card_payment_sent', language),
                    reply_markup=ReplyKeyboardBuilder.create_payment_methods_menu(language)
                )
            else:
                await processing_msg.edit_text(
                    get_text('errors.card_payment_error', language),
                    reply_markup=ReplyKeyboardBuilder.create_payment_methods_menu(language)
                )

        except Exception as e:
            logger.error(f"Error processing card payment: {e}")
            await update.message.reply_text(
                get_text('errors.card_payment_error', language),
                reply_markup=ReplyKeyboardBuilder.create_payment_methods_menu(language)
            )
    
    async def _handle_crypto_payment(self, update: Update, context: ContextTypes.DEFAULT_TYPE, language: str, package: dict):
        """Handle cryptocurrency payment."""
        try:
            from bot.handlers.payments import PaymentHandler
            payment_handler = PaymentHandler()
            crypto_data = await payment_handler.create_crypto_payment(update, context, package)
            
            await update.message.reply_text(
                f"₿ **Cryptocurrency Payment**\n\n"
                f"Package: {package['name']}\n"
                f"Price: ${package['price']}\n\n"
                f"Address: `{crypto_data.get('address', 'N/A')}`\n"
                f"Amount: {crypto_data.get('amount', 'N/A')} {crypto_data.get('currency', 'BTC')}\n\n"
                f"Send the exact amount to the address above.",
                reply_markup=ReplyKeyboardBuilder.create_back_main_menu(language),
                parse_mode='Markdown'
            )
        except Exception as e:
            logger.error(f"Error processing crypto payment: {e}")
            await update.message.reply_text(
                get_text('payment.error', language),
                reply_markup=ReplyKeyboardBuilder.create_payment_methods_menu(language)
            )
    
    async def _handle_ton_payment(self, update: Update, context: ContextTypes.DEFAULT_TYPE, language: str, package: dict):
        """Handle TON payment."""
        try:
            from bot.handlers.payments import PaymentHandler
            payment_handler = PaymentHandler()
            ton_data = await payment_handler.create_ton_payment(update, context, package)
            
            await update.message.reply_text(
                f"💎 **TON Payment**\n\n"
                f"Package: {package['name']}\n"
                f"Price: ${package['price']}\n\n"
                f"TON Address: `{ton_data.get('address', 'N/A')}`\n"
                f"Amount: {ton_data.get('amount', 'N/A')} TON\n\n"
                f"Send the exact amount to the address above.",
                reply_markup=ReplyKeyboardBuilder.create_back_main_menu(language),
                parse_mode='Markdown'
            )
        except Exception as e:
            logger.error(f"Error processing TON payment: {e}")
            await update.message.reply_text(
                get_text('payment.error', language),
                reply_markup=ReplyKeyboardBuilder.create_payment_methods_menu(language)
            )




# Global instance
command_handlers = CommandHandlers()
