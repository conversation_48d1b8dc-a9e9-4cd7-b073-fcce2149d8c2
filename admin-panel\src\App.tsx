import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Link, useLocation } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import './App.css';
import Dashboard from './components/Dashboard';
import Panels from './components/Panels';
import Plans from './components/Plans';
import Users from './components/Users';
import Settings from './components/Settings';

// Create a client with enhanced error handling
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: (failureCount, error: any) => {
        // Don't retry on authentication errors
        if (error?.response?.status === 401 || error?.response?.status === 403) {
          return false;
        }
        // Retry up to 2 times for other errors
        return failureCount < 2;
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
    },
    mutations: {
      retry: false,
    },
  },
});

// Navigation component with active link highlighting
const Navigation: React.FC = () => {
  const location = useLocation();
  
  const isActive = (path: string) => {
    return location.pathname === path ? 'active' : '';
  };
  
  return (
    <nav className="navbar">
      <div className="nav-brand">
        <h2>VPN Bot Admin</h2>
        <span className="version">v1.0.0</span>
      </div>
      <ul className="nav-links">
        <li><Link to="/" className={isActive('/')}>Dashboard</Link></li>
        <li><Link to="/panels" className={isActive('/panels')}>VPN Panels</Link></li>
        <li><Link to="/plans" className={isActive('/plans')}>Premium Plans</Link></li>
        <li><Link to="/users" className={isActive('/users')}>Users</Link></li>
        <li><Link to="/settings" className={isActive('/settings')}>Settings</Link></li>
      </ul>
      <div className="nav-status">
        <span className="status-indicator online"></span>
        <span>Online</span>
      </div>
    </nav>
  );
};

// Error Boundary Component
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('App Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-boundary">
          <h2>Something went wrong</h2>
          <p>The application encountered an unexpected error.</p>
          <button 
            onClick={() => this.setState({ hasError: false, error: undefined })}
            className="btn btn-primary"
          >
            Try Again
          </button>
          <button 
            onClick={() => window.location.reload()}
            className="btn"
            style={{ marginLeft: '1rem' }}
          >
            Reload Page
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

function App(): JSX.Element {
  const [connectionStatus, setConnectionStatus] = useState<'online' | 'offline' | 'checking'>('checking');

  useEffect(() => {
    // Check initial connection
    const checkConnection = async () => {
      try {
        const response = await fetch(process.env.REACT_APP_API_BASE_URL || '', {
          method: 'HEAD',
          mode: 'no-cors',
        });
        setConnectionStatus('online');
      } catch (error) {
        console.warn('Backend connection check failed:', error);
        setConnectionStatus('offline');
      }
    };

    checkConnection();

    // Set up periodic connection checks
    const interval = setInterval(checkConnection, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, []);

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <Router>
          <div className="App">
            {connectionStatus === 'offline' && (
              <div className="connection-warning">
                ⚠️ Cannot connect to backend server. Please check if the server is running.
              </div>
            )}
            
            <Navigation />

            <main className="main-content">
              <Routes>
                <Route path="/" element={<Dashboard />} />
                <Route path="/panels" element={<Panels />} />
                <Route path="/plans" element={<Plans />} />
                <Route path="/users" element={<Users />} />
                <Route path="/settings" element={<Settings />} />
              </Routes>
            </main>
          </div>
        </Router>
        {process.env.NODE_ENV === 'development' && (
          <ReactQueryDevtools initialIsOpen={false} />
        )}
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;
