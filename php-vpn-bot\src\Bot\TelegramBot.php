<?php

declare(strict_types=1);

namespace VpnBot\Bot;

use <PERSON><PERSON>\TelegramBot\Telegram;
use <PERSON><PERSON>\TelegramBot\Request;
use <PERSON><PERSON>\TelegramBot\Exception\TelegramException;
use VpnBot\Config\Config;
use VpnBot\Database\Connection;
use VpnBot\Bot\Handlers\CommandHandler;
use VpnBot\Bot\Handlers\CallbackQueryHandler;
use VpnBot\Bot\Middleware\AuthMiddleware;
use VpnBot\Bot\Middleware\LoggingMiddleware;
use VpnBot\Bot\Middleware\RateLimitMiddleware;
use Psr\Log\LoggerInterface;

class TelegramBot
{
    private Telegram $telegram;
    private Config $config;
    private LoggerInterface $logger;
    private CommandHandler $commandHandler;
    private CallbackQueryHandler $callbackHandler;

    public function __construct(Config $config, LoggerInterface $logger)
    {
        $this->config = $config;
        $this->logger = $logger;
        
        $this->initializeTelegram();
        $this->initializeHandlers();
        $this->setupMiddleware();
    }

    private function initializeTelegram(): void
    {
        try {
            $this->telegram = new Telegram(
                $this->config->get('bot.token'),
                $this->config->get('bot.username')
            );

            // Set webhook URL
            $webhookUrl = $this->config->get('bot.webhook_url') . '/webhook';
            $this->telegram->setWebhook($webhookUrl);

            $this->logger->info('Telegram bot initialized successfully');
        } catch (TelegramException $e) {
            $this->logger->error('Failed to initialize Telegram bot: ' . $e->getMessage());
            throw new \RuntimeException('Failed to initialize Telegram bot: ' . $e->getMessage());
        }
    }

    private function initializeHandlers(): void
    {
        $this->commandHandler = new CommandHandler($this->config, $this->logger);
        $this->callbackHandler = new CallbackQueryHandler($this->config, $this->logger);
    }

    private function setupMiddleware(): void
    {
        // Add middleware for authentication, logging, rate limiting, etc.
        // This will be implemented in the middleware classes
    }

    public function handleWebhook(string $input): void
    {
        try {
            $this->logger->debug('Received webhook data', ['input' => $input]);
            
            // Process the update
            $this->telegram->handle();
            
            $this->logger->info('Webhook processed successfully');
        } catch (TelegramException $e) {
            $this->logger->error('Error processing webhook: ' . $e->getMessage());
            throw new \RuntimeException('Error processing webhook: ' . $e->getMessage());
        }
    }

    public function setWebhook(): array
    {
        try {
            $webhookUrl = $this->config->get('bot.webhook_url') . '/webhook';
            $result = Request::setWebhook(['url' => $webhookUrl]);
            
            if ($result->isOk()) {
                $this->logger->info('Webhook set successfully', ['url' => $webhookUrl]);
                return ['success' => true, 'message' => 'Webhook set successfully'];
            } else {
                $this->logger->error('Failed to set webhook', ['result' => $result->getDescription()]);
                return ['success' => false, 'message' => $result->getDescription()];
            }
        } catch (TelegramException $e) {
            $this->logger->error('Error setting webhook: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    public function deleteWebhook(): array
    {
        try {
            $result = Request::deleteWebhook();
            
            if ($result->isOk()) {
                $this->logger->info('Webhook deleted successfully');
                return ['success' => true, 'message' => 'Webhook deleted successfully'];
            } else {
                $this->logger->error('Failed to delete webhook', ['result' => $result->getDescription()]);
                return ['success' => false, 'message' => $result->getDescription()];
            }
        } catch (TelegramException $e) {
            $this->logger->error('Error deleting webhook: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    public function getWebhookInfo(): array
    {
        try {
            $result = Request::getWebhookInfo();
            
            if ($result->isOk()) {
                return [
                    'success' => true,
                    'data' => $result->getResult()
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $result->getDescription()
                ];
            }
        } catch (TelegramException $e) {
            $this->logger->error('Error getting webhook info: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
}
