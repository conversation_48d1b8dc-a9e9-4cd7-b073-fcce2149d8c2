"""Authentication service for user management."""

import logging
from typing import Optional, Dict, Any
from datetime import datetime
from bot.database import get_db_connection
from bot.models import User

logger = logging.getLogger(__name__)


class AuthService:
    """Service for handling user authentication and management."""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def get_or_create_user(
        self, 
        telegram_id: int, 
        username: Optional[str] = None,
        first_name: Optional[str] = None,
        last_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get existing user or create new one."""
        try:
            async with get_db_connection() as conn:
                # Try to get existing user
                user = await conn.fetchrow(
                    "SELECT * FROM users WHERE telegram_id = $1", telegram_id
                )
                
                if user:
                    # Update user info if provided and different
                    updated = False
                    update_fields = []
                    update_values = [telegram_id]
                    
                    if username and user['username'] != username:
                        update_fields.append("username = $" + str(len(update_values) + 1))
                        update_values.append(username)
                        updated = True
                    
                    if first_name and user['first_name'] != first_name:
                        update_fields.append("first_name = $" + str(len(update_values) + 1))
                        update_values.append(first_name)
                        updated = True
                    
                    if last_name and user['last_name'] != last_name:
                        update_fields.append("last_name = $" + str(len(update_values) + 1))
                        update_values.append(last_name)
                        updated = True
                    
                    if updated:
                        update_fields.append("updated_at = NOW()")
                        query = f"UPDATE users SET {', '.join(update_fields)} WHERE telegram_id = $1 RETURNING *"
                        user = await conn.fetchrow(query, *update_values)
                        self.logger.info(f"Updated user info for {telegram_id}")
                    
                    return dict(user)
                
                else:
                    # Create new user
                    user = await conn.fetchrow(
                        """
                        INSERT INTO users (telegram_id, username, first_name, last_name, created_at)
                        VALUES ($1, $2, $3, $4, NOW())
                        RETURNING *
                        """,
                        telegram_id, username, first_name, last_name
                    )
                    
                    self.logger.info(f"Created new user: {telegram_id} (@{username})")
                    return dict(user)
        
        except Exception as e:
            self.logger.error(f"Error in get_or_create_user for {telegram_id}: {e}")
            raise
    
    async def get_user_by_id(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user by database ID."""
        try:
            async with get_db_connection() as conn:
                user = await conn.fetchrow(
                    "SELECT * FROM users WHERE id = $1", user_id
                )
                return dict(user) if user else None
        
        except Exception as e:
            self.logger.error(f"Error getting user by ID {user_id}: {e}")
            return None
    
    async def get_user_by_telegram_id(self, telegram_id: int) -> Optional[Dict[str, Any]]:
        """Get user by Telegram ID."""
        try:
            async with get_db_connection() as conn:
                user = await conn.fetchrow(
                    "SELECT * FROM users WHERE telegram_id = $1", telegram_id
                )
                return dict(user) if user else None
        
        except Exception as e:
            self.logger.error(f"Error getting user by Telegram ID {telegram_id}: {e}")
            return None
    
    async def update_user_language(self, telegram_id: int, language_code: str) -> bool:
        """Update user's language preference."""
        try:
            async with get_db_connection() as conn:
                await conn.execute(
                    "UPDATE users SET language_code = $1, updated_at = NOW() WHERE telegram_id = $2",
                    language_code, telegram_id
                )
                self.logger.info(f"Updated language for user {telegram_id} to {language_code}")
                return True
        
        except Exception as e:
            self.logger.error(f"Error updating language for user {telegram_id}: {e}")
            return False
    
    async def update_user_status(self, telegram_id: int, is_active: bool) -> bool:
        """Update user's active status."""
        try:
            async with get_db_connection() as conn:
                await conn.execute(
                    "UPDATE users SET is_active = $1, updated_at = NOW() WHERE telegram_id = $2",
                    is_active, telegram_id
                )
                self.logger.info(f"Updated status for user {telegram_id} to {'active' if is_active else 'inactive'}")
                return True
        
        except Exception as e:
            self.logger.error(f"Error updating status for user {telegram_id}: {e}")
            return False
    
    async def get_user_stats(self, telegram_id: int) -> Dict[str, Any]:
        """Get user statistics."""
        try:
            async with get_db_connection() as conn:
                # Get user basic info
                user = await conn.fetchrow(
                    "SELECT * FROM users WHERE telegram_id = $1", telegram_id
                )
                
                if not user:
                    return {}
                
                # Get VPN accounts count
                vpn_count = await conn.fetchval(
                    "SELECT COUNT(*) FROM vpn_accounts WHERE user_id = $1", user['id']
                )
                
                # Get active subscriptions count
                active_subs = await conn.fetchval(
                    """
                    SELECT COUNT(*) FROM premium_subscriptions 
                    WHERE user_id = $1 AND expires_at > NOW()
                    """, user['id']
                )
                
                # Get trial usage
                trial_used = await conn.fetchval(
                    "SELECT has_used_trial FROM users WHERE id = $1", user['id']
                )
                
                return {
                    'user_id': user['id'],
                    'telegram_id': user['telegram_id'],
                    'username': user['username'],
                    'created_at': user['created_at'],
                    'vpn_accounts_count': vpn_count,
                    'active_subscriptions': active_subs,
                    'trial_used': trial_used,
                    'is_active': user['is_active']
                }
        
        except Exception as e:
            self.logger.error(f"Error getting user stats for {telegram_id}: {e}")
            return {}
    
    async def is_user_admin(self, telegram_id: int) -> bool:
        """Check if user is an admin."""
        try:
            async with get_db_connection() as conn:
                result = await conn.fetchval(
                    "SELECT is_admin FROM users WHERE telegram_id = $1", telegram_id
                )
                return result or False
        
        except Exception as e:
            self.logger.error(f"Error checking admin status for {telegram_id}: {e}")
            return False
    
    async def set_user_admin(self, telegram_id: int, is_admin: bool) -> bool:
        """Set user admin status."""
        try:
            async with get_db_connection() as conn:
                await conn.execute(
                    "UPDATE users SET is_admin = $1, updated_at = NOW() WHERE telegram_id = $2",
                    is_admin, telegram_id
                )
                self.logger.info(f"Set admin status for user {telegram_id} to {is_admin}")
                return True
        
        except Exception as e:
            self.logger.error(f"Error setting admin status for user {telegram_id}: {e}")
            return False


# Global instance
auth_service = AuthService()