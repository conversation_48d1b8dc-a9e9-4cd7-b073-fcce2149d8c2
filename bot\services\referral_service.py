"""Referral system service for the Telegram bot."""

import logging
import random
import string
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from bot.database import get_db_connection


class ReferralService:
    """Service for managing referral system."""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def generate_referral_code(self, user_id: int, custom_code: str = None) -> Optional[str]:
        """Generate a unique referral code for a user."""
        try:
            async with get_db_connection() as conn:
                # Check if user already has a referral code
                existing_code = await conn.fetchval(
                    "SELECT referral_code FROM users WHERE id = $1",
                    user_id
                )
                
                if existing_code:
                    return existing_code
                
                # Generate new code
                if custom_code:
                    code = custom_code.upper()
                    # Check if custom code is available
                    existing = await conn.fetchval(
                        "SELECT id FROM users WHERE referral_code = $1",
                        code
                    )
                    if existing:
                        return None  # Code already taken
                else:
                    # Generate random code
                    while True:
                        code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))
                        existing = await conn.fetchval(
                            "SELECT id FROM users WHERE referral_code = $1",
                            code
                        )
                        if not existing:
                            break
                
                # Update user with referral code
                await conn.execute(
                    "UPDATE users SET referral_code = $1, updated_at = NOW() WHERE id = $2",
                    code, user_id
                )
                
                # Create referral code record
                await conn.execute(
                    """
                    INSERT INTO referral_codes (user_id, code, created_at)
                    VALUES ($1, $2, NOW())
                    """,
                    user_id, code
                )
                
                return code
        
        except Exception as e:
            self.logger.error(f"Error generating referral code for user {user_id}: {e}")
            return None
    
    async def process_referral(self, referred_user_id: int, referral_code: str) -> Dict[str, Any]:
        """Process a referral when a new user joins with a referral code."""
        try:
            async with get_db_connection() as conn:
                # Find the referrer
                referrer = await conn.fetchrow(
                    "SELECT id, telegram_id, username, first_name FROM users WHERE referral_code = $1",
                    referral_code.upper()
                )
                
                if not referrer:
                    return {'success': False, 'error': 'Invalid referral code'}
                
                # Check if user is trying to refer themselves
                if referrer['id'] == referred_user_id:
                    return {'success': False, 'error': 'Cannot refer yourself'}
                
                # Check if referral already exists
                existing_referral = await conn.fetchval(
                    "SELECT id FROM referrals WHERE referrer_id = $1 AND referred_id = $2",
                    referrer['id'], referred_user_id
                )
                
                if existing_referral:
                    return {'success': False, 'error': 'Referral already exists'}
                
                # Create referral record
                referral_id = await conn.fetchval(
                    """
                    INSERT INTO referrals (referrer_id, referred_id, referral_code, status, created_at)
                    VALUES ($1, $2, $3, 'pending', NOW())
                    RETURNING id
                    """,
                    referrer['id'], referred_user_id, referral_code.upper()
                )
                
                # Update referred user
                await conn.execute(
                    "UPDATE users SET referred_by = $1, updated_at = NOW() WHERE id = $2",
                    referrer['id'], referred_user_id
                )
                
                # Update referrer's referral count
                await conn.execute(
                    "UPDATE users SET referral_count = referral_count + 1, updated_at = NOW() WHERE id = $1",
                    referrer['id']
                )
                
                # Update referral code usage
                await conn.execute(
                    "UPDATE referral_codes SET uses_count = uses_count + 1, updated_at = NOW() WHERE code = $1",
                    referral_code.upper()
                )
                
                return {
                    'success': True,
                    'referral_id': referral_id,
                    'referrer': dict(referrer),
                    'message': f'Successfully referred by {referrer["first_name"]} (@{referrer["username"]})'
                }
        
        except Exception as e:
            self.logger.error(f"Error processing referral for user {referred_user_id}: {e}")
            return {'success': False, 'error': 'Failed to process referral'}
    
    async def complete_referral(self, referral_id: int, reward_type: str = 'data', reward_amount: int = 1073741824) -> bool:
        """Complete a referral and give rewards (default: 1GB data)."""
        try:
            async with get_db_connection() as conn:
                # Get referral details
                referral = await conn.fetchrow(
                    "SELECT * FROM referrals WHERE id = $1 AND status = 'pending'",
                    referral_id
                )

                if not referral:
                    return False

                # Update referral status
                await conn.execute(
                    """
                    UPDATE referrals
                    SET status = 'completed', reward_type = $1, reward_amount = $2, updated_at = NOW()
                    WHERE id = $3
                    """,
                    reward_type, reward_amount, referral_id
                )

                # Create reward for referrer
                await conn.execute(
                    """
                    INSERT INTO referral_rewards
                    (user_id, referral_id, reward_type, reward_amount, description, created_at)
                    VALUES ($1, $2, $3, $4, $5, NOW())
                    """,
                    referral['referrer_id'], referral_id, reward_type, reward_amount,
                    f"Referral reward for bringing new user"
                )

                # Apply reward based on type
                if reward_type == 'data':
                    # Add data to user's VPN accounts or create bonus data record
                    await self._apply_data_reward(referral['referrer_id'], reward_amount)
                elif reward_type == 'trial_reset':
                    # Reset trial for referrer
                    await conn.execute(
                        "UPDATE users SET has_used_trial = FALSE, trial_count = 0, last_trial_at = NULL WHERE id = $1",
                        referral['referrer_id']
                    )
                elif reward_type == 'time':
                    # Add time to active VPN accounts
                    await self._apply_time_reward(referral['referrer_id'], reward_amount)
                elif reward_type == 'premium':
                    # Give premium access or discount
                    await self._apply_premium_reward(referral['referrer_id'], reward_amount)

                # Update referrer's total rewards
                await conn.execute(
                    "UPDATE users SET total_referral_rewards = total_referral_rewards + $1 WHERE id = $2",
                    reward_amount, referral['referrer_id']
                )

                # Check for milestone rewards
                await self._check_milestone_rewards(referral['referrer_id'])

                return True

        except Exception as e:
            self.logger.error(f"Error completing referral {referral_id}: {e}")
            return False
    
    async def _apply_data_reward(self, user_id: int, data_amount: int) -> bool:
        """Apply data reward to user's VPN accounts."""
        try:
            async with get_db_connection() as conn:
                # Find active VPN accounts
                active_accounts = await conn.fetch(
                    """
                    SELECT id, data_limit FROM vpn_accounts 
                    WHERE user_id = $1 AND is_active = TRUE AND expire_date > NOW()
                    ORDER BY expire_date DESC
                    LIMIT 1
                    """,
                    user_id
                )
                
                if active_accounts:
                    # Add data to the most recent active account
                    account = active_accounts[0]
                    await conn.execute(
                        "UPDATE vpn_accounts SET data_limit = data_limit + $1 WHERE id = $2",
                        data_amount, account['id']
                    )
                else:
                    # Store as pending reward for when user gets VPN account
                    await conn.execute(
                        """
                        INSERT INTO referral_rewards 
                        (user_id, referral_id, reward_type, reward_amount, description, is_claimed, created_at)
                        VALUES ($1, 0, 'data', $2, 'Pending data reward from referral', FALSE, NOW())
                        """,
                        user_id, data_amount
                    )
                
                return True
        
        except Exception as e:
            self.logger.error(f"Error applying data reward to user {user_id}: {e}")
            return False
    
    async def _apply_time_reward(self, user_id: int, days: int) -> bool:
        """Apply time reward to user's VPN accounts."""
        try:
            async with get_db_connection() as conn:
                # Add time to all active accounts
                await conn.execute(
                    """
                    UPDATE vpn_accounts 
                    SET expire_date = expire_date + INTERVAL %s DAY
                    WHERE user_id = %s AND is_active = TRUE AND expire_date > NOW()
                    """,
                    days, user_id
                )
                
                return True
        
        except Exception as e:
            self.logger.error(f"Error applying time reward to user {user_id}: {e}")
            return False
    
    async def get_user_referral_stats(self, user_id: int) -> Dict[str, Any]:
        """Get referral statistics for a user."""
        try:
            async with get_db_connection() as conn:
                # Get user's referral code
                user_data = await conn.fetchrow(
                    "SELECT referral_code, referral_count, total_referral_rewards FROM users WHERE id = $1",
                    user_id
                )
                
                if not user_data:
                    return {}
                
                # Get referral details
                referrals = await conn.fetch(
                    """
                    SELECT r.*, u.first_name, u.username 
                    FROM referrals r
                    JOIN users u ON r.referred_id = u.id
                    WHERE r.referrer_id = $1
                    ORDER BY r.created_at DESC
                    """,
                    user_id
                )
                
                # Get pending rewards
                pending_rewards = await conn.fetch(
                    "SELECT * FROM referral_rewards WHERE user_id = $1 AND is_claimed = FALSE",
                    user_id
                )
                
                return {
                    'referral_code': user_data['referral_code'],
                    'total_referrals': user_data['referral_count'],
                    'total_rewards': user_data['total_referral_rewards'],
                    'referrals': [dict(r) for r in referrals],
                    'pending_rewards': [dict(r) for r in pending_rewards]
                }
        
        except Exception as e:
            self.logger.error(f"Error getting referral stats for user {user_id}: {e}")
            return {}

    async def _apply_premium_reward(self, user_id: int, reward_value: int) -> bool:
        """Apply premium reward to user."""
        try:
            async with get_db_connection() as conn:
                # Give premium status for specified days
                days = reward_value if reward_value < 1000 else 30  # Default 30 days if large number

                # Check if user has active premium subscription
                existing_sub = await conn.fetchrow(
                    """
                    SELECT * FROM premium_subscriptions
                    WHERE user_id = $1 AND is_active = TRUE AND expires_at > NOW()
                    ORDER BY expires_at DESC LIMIT 1
                    """,
                    user_id
                )

                if existing_sub:
                    # Extend existing subscription
                    await conn.execute(
                        "UPDATE premium_subscriptions SET expires_at = expires_at + INTERVAL %s DAY WHERE id = %s",
                        days, existing_sub['id']
                    )
                else:
                    # Create new premium subscription
                    await conn.execute(
                        """
                        INSERT INTO premium_subscriptions
                        (user_id, plan_id, is_active, expires_at, created_at)
                        VALUES ($1, NULL, TRUE, NOW() + INTERVAL %s DAY, NOW())
                        """,
                        user_id, days
                    )

                # Update user premium status
                await conn.execute(
                    "UPDATE users SET is_premium = TRUE WHERE id = $1",
                    user_id
                )

                return True

        except Exception as e:
            self.logger.error(f"Error applying premium reward to user {user_id}: {e}")
            return False

    async def _check_milestone_rewards(self, user_id: int) -> None:
        """Check and apply milestone rewards based on referral count."""
        try:
            async with get_db_connection() as conn:
                # Get user's current referral count
                user_data = await conn.fetchrow(
                    "SELECT referral_count, total_referral_rewards FROM users WHERE id = $1",
                    user_id
                )

                if not user_data:
                    return

                referral_count = user_data['referral_count']

                # Define milestone rewards
                milestones = {
                    5: {'type': 'trial_reset', 'amount': 1, 'description': '5 referrals milestone: Trial reset'},
                    10: {'type': 'data', 'amount': 5368709120, 'description': '10 referrals milestone: 5GB bonus data'},  # 5GB
                    25: {'type': 'premium', 'amount': 30, 'description': '25 referrals milestone: 30 days premium'},
                    50: {'type': 'data', 'amount': 10737418240, 'description': '50 referrals milestone: 10GB bonus data'},  # 10GB
                    100: {'type': 'premium', 'amount': 90, 'description': '100 referrals milestone: 90 days premium'}
                }

                # Check which milestones user has reached
                for milestone, reward in milestones.items():
                    if referral_count >= milestone:
                        # Check if milestone reward already given
                        existing_reward = await conn.fetchval(
                            """
                            SELECT id FROM referral_rewards
                            WHERE user_id = $1 AND description = $2
                            """,
                            user_id, reward['description']
                        )

                        if not existing_reward:
                            # Give milestone reward
                            await conn.execute(
                                """
                                INSERT INTO referral_rewards
                                (user_id, referral_id, reward_type, reward_amount, description, created_at)
                                VALUES ($1, 0, $2, $3, $4, NOW())
                                """,
                                user_id, reward['type'], reward['amount'], reward['description']
                            )

                            # Apply the reward
                            if reward['type'] == 'data':
                                await self._apply_data_reward(user_id, reward['amount'])
                            elif reward['type'] == 'trial_reset':
                                await conn.execute(
                                    "UPDATE users SET has_used_trial = FALSE, trial_count = 0, last_trial_at = NULL WHERE id = $1",
                                    user_id
                                )
                            elif reward['type'] == 'premium':
                                await self._apply_premium_reward(user_id, reward['amount'])

                            self.logger.info(f"Applied milestone reward for user {user_id}: {reward['description']}")

        except Exception as e:
            self.logger.error(f"Error checking milestone rewards for user {user_id}: {e}")

    async def get_available_rewards(self) -> Dict[str, Any]:
        """Get available reward types and amounts."""
        return {
            'data_rewards': [
                {'amount': 1073741824, 'display': '1 GB'},  # 1GB
                {'amount': 2147483648, 'display': '2 GB'},  # 2GB
                {'amount': 5368709120, 'display': '5 GB'},  # 5GB
                {'amount': 10737418240, 'display': '10 GB'}  # 10GB
            ],
            'time_rewards': [
                {'amount': 7, 'display': '7 days'},
                {'amount': 15, 'display': '15 days'},
                {'amount': 30, 'display': '30 days'},
                {'amount': 60, 'display': '60 days'}
            ],
            'premium_rewards': [
                {'amount': 7, 'display': '7 days premium'},
                {'amount': 30, 'display': '30 days premium'},
                {'amount': 60, 'display': '60 days premium'},
                {'amount': 90, 'display': '90 days premium'}
            ],
            'special_rewards': [
                {'type': 'trial_reset', 'display': 'Trial Reset'}
            ]
        }


# Global instance
referral_service = ReferralService()
