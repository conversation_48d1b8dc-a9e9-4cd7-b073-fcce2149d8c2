#!/usr/bin/env pwsh
# VPN Bot Startup Script
# This script simplifies the Docker management for the VPN bot project

param(
    [Parameter(Position=0)]
    [ValidateSet('up', 'down', 'restart', 'logs', 'status', 'build')]
    [string]$Action = 'up',
    
    [switch]$Build,
    [switch]$Detach = $true
)

Write-Host "VPN Bot Docker Manager" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan

switch ($Action) {
    'up' {
        Write-Host "Starting VPN Bot services..." -ForegroundColor Green
        if ($Build) {
            docker-compose up --build $(if ($Detach) { '-d' })
        } else {
            docker-compose up $(if ($Detach) { '-d' })
        }
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "\nServices started successfully!" -ForegroundColor Green
            Write-Host "Admin Panel: http://localhost:3000" -ForegroundColor Yellow
            Write-Host "Admin API: http://localhost:8000" -ForegroundColor Yellow
            Write-Host "\nUse 'docker-compose logs -f' to view logs" -ForegroundColor Cyan
        }
    }
    
    'down' {
        Write-Host "Stopping VPN Bot services..." -ForegroundColor Yellow
        docker-compose down
        Write-Host "Services stopped." -ForegroundColor Green
    }
    
    'restart' {
        Write-Host "Restarting VPN Bot services..." -ForegroundColor Yellow
        docker-compose down
        docker-compose up $(if ($Detach) { '-d' })
        Write-Host "Services restarted." -ForegroundColor Green
    }
    
    'logs' {
        Write-Host "Showing service logs..." -ForegroundColor Cyan
        docker-compose logs -f
    }
    
    'status' {
        Write-Host "Service Status:" -ForegroundColor Cyan
        docker-compose ps
        Write-Host "\nContainer Health:" -ForegroundColor Cyan
        docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    }
    
    'build' {
        Write-Host "Building VPN Bot services..." -ForegroundColor Blue
        docker-compose build --no-cache
        Write-Host "Build completed." -ForegroundColor Green
    }
}

Write-Host "\nAvailable commands:" -ForegroundColor Cyan
Write-Host "  .\start.ps1 up [-Build]     - Start services" -ForegroundColor White
Write-Host "  .\start.ps1 down           - Stop services" -ForegroundColor White
Write-Host "  .\start.ps1 restart        - Restart services" -ForegroundColor White
Write-Host "  .\start.ps1 logs           - View logs" -ForegroundColor White
Write-Host "  .\start.ps1 status         - Check status" -ForegroundColor White
Write-Host "  .\start.ps1 build          - Rebuild images" -ForegroundColor White