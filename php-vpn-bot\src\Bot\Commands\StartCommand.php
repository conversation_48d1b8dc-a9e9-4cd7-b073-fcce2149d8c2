<?php

declare(strict_types=1);

namespace VpnBot\Bot\Commands;

use <PERSON><PERSON>\TelegramBot\Commands\SystemCommand;
use <PERSON><PERSON>\TelegramBot\Entities\ServerResponse;
use <PERSON><PERSON>\TelegramBot\Request;
use VpnBot\Models\User;
use VpnBot\Utils\Localization;
use VpnBot\Bot\Keyboards\ReplyKeyboardBuilder;
use VpnBot\Services\ChannelService;
use VpnBot\Services\ReferralService;

class StartCommand extends SystemCommand
{
    protected $name = 'start';
    protected $description = 'Start command';
    protected $usage = '/start';
    protected $version = '1.0.0';

    public function execute(): ServerResponse
    {
        $message = $this->getMessage();
        $user = $message->getFrom();
        $chat = $message->getChat();
        $text = $message->getText(true);

        // Initialize services
        $localization = Localization::getInstance();
        $keyboardBuilder = new ReplyKeyboardBuilder();
        $channelService = new ChannelService();
        $referralService = new ReferralService();

        // Get or create user
        $dbUser = User::findByTelegramId($user->getId());
        if (!$dbUser) {
            $dbUser = new User();
            $dbUser->telegram_id = $user->getId();
            $dbUser->username = $user->getUsername();
            $dbUser->first_name = $user->getFirstName();
            $dbUser->last_name = $user->getLastName();
            $dbUser->language_code = $user->getLanguageCode() ?? 'en';
            $dbUser->save();
        } else {
            // Update user info
            $dbUser->username = $user->getUsername();
            $dbUser->first_name = $user->getFirstName();
            $dbUser->last_name = $user->getLastName();
            $dbUser->updateLastSeen();
            $dbUser->save();
        }

        $language = $dbUser->language_code;

        // Handle referral code
        if (!empty($text) && strpos($text, 'ref_') === 0) {
            $referralCode = substr($text, 4);
            $referralService->processReferral($dbUser->id, $referralCode);
        }

        // Check channel subscriptions
        $subscriptionStatus = $channelService->checkUserSubscriptions($user->getId());
        
        if (!$subscriptionStatus['is_subscribed']) {
            $channels = $subscriptionStatus['missing_channels'];
            $channelData = [];
            
            foreach ($channels as $channel) {
                $channelData[] = [
                    'name' => $channel->channel_name,
                    'url' => $channel->getUrl(),
                ];
            }

            $message = $localization->get('trial.subscription_required', $language) . "\n\n";
            $message .= $localization->get('trial.subscription_progress', $language, [
                'subscribed' => $subscriptionStatus['subscribed_count'],
                'total' => $subscriptionStatus['total_required']
            ]) . "\n\n";
            
            $message .= "📢 Required channels:\n";
            foreach ($channelData as $i => $channel) {
                $message .= ($i + 1) . ". {$channel['name']}\n";
            }

            return Request::sendMessage([
                'chat_id' => $chat->getId(),
                'text' => $message,
                'reply_markup' => $keyboardBuilder->createChannelSubscriptionMenu($channelData, $language),
                'parse_mode' => 'Markdown',
                'disable_web_page_preview' => true,
            ]);
        }

        // Send welcome message with main menu
        $welcomeText = $localization->get('welcome.title', $language, ['name' => $user->getFirstName()]);
        $welcomeText .= "\n\n" . $localization->get('welcome.description', $language);

        return Request::sendMessage([
            'chat_id' => $chat->getId(),
            'text' => $welcomeText,
            'reply_markup' => $keyboardBuilder->createMainMenu($language),
            'parse_mode' => 'Markdown',
        ]);
    }
}
