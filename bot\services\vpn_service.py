"""VPN service for managing VPN accounts and Marzban API integration."""

import logging
import asyncio
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from bot.database import get_db_connection
from bot.marzban_api import MarzbanAPI
from bot.services.qr_service import qr_service
from bot.config import settings

logger = logging.getLogger(__name__)


class VPNService:
    """Service for managing VPN accounts and subscriptions."""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.marzban_api = MarzbanAPI(
            base_url=settings.MARZBAN_URL,
            username=settings.MARZBAN_USERNAME,
            password=settings.MARZBAN_PASSWORD,
            timeout=settings.MARZBAN_API_TIMEOUT
        )
    
    async def create_trial_vpn(self, user_id: int, duration_days: int = 3) -> Optional[Dict[str, Any]]:
        """Create a trial VPN account for user."""
        try:
            async with get_db_connection() as conn:
                # Check if user already used trial
                user = await conn.fetchrow(
                    "SELECT has_used_trial FROM users WHERE id = $1", user_id
                )
                
                if not user:
                    raise ValueError("User not found")
                
                if user['has_used_trial']:
                    raise ValueError("Trial already used")
                
                # Generate unique username
                username = f"trial_{user_id}_{int(datetime.now().timestamp())}"
                
                # Create account in Marzban
                user_data = {
                    'username': username,
                    'data_limit': 1024 * 1024 * 1024,  # 1GB
                    'expire': int((datetime.now() + timedelta(days=duration_days)).timestamp())
                }
                marzban_user = await self.marzban_api.create_user(user_data)
                
                if not marzban_user:
                    raise Exception("Failed to create user in Marzban")
                
                # Save to database
                vpn_account = await conn.fetchrow(
                    """
                    INSERT INTO vpn_accounts 
                    (user_id, username, subscription_url, data_limit, expires_at, is_trial, created_at)
                    VALUES ($1, $2, $3, $4, $5, true, NOW())
                    RETURNING *
                    """,
                    user_id, username, marzban_user.get('subscription_url'),
                    1024 * 1024 * 1024, datetime.now() + timedelta(days=duration_days)
                )
                
                # Mark trial as used
                await conn.execute(
                    "UPDATE users SET has_used_trial = true WHERE id = $1", user_id
                )
                
                self.logger.info(f"Created trial VPN for user {user_id}: {username}")
                
                return {
                    'id': vpn_account['id'],
                    'username': username,
                    'subscription_url': marzban_user.get('subscription_url'),
                    'data_limit': 1024 * 1024 * 1024,
                    'expires_at': vpn_account['expires_at'],
                    'is_trial': True
                }
        
        except Exception as e:
            self.logger.error(f"Error creating trial VPN for user {user_id}: {e}")
            raise
    
    async def create_premium_vpn(
        self, 
        user_id: int, 
        plan_id: int,
        duration_days: int,
        data_limit_gb: int
    ) -> Optional[Dict[str, Any]]:
        """Create a premium VPN account for user."""
        try:
            async with get_db_connection() as conn:
                # Get plan details
                plan = await conn.fetchrow(
                    "SELECT * FROM premium_plans WHERE id = $1", plan_id
                )
                
                if not plan:
                    raise ValueError("Plan not found")
                
                # Generate unique username
                username = f"premium_{user_id}_{plan_id}_{int(datetime.now().timestamp())}"
                
                # Create account in Marzban
                user_data = {
                    'username': username,
                    'data_limit': data_limit_gb * 1024 * 1024 * 1024,
                    'expire': int((datetime.now() + timedelta(days=duration_days)).timestamp())
                }
                marzban_user = await self.marzban_api.create_user(user_data)
                
                if not marzban_user:
                    raise Exception("Failed to create user in Marzban")
                
                # Save to database
                vpn_account = await conn.fetchrow(
                    """
                    INSERT INTO vpn_accounts 
                    (user_id, username, subscription_url, data_limit, expires_at, plan_id, is_trial, created_at)
                    VALUES ($1, $2, $3, $4, $5, $6, false, NOW())
                    RETURNING *
                    """,
                    user_id, username, marzban_user.get('subscription_url'),
                    data_limit_gb * 1024 * 1024 * 1024, 
                    datetime.now() + timedelta(days=duration_days),
                    plan_id
                )
                
                self.logger.info(f"Created premium VPN for user {user_id}: {username}")
                
                return {
                    'id': vpn_account['id'],
                    'username': username,
                    'subscription_url': marzban_user.get('subscription_url'),
                    'data_limit': data_limit_gb * 1024 * 1024 * 1024,
                    'expires_at': vpn_account['expires_at'],
                    'is_trial': False,
                    'plan_id': plan_id
                }
        
        except Exception as e:
            self.logger.error(f"Error creating premium VPN for user {user_id}: {e}")
            raise
    
    async def get_user_vpn_accounts(self, user_id: int) -> List[Dict[str, Any]]:
        """Get all VPN accounts for a user."""
        try:
            async with get_db_connection() as conn:
                accounts = await conn.fetch(
                    """
                    SELECT va.*, pp.name as plan_name, pp.price as plan_price
                    FROM vpn_accounts va
                    LEFT JOIN premium_plans pp ON va.plan_id = pp.id
                    WHERE va.user_id = $1
                    ORDER BY va.created_at DESC
                    """,
                    user_id
                )
                
                result = []
                for account in accounts:
                    account_dict = dict(account)
                    
                    # Get usage data from Marzban
                    usage = await self.get_account_usage(account['username'])
                    account_dict.update(usage)
                    
                    result.append(account_dict)
                
                return result
        
        except Exception as e:
            self.logger.error(f"Error getting VPN accounts for user {user_id}: {e}")
            return []
    
    async def get_account_usage(self, username: str) -> Dict[str, Any]:
        """Get account usage from Marzban API."""
        try:
            usage_data = await self.marzban_api.get_user_usage(username)
            
            if usage_data:
                return {
                    'used_traffic': usage_data.get('used_traffic', 0),
                    'remaining_traffic': usage_data.get('data_limit', 0) - usage_data.get('used_traffic', 0),
                    'is_active': usage_data.get('status') == 'active',
                    'last_online': usage_data.get('online_at')
                }
            
            return {
                'used_traffic': 0,
                'remaining_traffic': 0,
                'is_active': False,
                'last_online': None
            }
        
        except Exception as e:
            self.logger.error(f"Error getting usage for {username}: {e}")
            return {
                'used_traffic': 0,
                'remaining_traffic': 0,
                'is_active': False,
                'last_online': None
            }
    
    async def reset_account_usage(self, username: str) -> bool:
        """Reset account usage in Marzban."""
        try:
            success = await self.marzban_api.reset_user_usage(username)
            if success:
                self.logger.info(f"Reset usage for account {username}")
            return success
        
        except Exception as e:
            self.logger.error(f"Error resetting usage for {username}: {e}")
            return False
    
    async def extend_account(self, username: str, additional_days: int) -> bool:
        """Extend account expiration date."""
        try:
            # Get current user data
            user_data = await self.marzban_api.get_user(username)
            if not user_data:
                return False
            
            # Calculate new expiration date
            current_expire = datetime.fromisoformat(user_data.get('expire', datetime.now().isoformat()))
            new_expire = current_expire + timedelta(days=additional_days)
            
            # Update in Marzban
            update_data = {
                'expire': int(new_expire.timestamp())
            }
            success = await self.marzban_api.update_user(username, update_data)
            
            if success:
                # Update in database
                async with get_db_connection() as conn:
                    await conn.execute(
                        "UPDATE vpn_accounts SET expires_at = $1 WHERE username = $2",
                        new_expire, username
                    )
                
                self.logger.info(f"Extended account {username} by {additional_days} days")
            
            return success
        
        except Exception as e:
            self.logger.error(f"Error extending account {username}: {e}")
            return False
    
    async def delete_account(self, username: str) -> bool:
        """Delete VPN account."""
        try:
            # Delete from Marzban
            success = await self.marzban_api.delete_user(username)
            
            if success:
                # Update database (soft delete)
                async with get_db_connection() as conn:
                    await conn.execute(
                        "UPDATE vpn_accounts SET is_active = false, deleted_at = NOW() WHERE username = $1",
                        username
                    )
                
                self.logger.info(f"Deleted account {username}")
            
            return success
        
        except Exception as e:
            self.logger.error(f"Error deleting account {username}: {e}")
            return False
    
    async def generate_qr_code(self, subscription_url: str) -> Optional[str]:
        """Generate QR code for subscription URL."""
        try:
            return await qr_service.generate_qr_code(subscription_url)
        except Exception as e:
            self.logger.error(f"Error generating QR code: {e}")
            return None
    
    async def get_account_config(self, username: str) -> Optional[str]:
        """Get account configuration from Marzban."""
        try:
            config = await self.marzban_api.get_user_config(username)
            return config
        except Exception as e:
            self.logger.error(f"Error getting config for {username}: {e}")
            return None
    
    async def user_has_trial_account(self, user_id: int) -> bool:
        """Check if user has already used trial account."""
        try:
            async with get_db_connection() as conn:
                user = await conn.fetchrow(
                    "SELECT has_used_trial FROM users WHERE telegram_id = $1", user_id
                )
                
                if not user:
                    return False
                
                return user['has_used_trial'] or False
        
        except Exception as e:
            self.logger.error(f"Error checking trial account for user {user_id}: {e}")
            return False
    
    async def get_user_accounts(self, user_id: int) -> List[Dict[str, Any]]:
        """Get all VPN accounts for a user (alias for get_user_vpn_accounts)."""
        return await self.get_user_vpn_accounts(user_id)
    
    async def get_account_details(self, user_id: int, account_id: int) -> Optional[Dict[str, Any]]:
        """Get detailed information about a specific account."""
        try:
            async with get_db_connection() as conn:
                account = await conn.fetchrow(
                    """
                    SELECT va.*, pp.name as plan_name, pp.price as plan_price,
                           s.name as server_name
                    FROM vpn_accounts va
                    LEFT JOIN premium_plans pp ON va.plan_id = pp.id
                    LEFT JOIN servers s ON va.server_id = s.id
                    WHERE va.id = $1 AND va.user_id = $2
                    """,
                    account_id, user_id
                )
                
                if not account:
                    return None
                
                account_dict = dict(account)
                
                # Get usage data from Marzban
                usage = await self.get_account_usage(account['username'])
                account_dict.update(usage)
                
                # Format data for display
                account_dict['used_traffic_gb'] = round((account_dict.get('used_traffic', 0)) / (1024**3), 2)
                account_dict['data_limit_gb'] = round((account_dict.get('data_limit', 0)) / (1024**3), 2)
                account_dict['status'] = 'active' if account_dict.get('is_active') else 'inactive'
                account_dict['expire_date'] = account_dict.get('expires_at', '').split('T')[0] if account_dict.get('expires_at') else 'N/A'
                account_dict['server_name'] = account_dict.get('server_name', 'Default')
                
                return account_dict
        
        except Exception as e:
            self.logger.error(f"Error getting account details for user {user_id}, account {account_id}: {e}")
            return None
    
    async def reset_account_usage(self, user_id: int, account_id: int) -> bool:
        """Reset account usage statistics."""
        try:
            async with get_db_connection() as conn:
                # Get account from database
                account = await conn.fetchrow(
                    "SELECT username FROM vpn_accounts WHERE id = $1 AND user_id = $2",
                    account_id, user_id
                )
                
                if not account:
                    self.logger.error(f"Account {account_id} not found for user {user_id}")
                    return False
                
                # Reset usage via Marzban API
                success = await self.marzban_api.reset_user_usage(account['username'])
                
                if success:
                    self.logger.info(f"Successfully reset usage for account {account['username']}")
                    return True
                else:
                    self.logger.error(f"Failed to reset usage for account {account['username']} via Marzban API")
                    return False
                
        except Exception as e:
            self.logger.error(f"Error resetting account usage: {e}")
            return False
    
    async def check_trial_eligibility(self, user_id: int, bot=None) -> Dict[str, Any]:
        """Check if user is eligible for trial."""
        try:
            async with get_db_connection() as conn:
                user = await conn.fetchrow(
                    "SELECT has_used_trial, created_at FROM users WHERE telegram_id = $1", user_id
                )

                if not user:
                    return {'eligible': False, 'reason': 'User not found'}

                if user['has_used_trial']:
                    return {'eligible': False, 'reason': 'Trial already used'}

                # Check if user has any active VPN accounts
                active_accounts = await conn.fetchval(
                    """
                    SELECT COUNT(*) FROM vpn_accounts
                    WHERE user_id = $1 AND expires_at > NOW() AND is_active = true
                    """,
                    user_id
                )

                if active_accounts > 0:
                    return {'eligible': False, 'reason': 'User has active VPN accounts'}

                # Check channel subscriptions if bot is provided
                if bot:
                    from bot.services.channel_service import ChannelService
                    channel_service = ChannelService()

                    verification_result = await channel_service.verify_all_subscriptions(bot, user_id)

                    if not verification_result.get('is_subscribed', False):
                        missing_channels = verification_result.get('missing_channels', [])
                        return {
                            'eligible': False,
                            'reason': 'Channel subscription required',
                            'missing_channels': missing_channels,
                            'subscribed_count': verification_result.get('subscribed_count', 0),
                            'total_required': verification_result.get('total_required', 0)
                        }

                return {'eligible': True, 'reason': 'User is eligible for trial'}

        except Exception as e:
            self.logger.error(f"Error checking trial eligibility for user {user_id}: {e}")
            return {'eligible': False, 'reason': 'Error checking eligibility'}


# Global instance
vpn_service = VPNService()