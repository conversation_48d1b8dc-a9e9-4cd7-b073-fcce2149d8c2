-- Migration: Add ton_payments table for TON blockchain payments
-- Date: 2025-01-26
-- Description: Creates table to track TON cryptocurrency payments

CREATE TABLE IF NOT EXISTS ton_payments (
    id SERIAL PRIMARY KEY,
    payment_id VARCHAR(255) UNIQUE NOT NULL,
    user_id BIGINT NOT NULL,
    plan_id INTEGER NOT NULL,
    wallet_address VARCHAR(255) NOT NULL,
    ton_amount DECIMAL(20, 8) NOT NULL,
    usd_amount DECIMAL(10, 2) NOT NULL,
    memo VARCHAR(255) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'waiting',
    transaction_hash VARCHAR(255),
    expires_at TIMESTAMP NOT NULL,
    confirmed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_ton_payments_user_id 
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_ton_payments_plan_id 
        FOREIGN KEY (plan_id) REFERENCES premium_plans(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_ton_payments_user_id ON ton_payments(user_id);
CREATE INDEX IF NOT EXISTS idx_ton_payments_status ON ton_payments(status);
CREATE INDEX IF NOT EXISTS idx_ton_payments_payment_id ON ton_payments(payment_id);
CREATE INDEX IF NOT EXISTS idx_ton_payments_wallet_address ON ton_payments(wallet_address);
CREATE INDEX IF NOT EXISTS idx_ton_payments_created_at ON ton_payments(created_at);
CREATE INDEX IF NOT EXISTS idx_ton_payments_expires_at ON ton_payments(expires_at);

-- Add ton_payment_id column to premium_subscriptions table
ALTER TABLE premium_subscriptions 
ADD COLUMN IF NOT EXISTS ton_payment_id INTEGER REFERENCES ton_payments(id);

-- Add index for ton_payment_id
CREATE INDEX IF NOT EXISTS idx_premium_subscriptions_ton_payment_id ON premium_subscriptions(ton_payment_id);

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_ton_payments_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER IF NOT EXISTS trigger_ton_payments_updated_at
    BEFORE UPDATE ON ton_payments
    FOR EACH ROW
    EXECUTE FUNCTION update_ton_payments_updated_at();

-- Add comments for documentation
COMMENT ON TABLE ton_payments IS 'Tracks TON cryptocurrency payments';
COMMENT ON COLUMN ton_payments.payment_id IS 'Unique payment identifier';
COMMENT ON COLUMN ton_payments.wallet_address IS 'TON wallet address for payment';
COMMENT ON COLUMN ton_payments.ton_amount IS 'Amount in TON cryptocurrency';
COMMENT ON COLUMN ton_payments.usd_amount IS 'Equivalent amount in USD';
COMMENT ON COLUMN ton_payments.memo IS 'Payment memo for transaction identification';
COMMENT ON COLUMN ton_payments.status IS 'Payment status: waiting, confirmed, expired, failed';
COMMENT ON COLUMN ton_payments.transaction_hash IS 'TON blockchain transaction hash';
COMMENT ON COLUMN ton_payments.expires_at IS 'Payment expiration timestamp';
COMMENT ON COLUMN ton_payments.confirmed_at IS 'Payment confirmation timestamp';