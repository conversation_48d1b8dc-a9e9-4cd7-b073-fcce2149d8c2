#!/usr/bin/env python3
"""
Verification script for PaymentHandler import fixes in main.py
Tests all sections of the fix to ensure proper functionality.
"""

import sys
import os
import logging

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


def test_section_1_main_import_fix():
    """Section 1: Test main import fix"""
    logger.info("🔍 Section 1: Testing Main Import Fix")
    
    try:
        # Test that PaymentHandler can be imported from payments module
        from bot.handlers.payments import PaymentHandler
        logger.info("✅ PaymentHandler import successful")
        
        # Test that the class can be instantiated
        handler = PaymentHandler()
        logger.info("✅ PaymentHandler instantiation successful")
        
        # Test that main.py can be imported without errors
        from bot.main import TelegramBot
        logger.info("✅ Main.py import successful")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        return False


def test_section_2_reference_updates():
    """Section 2: Test reference updates in main.py"""
    logger.info("🔍 Section 2: Testing Reference Updates")
    
    try:
        from bot.main import TelegramBot
        
        # Create bot instance to test handler initialization
        bot = TelegramBot()
        
        # Test that payment_handler attribute exists (not payment_handlers)
        if hasattr(bot, 'payment_handler'):
            logger.info("✅ payment_handler attribute exists")
        else:
            logger.error("❌ payment_handler attribute missing")
            return False
        
        # Test that old payment_handlers attribute doesn't exist
        if not hasattr(bot, 'payment_handlers'):
            logger.info("✅ Old payment_handlers attribute properly removed")
        else:
            logger.error("❌ Old payment_handlers attribute still exists")
            return False
        
        # Test that the payment_handler is the correct type
        if isinstance(bot.payment_handler, type(bot.payment_handler)):
            logger.info(f"✅ payment_handler is correct type: {type(bot.payment_handler).__name__}")
        else:
            logger.error("❌ payment_handler is incorrect type")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing references: {e}")
        return False


def test_section_3_verification_and_testing():
    """Section 3: Test verification and functionality"""
    logger.info("🔍 Section 3: Testing Verification and Functionality")
    
    try:
        from bot.handlers.payments import PaymentHandler
        
        # Test that all required methods exist
        handler = PaymentHandler()
        required_methods = [
            'handle_stars_payment',
            'create_card_payment',
            'create_crypto_payment',
            'create_ton_payment',
            'handle_pre_checkout_query',
            'handle_successful_payment'
        ]
        
        missing_methods = []
        for method in required_methods:
            if hasattr(handler, method):
                logger.info(f"✅ Method {method} exists")
            else:
                missing_methods.append(method)
                logger.error(f"❌ Method {method} missing")
        
        if missing_methods:
            logger.error(f"❌ Missing methods: {missing_methods}")
            return False
        
        logger.info("✅ All required PaymentHandler methods exist")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing functionality: {e}")
        return False


def test_section_4_codebase_cleanup():
    """Section 4: Test codebase-wide cleanup"""
    logger.info("🔍 Section 4: Testing Codebase-wide Cleanup")
    
    try:
        # Test that main.py doesn't contain any references to PaymentHandlers (plural)
        with open('bot/main.py', 'r') as f:
            main_content = f.read()
        
        if 'PaymentHandlers' in main_content:
            logger.error("❌ main.py still contains references to PaymentHandlers (plural)")
            return False
        else:
            logger.info("✅ main.py cleaned of PaymentHandlers references")
        
        # Test that main.py contains correct references to PaymentHandler (singular)
        if 'PaymentHandler' in main_content:
            logger.info("✅ main.py contains correct PaymentHandler references")
        else:
            logger.error("❌ main.py missing PaymentHandler references")
            return False
        
        # Test that the payment_handler instance is used correctly
        if 'self.payment_handler.' in main_content:
            logger.info("✅ payment_handler instance used correctly in main.py")
        else:
            logger.error("❌ payment_handler instance not used correctly")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing codebase cleanup: {e}")
        return False


def test_integration():
    """Integration test: Test that the bot can be initialized and handlers work together"""
    logger.info("🔍 Integration Test: Testing Bot Initialization")
    
    try:
        from bot.main import TelegramBot
        
        # Create bot instance
        bot = TelegramBot()
        
        # Test that all handlers are properly initialized
        handlers = [
            ('command_handlers', 'CommandHandlers'),
            ('payment_handler', 'PaymentHandler'),
            ('error_handlers', 'ErrorHandlers'),
            ('language_selection_handler', 'LanguageSelectionHandler')
        ]
        
        for attr_name, class_name in handlers:
            if hasattr(bot, attr_name):
                handler = getattr(bot, attr_name)
                if handler is not None:
                    logger.info(f"✅ {attr_name} initialized correctly ({type(handler).__name__})")
                else:
                    logger.error(f"❌ {attr_name} is None")
                    return False
            else:
                logger.error(f"❌ {attr_name} attribute missing")
                return False
        
        logger.info("✅ All handlers initialized correctly")
        return True
        
    except Exception as e:
        logger.error(f"❌ Integration test failed: {e}")
        return False


def main():
    """Run all verification tests"""
    logger.info("🚀 Starting PaymentHandler Fix Verification")
    logger.info("=" * 60)
    
    tests = [
        ("Section 1: Main Import Fix", test_section_1_main_import_fix),
        ("Section 2: Reference Updates", test_section_2_reference_updates),
        ("Section 3: Verification and Testing", test_section_3_verification_and_testing),
        ("Section 4: Codebase-wide Cleanup", test_section_4_codebase_cleanup),
        ("Integration Test", test_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running: {test_name}")
        result = test_func()
        results.append((test_name, result))
        
        if result:
            logger.info(f"✅ {test_name}: PASSED")
        else:
            logger.error(f"❌ {test_name}: FAILED")
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 VERIFICATION SUMMARY")
    logger.info("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"   {status} {test_name}")
    
    logger.info(f"\n📈 Results: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 ALL TESTS PASSED! PaymentHandler fix is complete and working correctly.")
        logger.info("🚀 The bot is ready for deployment with proper PaymentHandler integration.")
        return True
    else:
        logger.error(f"⚠️  {total - passed} tests failed. Please review the errors above.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
