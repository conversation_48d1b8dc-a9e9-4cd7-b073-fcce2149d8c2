<?php

declare(strict_types=1);

namespace VpnBot\Models;

use VpnBot\Database\Connection;
use PDO;

class VpnPanel
{
    public ?int $id = null;
    public string $name;
    public string $base_url;
    public string $api_username;
    public string $api_password;
    public ?string $api_token = null;
    public bool $is_active = true;
    public int $max_users = 1000;
    public int $current_users = 0;
    public string $panel_type = 'marzban';
    public ?array $settings = null;
    public ?string $created_at = null;
    public ?string $updated_at = null;

    public static function findAll(): array
    {
        $pdo = Connection::getInstance();
        $stmt = $pdo->query('SELECT * FROM vpn_panels ORDER BY created_at ASC');
        
        $panels = [];
        while ($data = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $panels[] = self::fromArray($data);
        }

        return $panels;
    }

    public static function findActive(): array
    {
        $pdo = Connection::getInstance();
        $stmt = $pdo->prepare('SELECT * FROM vpn_panels WHERE is_active = ? ORDER BY current_users ASC');
        $stmt->execute([true]);
        
        $panels = [];
        while ($data = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $panels[] = self::fromArray($data);
        }

        return $panels;
    }

    public static function findById(int $id): ?VpnPanel
    {
        $pdo = Connection::getInstance();
        $stmt = $pdo->prepare('SELECT * FROM vpn_panels WHERE id = ?');
        $stmt->execute([$id]);
        
        $data = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!$data) {
            return null;
        }

        return self::fromArray($data);
    }

    public function save(): bool
    {
        $pdo = Connection::getInstance();
        
        if ($this->id === null) {
            return $this->insert($pdo);
        } else {
            return $this->update($pdo);
        }
    }

    private function insert(PDO $pdo): bool
    {
        $sql = '
            INSERT INTO vpn_panels (
                name, base_url, api_username, api_password, api_token,
                is_active, max_users, current_users, panel_type,
                settings, created_at, updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW()
            )
        ';

        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            $this->name,
            $this->base_url,
            $this->api_username,
            $this->api_password,
            $this->api_token,
            $this->is_active,
            $this->max_users,
            $this->current_users,
            $this->panel_type,
            $this->settings ? json_encode($this->settings) : null,
        ]);

        if ($result) {
            $this->id = (int)$pdo->lastInsertId();
        }

        return $result;
    }

    private function update(PDO $pdo): bool
    {
        $sql = '
            UPDATE vpn_panels SET
                name = ?, base_url = ?, api_username = ?, api_password = ?,
                api_token = ?, is_active = ?, max_users = ?, current_users = ?,
                panel_type = ?, settings = ?, updated_at = NOW()
            WHERE id = ?
        ';

        $stmt = $pdo->prepare($sql);
        return $stmt->execute([
            $this->name,
            $this->base_url,
            $this->api_username,
            $this->api_password,
            $this->api_token,
            $this->is_active,
            $this->max_users,
            $this->current_users,
            $this->panel_type,
            $this->settings ? json_encode($this->settings) : null,
            $this->id,
        ]);
    }

    public static function fromArray(array $data): VpnPanel
    {
        $panel = new VpnPanel();
        $panel->id = $data['id'] ?? null;
        $panel->name = $data['name'];
        $panel->base_url = $data['base_url'];
        $panel->api_username = $data['api_username'];
        $panel->api_password = $data['api_password'];
        $panel->api_token = $data['api_token'];
        $panel->is_active = (bool)$data['is_active'];
        $panel->max_users = (int)$data['max_users'];
        $panel->current_users = (int)$data['current_users'];
        $panel->panel_type = $data['panel_type'];
        $panel->settings = $data['settings'] ? json_decode($data['settings'], true) : null;
        $panel->created_at = $data['created_at'];
        $panel->updated_at = $data['updated_at'];

        return $panel;
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'base_url' => $this->base_url,
            'api_username' => $this->api_username,
            'api_password' => $this->api_password,
            'api_token' => $this->api_token,
            'is_active' => $this->is_active,
            'max_users' => $this->max_users,
            'current_users' => $this->current_users,
            'panel_type' => $this->panel_type,
            'settings' => $this->settings,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }

    public function hasCapacity(): bool
    {
        return $this->current_users < $this->max_users;
    }

    public function getUsagePercentage(): float
    {
        if ($this->max_users <= 0) {
            return 0.0;
        }
        return ($this->current_users / $this->max_users) * 100;
    }
}
