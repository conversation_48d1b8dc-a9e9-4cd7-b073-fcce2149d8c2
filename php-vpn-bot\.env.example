# Bot Configuration
BOT_TOKEN=your_telegram_bot_token_here
BOT_USERNAME=your_bot_username
WEBHOOK_URL=https://your-domain.com/webhook
ADMIN_USER_ID=123456789

# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DATABASE=telegram_vpn_bot
POSTGRES_USER=vpn_bot
POSTGRES_PASSWORD=vpn_bot_pass

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# VPN Configuration
FREE_DATA_LIMIT=**********
FREE_DURATION_DAYS=7
TRIAL_DATA_LIMIT=**********
TRIAL_DURATION_DAYS=1

# Payment Configuration
PAYMENT_PROVIDER_TOKEN=your_payment_provider_token

# NowPayments Configuration
NOWPAYMENTS_API_KEY=your_nowpayments_api_key
NOWPAYMENTS_IPN_SECRET=your_nowpayments_ipn_secret

# TON Payment Configuration
TON_MASTER_WALLET=your_ton_wallet_address
TON_MASTER_PRIVATE_KEY=your_ton_private_key
TON_NETWORK=mainnet
TON_API_KEY=your_ton_api_key
TON_PAYMENT_TIMEOUT_MINUTES=30

# Marzban Configuration
MARZBAN_URL=https://your-marzban-panel.com
MARZBAN_USERNAME=admin
MARZBAN_PASSWORD=admin_password
MARZBAN_TOKEN=
MARZBAN_API_TIMEOUT=30

# Admin Panel Configuration
ADMIN_PANEL_USERNAME=admin
ADMIN_PANEL_PASSWORD=secure_admin_password

# Application Configuration
APP_ENV=production
APP_DEBUG=false
LOG_LEVEL=info
TIMEZONE=UTC

# Security
JWT_SECRET=your_jwt_secret_key_here
ENCRYPTION_KEY=your_encryption_key_here
