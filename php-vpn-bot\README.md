# PHP VPN Telegram Bot

A comprehensive PHP-based Telegram bot for VPN service management with reply keyboard interface, multi-language support, and premium subscription handling.

## Features

### 🤖 Bot Features
- **Reply Keyboard Interface**: Clean, persistent reply keyboards instead of inline keyboards
- **Multi-language Support**: English, Persian, Russian, Chinese
- **Dynamic Language Switching**: Keyboards update instantly when language changes
- **State Management**: Proper navigation state tracking

### 🔐 VPN Management
- **Trial Accounts**: Free trial VPN accounts with data/time limits
- **Premium Plans**: Multiple subscription tiers (1 month, 3 months, 6 months, 1 year)
- **Marzban Integration**: Full integration with Marzban VPN panel
- **Account Monitoring**: Real-time usage tracking and account management

### 💳 Payment System
- **Multiple Payment Methods**:
  - Telegram Stars
  - Cryptocurrency (via NowPayments)
  - TON Blockchain
  - Credit Cards
- **Payment Verification**: Automated payment processing and verification
- **Invoice Generation**: Automatic invoice creation for all payment methods

### 👥 User Management
- **Channel Subscription**: Required channel subscription verification
- **Referral System**: Complete referral program with rewards
- **User Analytics**: Comprehensive user statistics and tracking
- **Admin Panel**: Web-based administration interface

## Installation

### Prerequisites
- Docker and Docker Compose
- Domain name with SSL certificate
- Telegram Bot Token
- Marzban VPN Panel

### Quick Start

1. **Clone the repository**
```bash
git clone <repository-url>
cd php-vpn-bot
```

2. **Configure environment**
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Start the services**
```bash
docker-compose up -d
```

4. **Run database migrations**
```bash
docker-compose exec postgres psql -U vpn_bot -d telegram_vpn_bot -f /docker-entrypoint-initdb.d/001_create_tables.sql
```

5. **Set webhook**
```bash
curl https://your-domain.com/set-webhook
```

## Configuration

### Environment Variables

#### Bot Configuration
```env
BOT_TOKEN=your_telegram_bot_token_here
BOT_USERNAME=your_bot_username
WEBHOOK_URL=https://your-domain.com
ADMIN_USER_ID=123456789
```

#### Database Configuration
```env
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DATABASE=telegram_vpn_bot
POSTGRES_USER=vpn_bot
POSTGRES_PASSWORD=vpn_bot_pass
```

#### VPN Configuration
```env
FREE_DATA_LIMIT=**********  # 1GB in bytes
FREE_DURATION_DAYS=7
TRIAL_DATA_LIMIT=**********  # 1GB in bytes
TRIAL_DURATION_DAYS=1
```

#### Marzban Configuration
```env
MARZBAN_URL=https://your-marzban-panel.com
MARZBAN_USERNAME=admin
MARZBAN_PASSWORD=admin_password
```

#### Payment Configuration
```env
# Telegram Stars & Card Payments
PAYMENT_PROVIDER_TOKEN=your_payment_provider_token

# Cryptocurrency Payments
NOWPAYMENTS_API_KEY=your_nowpayments_api_key
NOWPAYMENTS_IPN_SECRET=your_nowpayments_ipn_secret

# TON Payments
TON_MASTER_WALLET=your_ton_wallet_address
TON_MASTER_PRIVATE_KEY=your_ton_private_key
TON_NETWORK=mainnet
```

## Bot Commands

### User Commands
- `/start` - Start the bot and show main menu
- `/menu` - Show main menu

### Reply Keyboard Navigation
The bot uses reply keyboards for all navigation:

#### Main Menu
- 🆓 Free Trial
- 💎 Premium Plans
- 📊 Dashboard
- 👤 My Accounts
- 🎁 Referral Program
- ⚙️ Settings
- ❓ Help
- 🆘 Support

#### Premium Plans
- 💎 1 Month - $9.99
- 💎 3 Months - $24.99
- 💎 6 Months - $44.99
- 💎 1 Year - $79.99

#### Payment Methods
- ⭐ Telegram Stars
- 💳 Credit Card
- ₿ Cryptocurrency
- 💎 TON

## Architecture

### Directory Structure
```
php-vpn-bot/
├── src/
│   ├── Bot/
│   │   ├── Commands/          # Telegram bot commands
│   │   ├── Handlers/          # Message and callback handlers
│   │   └── Keyboards/         # Reply keyboard builders
│   ├── Config/                # Configuration management
│   ├── Database/              # Database connection
│   ├── Models/                # Data models
│   ├── Services/              # Business logic services
│   └── Utils/                 # Utility classes
├── public/                    # Web entry point
├── database/                  # Database migrations
├── docker/                    # Docker configuration
└── logs/                      # Application logs
```

### Key Components

#### Reply Keyboard System
- `ReplyKeyboardBuilder`: Creates persistent reply keyboards
- Dynamic keyboard updates based on user language
- State-aware navigation with proper back button handling

#### Message Handler
- `MessageHandler`: Processes all text messages from reply keyboards
- State management for navigation flow
- Language-aware message processing

#### Services
- `VpnService`: VPN account management and Marzban integration
- `PaymentService`: Payment processing for all methods
- `ChannelService`: Channel subscription verification
- `ReferralService`: Referral system management

## Database Schema

### Core Tables
- `users`: User information and settings
- `vpn_accounts`: VPN account details and usage
- `payments`: Payment records and transactions
- `channels`: Required channels for subscription
- `referrals`: Referral relationships and rewards
- `premium_plans`: Available subscription plans

## API Integration

### Marzban VPN Panel
- Account creation and management
- Usage monitoring and limits
- Configuration generation

### Payment Processors
- **Telegram Payments**: Stars and card payments
- **NowPayments**: Cryptocurrency processing
- **TON**: Direct blockchain integration

## Monitoring and Logging

### Logging
- Application logs: `/var/www/html/logs/bot.log`
- PHP errors: `/var/www/html/logs/php_errors.log`
- Nginx logs: `/var/log/nginx/`

### Health Checks
- Bot health: `GET /health`
- Webhook status: `GET /webhook-info`

## Security

### Best Practices
- Environment variable configuration
- SQL injection prevention with prepared statements
- Input validation and sanitization
- Rate limiting and abuse prevention
- Secure webhook handling

### Data Protection
- User data encryption
- Secure payment processing
- GDPR compliance considerations

## Deployment

### Production Deployment

1. **Server Setup**
```bash
# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

2. **SSL Certificate**
```bash
# Using Certbot for Let's Encrypt
sudo apt install certbot
sudo certbot certonly --standalone -d your-domain.com
```

3. **Deploy Application**
```bash
git clone <repository-url>
cd php-vpn-bot
cp .env.example .env
# Configure .env file
docker-compose up -d
```

4. **Set Webhook**
```bash
curl https://your-domain.com/set-webhook
```

### Maintenance

#### Update Application
```bash
git pull origin main
docker-compose build --no-cache
docker-compose up -d
```

#### Database Backup
```bash
docker-compose exec postgres pg_dump -U vpn_bot telegram_vpn_bot > backup.sql
```

#### View Logs
```bash
docker-compose logs -f app
docker-compose logs -f postgres
```

## Support

For support and questions:
1. Check the logs for error messages
2. Verify environment configuration
3. Test webhook connectivity
4. Review database connections

## License

This project is licensed under the MIT License.
