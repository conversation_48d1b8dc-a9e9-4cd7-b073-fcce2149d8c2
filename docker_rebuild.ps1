# Docker Rebuild Script for VPN Bot (PowerShell)
# This script stops, rebuilds, and restarts the VPN bot with all latest fixes

Write-Host "🐳 VPN Bot Docker Rebuild Script" -ForegroundColor Blue
Write-Host "=================================" -ForegroundColor Blue

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Cyan
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error-Custom {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Check if Docker is running
try {
    docker info | Out-Null
    Write-Status "Docker is running. Proceeding with rebuild..."
} catch {
    Write-Error-Custom "Docker is not running. Please start Docker and try again."
    exit 1
}

# Step 1: Stop current containers
Write-Status "Step 1: Stopping current containers..."
try {
    $runningContainers = docker-compose ps -q
    if ($runningContainers) {
        docker-compose down
        Write-Success "Containers stopped successfully"
    } else {
        Write-Status "No running containers found"
    }
} catch {
    Write-Warning "Some containers may not have stopped cleanly"
}

# Step 2: Build with no cache to ensure all changes are applied
Write-Status "Step 2: Building containers with latest changes (no cache)..."
try {
    docker-compose build --no-cache
    Write-Success "Build completed successfully"
} catch {
    Write-Error-Custom "Build failed. Please check the output above for errors."
    exit 1
}

# Step 3: Start containers
Write-Status "Step 3: Starting containers..."
try {
    docker-compose up -d
    Write-Success "Containers started successfully"
} catch {
    Write-Error-Custom "Failed to start containers. Please check the logs."
    exit 1
}

# Step 4: Wait a moment for containers to initialize
Write-Status "Step 4: Waiting for containers to initialize..."
Start-Sleep -Seconds 10

# Step 5: Check container status
Write-Status "Step 5: Checking container status..."
docker-compose ps

# Step 6: Show logs
Write-Status "Step 6: Showing recent logs..."
Write-Host ""
Write-Status "Bot logs (last 20 lines):"
docker-compose logs --tail=20 bot

Write-Host ""
Write-Status "Database logs (last 10 lines):"
docker-compose logs --tail=10 postgres

# Step 7: Health check
Write-Status "Step 7: Performing health check..."
Start-Sleep -Seconds 5

# Check if bot container is running
$botStatus = docker-compose ps bot
if ($botStatus -match "Up") {
    Write-Success "✅ Bot container is running"
} else {
    Write-Error-Custom "❌ Bot container is not running properly"
    Write-Status "Showing bot logs for debugging:"
    docker-compose logs bot
    exit 1
}

# Check if database container is running
$dbStatus = docker-compose ps postgres
if ($dbStatus -match "Up") {
    Write-Success "✅ Database container is running"
} else {
    Write-Error-Custom "❌ Database container is not running properly"
    Write-Status "Showing database logs for debugging:"
    docker-compose logs postgres
    exit 1
}

# Final success message
Write-Host ""
Write-Host "🎉 Docker rebuild completed successfully!" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Blue
Write-Success "All containers are running properly"
Write-Status "You can now test your bot by sending /start on Telegram"
Write-Status "To view live logs, run: docker-compose logs -f bot"
Write-Status "To check status anytime, run: docker-compose ps"

# Optional: Show how to test
Write-Host ""
Write-Status "Testing recommendations:"
Write-Host "1. Send /start to your bot on Telegram"
Write-Host "2. Try /help to see all commands"
Write-Host "3. Test /trial for trial VPN functionality"
Write-Host "4. Test /premium for payment integration"
Write-Host "5. Test /referral for referral system"

Write-Host ""
Write-Status "If you encounter any issues:"
Write-Host "1. Check logs: docker-compose logs bot"
Write-Host "2. Restart containers: docker-compose restart"
Write-Host "3. Full rebuild: .\docker_rebuild.ps1"

Write-Host ""
Write-Success "VPN Bot is ready for production! 🚀"
