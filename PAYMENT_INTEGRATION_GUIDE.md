# Payment Integration Setup Guide

## Overview
This guide provides step-by-step instructions for configuring all payment methods in the VPN bot, including Telegram Stars, TON wallet integration, and other payment APIs.

## 🌟 Telegram Stars Payment Setup

### 1. Bot Configuration
Telegram Stars payments are built into the Telegram Bot API and require minimal setup.

**Required Environment Variables:**
```bash
# Your bot token from @BotFather
BOT_TOKEN=your_bot_token_here

# Payment provider token (optional for Stars)
PAYMENT_PROVIDER_TOKEN=your_provider_token_here
```

**Steps:**
1. **Get Bot Token**: Message @BotFather on Telegram and create a new bot or use existing one
2. **Enable Payments**: Contact @BotFather and use `/mybots` → Select your bot → Payments → Connect a payment provider
3. **For Stars**: Select "Telegram Stars" as payment method (no additional setup required)
4. **Test Mode**: Use test payment provider tokens for development

**Configuration in Code:**
```python
# In bot/config.py
BOT_TOKEN = os.getenv("BOT_TOKEN", "")
PAYMENT_PROVIDER_TOKEN = os.getenv("PAYMENT_PROVIDER_TOKEN", "")
```

### 2. Creating Star Invoices
```python
# Example usage in payment service
await context.bot.send_invoice(
    chat_id=chat_id,
    title="Premium VPN Plan",
    description="1 month premium VPN access",
    payload=f"plan_{plan_id}_user_{user_id}",
    provider_token="",  # Empty for Stars
    currency="XTR",     # Telegram Stars currency
    prices=[LabeledPrice("Premium Plan", amount_in_stars)]
)
```

## 💎 TON Wallet Integration Setup

### 1. TON Wallet Configuration
**Required Environment Variables:**
```bash
# TON API Configuration
TON_API_KEY=your_ton_api_key
TON_WALLET_ADDRESS=your_wallet_address
TON_WALLET_PRIVATE_KEY=your_private_key_hex
TON_NETWORK=mainnet  # or testnet for development

# TON Payment Settings
TON_PAYMENT_TIMEOUT=3600  # 1 hour timeout for payments
TON_CONFIRMATION_BLOCKS=3  # Number of confirmations required
```

### 2. TON API Setup
1. **Get TON API Access**: Register at https://toncenter.com/ or use TON HTTP API
2. **Create Wallet**: Generate a new TON wallet or use existing one
3. **Fund Wallet**: Add TON coins for transaction fees
4. **Get API Key**: Obtain API key from TON Center or similar service

**TON Service Configuration:**
```python
# In bot/services/ton_service.py
class TONService:
    def __init__(self):
        self.api_key = settings.TON_API_KEY
        self.wallet_address = settings.TON_WALLET_ADDRESS
        self.private_key = settings.TON_WALLET_PRIVATE_KEY
        self.network = settings.TON_NETWORK
        self.base_url = "https://toncenter.com/api/v2/"
```

### 3. TON Payment Flow
1. **Generate Payment Address**: Create unique address for each payment
2. **Monitor Blockchain**: Check for incoming transactions
3. **Verify Payment**: Confirm amount and sender
4. **Process Order**: Create VPN account after confirmation

## 🔗 Crypto Payment Integration

### 1. NowPayments Setup
**Required Environment Variables:**
```bash
# NowPayments Configuration
NOWPAYMENTS_API_KEY=your_nowpayments_api_key
NOWPAYMENTS_IPN_SECRET=your_ipn_secret_key
NOWPAYMENTS_SANDBOX=true  # Set to false for production

# Webhook Configuration
WEBHOOK_URL=https://yourdomain.com/webhook/nowpayments
```

### 2. NowPayments Account Setup
1. **Register**: Create account at https://nowpayments.io/
2. **Verify Account**: Complete KYC verification
3. **Get API Key**: Generate API key from dashboard
4. **Set Webhook**: Configure IPN webhook URL
5. **Test Integration**: Use sandbox mode for testing

**Supported Cryptocurrencies:**
- Bitcoin (BTC)
- Ethereum (ETH)
- Litecoin (LTC)
- Dogecoin (DOGE)
- And 200+ other cryptocurrencies

### 3. Crypto Payment Flow
```python
# Create payment
payment_data = {
    "price_amount": plan_price,
    "price_currency": "USD",
    "pay_currency": "BTC",  # User selected crypto
    "order_id": f"order_{user_id}_{plan_id}_{timestamp}",
    "order_description": f"VPN Plan: {plan_name}",
    "ipn_callback_url": f"{WEBHOOK_URL}/nowpayments",
    "success_url": f"{WEBHOOK_URL}/success",
    "cancel_url": f"{WEBHOOK_URL}/cancel"
}
```

## 💳 Traditional Payment Methods

### 1. Stripe Integration
**Required Environment Variables:**
```bash
# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key
STRIPE_SECRET_KEY=sk_test_your_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
STRIPE_ENDPOINT_SECRET=your_endpoint_secret
```

### 2. PayPal Integration
**Required Environment Variables:**
```bash
# PayPal Configuration
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_MODE=sandbox  # or live for production
PAYPAL_WEBHOOK_ID=your_webhook_id
```

## 🔧 Environment Configuration

### 1. Complete .env File Template
```bash
# Bot Configuration
BOT_TOKEN=your_telegram_bot_token
ADMIN_USER_ID=your_telegram_user_id

# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_USER=vpn_bot
POSTGRES_PASSWORD=secure_password
POSTGRES_DATABASE=telegram_vpn_bot
POSTGRES_PORT=5432

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# Payment Configuration
PAYMENT_PROVIDER_TOKEN=your_payment_provider_token

# TON Configuration
TON_API_KEY=your_ton_api_key
TON_WALLET_ADDRESS=your_ton_wallet_address
TON_WALLET_PRIVATE_KEY=your_private_key
TON_NETWORK=mainnet

# Crypto Payments
NOWPAYMENTS_API_KEY=your_nowpayments_api_key
NOWPAYMENTS_IPN_SECRET=your_ipn_secret
NOWPAYMENTS_SANDBOX=true

# Stripe
STRIPE_PUBLISHABLE_KEY=pk_test_your_key
STRIPE_SECRET_KEY=sk_test_your_key
STRIPE_WEBHOOK_SECRET=whsec_your_secret

# PayPal
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_secret
PAYPAL_MODE=sandbox

# Webhook Configuration
WEBHOOK_URL=https://yourdomain.com
```

### 2. Docker Environment
```yaml
# docker-compose.yml
version: '3.8'
services:
  bot:
    build: .
    environment:
      - BOT_TOKEN=${BOT_TOKEN}
      - POSTGRES_HOST=postgres
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DATABASE=${POSTGRES_DATABASE}
      - REDIS_HOST=redis
      - TON_API_KEY=${TON_API_KEY}
      - NOWPAYMENTS_API_KEY=${NOWPAYMENTS_API_KEY}
    depends_on:
      - postgres
      - redis
```

## 🧪 Testing Payment Integration

### 1. Test Telegram Stars
```python
# Test Stars payment
await payment_service.create_stars_invoice(
    user_id=123456789,
    plan_id=1,
    amount_stars=100  # 100 Stars
)
```

### 2. Test TON Payment
```python
# Test TON payment
payment_address = await ton_service.generate_payment_address(
    user_id=123456789,
    amount_ton=1.5  # 1.5 TON
)
```

### 3. Test Crypto Payment
```python
# Test crypto payment
payment_url = await nowpayments_service.create_payment(
    amount_usd=9.99,
    currency="BTC",
    order_id="test_order_123"
)
```

## 🔒 Security Considerations

### 1. Webhook Security
- Always verify webhook signatures
- Use HTTPS for all webhook endpoints
- Implement rate limiting
- Validate all incoming data

### 2. API Key Management
- Store API keys in environment variables
- Use different keys for development/production
- Rotate keys regularly
- Monitor API usage

### 3. Payment Validation
- Always verify payment amounts
- Check payment status before processing
- Implement timeout mechanisms
- Log all payment activities

## 📊 Monitoring and Analytics

### 1. Payment Tracking
- Log all payment attempts
- Track conversion rates
- Monitor failed payments
- Set up alerts for issues

### 2. Error Handling
- Implement retry mechanisms
- Handle network timeouts
- Process webhook failures
- Provide user feedback

## 🚀 Production Deployment

### 1. Pre-deployment Checklist
- [ ] All API keys configured
- [ ] Webhook endpoints tested
- [ ] SSL certificates installed
- [ ] Database migrations applied
- [ ] Payment flows tested
- [ ] Error handling verified
- [ ] Monitoring setup complete

### 2. Go-Live Steps
1. Switch to production API keys
2. Update webhook URLs
3. Test with small amounts
4. Monitor for 24 hours
5. Scale up gradually

## 📞 Support and Troubleshooting

### Common Issues
1. **Invalid API Key**: Check environment variables
2. **Webhook Not Receiving**: Verify URL and SSL
3. **Payment Not Processing**: Check logs and API status
4. **Amount Mismatch**: Verify currency conversion

### Getting Help
- Check payment provider documentation
- Review bot logs for errors
- Test in sandbox/development mode
- Contact payment provider support
