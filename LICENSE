MIT License

Copyright (c) 2024 VPN Telegram Bot Contributors

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMplied, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

---

## Third-Party Licenses

This project includes or depends on third-party software components that are
licensed under their own terms. Below is a summary of the key dependencies
and their licenses:

### Python Dependencies

- **python-telegram-bot** - LGPLv3 License
  - Copyright (c) 2015-2024 Leandro Toledo de Souza
  - https://github.com/python-telegram-bot/python-telegram-bot

- **FastAPI** - MIT License
  - Copyright (c) 2018 Sebastián Ramírez
  - https://github.com/tiangolo/fastapi

- **SQLAlchemy** - MIT License
  - Copyright (c) 2006-2024 the SQLAlchemy authors and contributors
  - https://github.com/sqlalchemy/sqlalchemy

- **Alembic** - MIT License
  - Copyright (c) 2009-2024 Michael Bayer
  - https://github.com/sqlalchemy/alembic

- **Pydantic** - MIT License
  - Copyright (c) 2017 to present Pydantic Services Inc. and individual contributors
  - https://github.com/pydantic/pydantic

- **Redis-py** - MIT License
  - Copyright (c) 2012 Andy McCurdy
  - https://github.com/redis/redis-py

- **Uvicorn** - BSD 3-Clause License
  - Copyright (c) 2017-present, Encode OSS Ltd.
  - https://github.com/encode/uvicorn

- **Asyncpg** - Apache License 2.0
  - Copyright (c) 2016-present MagicStack Inc.
  - https://github.com/MagicStack/asyncpg

- **Cryptography** - Apache License 2.0 and BSD 3-Clause License
  - Copyright (c) Individual contributors
  - https://github.com/pyca/cryptography

- **Jinja2** - BSD 3-Clause License
  - Copyright (c) 2007 by the Jinja Team
  - https://github.com/pallets/jinja

- **Click** - BSD 3-Clause License
  - Copyright (c) 2014 by the Pallets team
  - https://github.com/pallets/click

### Development Dependencies

- **pytest** - MIT License
  - Copyright (c) 2004 Holger Krekel and others
  - https://github.com/pytest-dev/pytest

- **Black** - MIT License
  - Copyright (c) 2018 Łukasz Langa
  - https://github.com/psf/black

- **isort** - MIT License
  - Copyright (c) 2013 Timothy Edmund Crosley
  - https://github.com/PyCQA/isort

- **Flake8** - MIT License
  - Copyright (c) 2011-2013 Tarek Ziade
  - https://github.com/PyCQA/flake8

- **MyPy** - MIT License
  - Copyright (c) 2012-2021 Jukka Lehtosalo and contributors
  - https://github.com/python/mypy

### Infrastructure Dependencies

- **PostgreSQL** - PostgreSQL License (similar to BSD or MIT)
  - Copyright (c) 1996-2024, The PostgreSQL Global Development Group
  - https://www.postgresql.org/

- **Redis** - BSD 3-Clause License
  - Copyright (c) 2009-2012, Salvatore Sanfilippo
  - https://redis.io/

- **Docker** - Apache License 2.0
  - Copyright (c) 2013-2024 Docker, Inc.
  - https://www.docker.com/

### Monitoring and Observability

- **Prometheus** - Apache License 2.0
  - Copyright (c) 2012-2015 The Prometheus Authors
  - https://prometheus.io/

- **Grafana** - Apache License 2.0
  - Copyright (c) 2014-2024 Grafana Labs
  - https://grafana.com/

- **Sentry** - Business Source License 1.1
  - Copyright (c) 2012-2024 Functional Software, Inc.
  - https://sentry.io/

## License Compatibility

This project is distributed under the MIT License, which is compatible with
all the licenses of the dependencies listed above. The MIT License is a
permissive license that allows for commercial use, modification, distribution,
and private use.

## Attribution Requirements

While the MIT License does not require attribution in derivative works beyond
including the license text, we encourage users to:

1. Maintain attribution to the original authors
2. Include this license file in distributions
3. Respect the licenses of all dependencies
4. Consider contributing improvements back to the community

## Disclaimer

The license information provided above is for informational purposes and may
not be complete or up-to-date. Users should verify license information
independently and ensure compliance with all applicable licenses.

For the most current license information for each dependency, please refer to
the respective project repositories and documentation.

## Contact

For questions about licensing or to report license-related issues:

- **Email**: <EMAIL>
- **GitHub Issues**: https://github.com/your-username/vpn-telegram-bot/issues
- **Documentation**: https://github.com/your-username/vpn-telegram-bot/docs

---

**Note**: This project is provided "as is" without warranty of any kind.
Users are responsible for ensuring compliance with all applicable laws and
regulations in their jurisdiction, including but not limited to data
protection, privacy, and telecommunications regulations.