from typing import List, Dict, Any, Optional
from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status, Request, Form
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.security import HTTPBasic, HTTPBasicCredentials
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from bot.database import get_db
from bot.models import BotSettings, VPNPanel, PremiumPlan, User, VPNAccount, Channel
from bot.marzban_api import get_system_stats_from_marzban, reset_user_usage_marzban # Import Marzban API functions
from admin.config import admin_settings # Import admin settings

import json
import secrets
import logging

logger = logging.getLogger(__name__)

app = FastAPI(title="VPN Bot Admin Panel")
security = HTTPBasic()
templates = Jinja2Templates(directory="admin/templates")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3002"],  # React app URL
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
    expose_headers=["*"],
    max_age=3600,
)

async def authenticate_admin(credentials: HTTPBasicCredentials = Depends(security)):
    correct_username = secrets.compare_digest(credentials.username, admin_settings.ADMIN_PANEL_USERNAME)
    correct_password = secrets.compare_digest(credentials.password, admin_settings.ADMIN_PANEL_PASSWORD)
    if not (correct_username and correct_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Basic"},
        )
    return credentials.username

# HTML Routes
@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request, username: str = Depends(authenticate_admin)):
    return templates.TemplateResponse("index.html", {"request": request, "username": username})

@app.get("/settings", response_class=HTMLResponse)
async def get_settings_page(request: Request, db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    settings_obj = (await db.execute(select(BotSettings))).scalar_one_or_none()
    if not settings_obj:
        settings_obj = BotSettings() # Create a default one if not exists
        db.add(settings_obj)
        await db.commit()
        await db.refresh(settings_obj)
    
    # Convert required_channels list to a comma-separated string for display in HTML
    required_channels_str = ", ".join(settings_obj.required_channels) if settings_obj.required_channels else ""

    return templates.TemplateResponse("settings.html", {"request": request, "settings": settings_obj, "required_channels_str": required_channels_str, "username": username})


@app.get("/panels", response_class=HTMLResponse)
async def get_panels_page(request: Request, db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    panels = (await db.execute(select(VPNPanel))).scalars().all()
    return templates.TemplateResponse("panels.html", {"request": request, "panels": panels, "username": username})

@app.get("/premium_plans", response_class=HTMLResponse)
async def get_premium_plans_page(request: Request, db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    plans = (await db.execute(select(PremiumPlan))).scalars().all()
    return templates.TemplateResponse("premium_plans.html", {"request": request, "plans": plans, "username": username})

@app.get("/users", response_class=HTMLResponse)
async def get_users_page(request: Request, db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    # Initially, display no users or a limited set. Users will be fetched via AJAX/API.
    return templates.TemplateResponse("users.html", {"request": request, "username": username})

@app.get("/channels", response_class=HTMLResponse)
async def get_channels_page(request: Request, db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    channels = (await db.execute(select(Channel))).scalars().all()
    return templates.TemplateResponse("channels.html", {"request": request, "channels": channels, "username": username})


@app.post("/api/channels", status_code=status.HTTP_201_CREATED)
async def add_channel(request: Request, db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    form_data = await request.form()
    channel_id = form_data.get("channel_id")
    is_required = form_data.get("is_required") == "on"

    if not channel_id:
        raise HTTPException(status_code=400, detail="Channel ID is required")

    new_channel = Channel(
        channel_id=channel_id,
        is_required=is_required
    )
    db.add(new_channel)
    await db.commit()
    await db.refresh(new_channel)
    return RedirectResponse(url="/channels", status_code=status.HTTP_303_SEE_OTHER)


@app.post("/api/channels/{channel_id}/delete", status_code=status.HTTP_200_OK)
async def delete_channel(channel_id: int, db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    channel = (await db.execute(select(Channel).filter(Channel.id == channel_id))).scalar_one_or_none()
    if not channel:
        raise HTTPException(status_code=404, detail="Channel not found")

    await db.delete(channel)
    await db.commit()
    return RedirectResponse(url="/channels", status_code=status.HTTP_303_SEE_OTHER)

@app.get("/trial", response_class=HTMLResponse)
async def get_trial_page(request: Request, db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    settings_obj = (await db.execute(select(BotSettings))).scalar_one_or_none()
    if not settings_obj:
        settings_obj = BotSettings()
        db.add(settings_obj)
        await db.commit()
        await db.refresh(settings_obj)
    return templates.TemplateResponse("trial.html", {"request": request, "settings": settings_obj, "username": username})


# API Endpoints for Trial Management
@app.post("/api/trial/reset", status_code=status.HTTP_200_OK)
async def reset_all_trials(username: str = Depends(authenticate_admin)):
    from bot.tasks import reset_all_trial_users_task
    reset_all_trial_users_task.delay()
    return {"message": "Trial reset task has been initiated for all users."}

# API Endpoints for Bot Settings
@app.get("/api/settings", status_code=status.HTTP_200_OK)
async def get_bot_settings(db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    settings_obj = (await db.execute(select(BotSettings))).scalar_one_or_none()
    if not settings_obj:
        settings_obj = BotSettings()  # Create a default one if not exists
        db.add(settings_obj)
        await db.commit()
        await db.refresh(settings_obj)
    return settings_obj.to_dict()

@app.put("/api/settings", status_code=status.HTTP_200_OK)
async def update_bot_settings(request: Request, db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    try:
        data = await request.json()
    except:
        # Fallback to form data for backward compatibility
        form_data = await request.form()
        data = {
            "free_data_limit": int(form_data.get("free_data_limit")) if form_data.get("free_data_limit") else None,
            "free_duration_days": int(form_data.get("free_duration_days")) if form_data.get("free_duration_days") else None,
            "required_channels": [ch.strip() for ch in form_data.get("required_channels", "").split(",") if ch.strip()],
            "payment_provider_token": form_data.get("payment_provider_token"),
            "trial_vpn_panel_id": int(form_data.get("trial_vpn_panel_id")) if form_data.get("trial_vpn_panel_id") and form_data.get("trial_vpn_panel_id") != "" else None,
            "max_trials_per_user": int(form_data.get("max_trials_per_user", 1)),
            "trial_reset_days": int(form_data.get("trial_reset_days", 30)),
            "auto_notify_trial_reset": form_data.get("auto_notify_trial_reset") == "on"
        }
    
    settings_obj = (await db.execute(select(BotSettings))).scalar_one_or_none()
    if not settings_obj:
        settings_obj = BotSettings()  # Create a default one if not exists
        db.add(settings_obj)
    
    # Update settings from data
    if data.get("free_data_limit") is not None:
        settings_obj.free_data_limit = int(data.get("free_data_limit"))
    if data.get("free_duration_days") is not None:
        settings_obj.free_duration_days = int(data.get("free_duration_days"))
    if data.get("required_channels") is not None:
        settings_obj.required_channels = data.get("required_channels")
    if data.get("payment_provider_token") is not None:
        settings_obj.payment_provider_token = data.get("payment_provider_token")
    if data.get("trial_vpn_panel_id") is not None:
        settings_obj.trial_vpn_panel_id = data.get("trial_vpn_panel_id")
    if data.get("max_trials_per_user") is not None:
        settings_obj.max_trials_per_user = int(data.get("max_trials_per_user"))
    if data.get("trial_reset_days") is not None:
        settings_obj.trial_reset_days = int(data.get("trial_reset_days"))
    if data.get("auto_notify_trial_reset") is not None:
        settings_obj.auto_notify_trial_reset = data.get("auto_notify_trial_reset")
    
    await db.commit()
    await db.refresh(settings_obj)
    
    return {"message": "Settings updated successfully", "settings": settings_obj.to_dict()}

# API Endpoints for Marzban Panel Management
@app.post("/api/panels", status_code=status.HTTP_201_CREATED)
async def add_panel(request: Request, db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    try:
        data = await request.json()
    except:
        # Fallback to form data for backward compatibility
        form_data = await request.form()
        data = {
            "name": form_data.get("name"),
            "base_url": form_data.get("base_url"),
            "api_username": form_data.get("api_username"),
            "api_password": form_data.get("api_password"),
            "is_active": form_data.get("is_active") == "on",
            "proxies": json.loads(form_data.get("proxies", "{}")),
            "inbounds": json.loads(form_data.get("inbounds", "{}"))
        }
    
    new_panel = VPNPanel(
        name=data.get("name"),
        base_url=data.get("base_url"),
        api_username=data.get("api_username"),
        api_password=data.get("api_password"),
        is_active=data.get("is_active", True),
        proxies=data.get("proxies", {}),
        inbounds=data.get("inbounds", {})
    )
    db.add(new_panel)
    await db.commit()
    await db.refresh(new_panel)
    return {"message": "VPN Panel added successfully", "panel": new_panel.to_dict()}

@app.get("/api/panels", status_code=status.HTTP_200_OK)
async def get_panels(db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    panels = (await db.execute(select(VPNPanel))).scalars().all()
    return [panel.to_dict() for panel in panels]

@app.put("/api/panels/{panel_id}", status_code=status.HTTP_200_OK)
async def update_panel(panel_id: int, request: Request, db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    panel = (await db.execute(select(VPNPanel).filter(VPNPanel.id == panel_id))).scalar_one_or_none()
    if not panel:
        raise HTTPException(status_code=404, detail="Panel not found")
    
    try:
        data = await request.json()
    except:
        # Fallback to form data for backward compatibility
        form_data = await request.form()
        data = {
            "name": form_data.get("name"),
            "base_url": form_data.get("base_url"),
            "api_username": form_data.get("api_username"),
            "api_password": form_data.get("api_password"),
            "is_active": form_data.get("is_active") == "on",
            "proxies": json.loads(form_data.get("proxies", "{}")),
            "inbounds": json.loads(form_data.get("inbounds", "{}"))
        }
    
    panel.name = data.get("name", panel.name)
    panel.base_url = data.get("base_url", panel.base_url)
    panel.api_username = data.get("api_username", panel.api_username)
    panel.api_password = data.get("api_password", panel.api_password)
    panel.is_active = data.get("is_active", panel.is_active)
    panel.proxies = data.get("proxies", panel.proxies)
    panel.inbounds = data.get("inbounds", panel.inbounds)
    
    await db.commit()
    await db.refresh(panel)
    return {"message": "Panel updated successfully", "panel": panel.to_dict()}

@app.delete("/api/panels/{panel_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_panel(panel_id: int, db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    panel = (await db.execute(select(VPNPanel).filter(VPNPanel.id == panel_id))).scalar_one_or_none()
    if not panel:
        raise HTTPException(status_code=404, detail="Panel not found")
    await db.delete(panel)
    await db.commit()
    return

# API Endpoints for Premium Plans
@app.post("/api/premium_plans", status_code=status.HTTP_201_CREATED)
async def add_premium_plan(request: Request, db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    try:
        data = await request.json()
    except:
        # Fallback to form data for backward compatibility
        form_data = await request.form()
        data = {
            "name": form_data.get("name"),
            "price": float(form_data.get("price")),
            "duration_days": int(form_data.get("duration_days")),
            "data_limit": int(form_data.get("data_limit")),
            "description": form_data.get("description"),
            "is_active": form_data.get("is_active") == "on"
        }

    new_plan = PremiumPlan(
        name=data.get("name"),
        price=float(data.get("price")),
        duration_days=int(data.get("duration_days")),
        data_limit=int(data.get("data_limit")),
        description=data.get("description"),
        is_active=data.get("is_active", True)
    )
    db.add(new_plan)
    await db.commit()
    await db.refresh(new_plan)
    return {"message": "Premium plan added successfully", "plan": new_plan.to_dict()}

@app.get("/api/premium_plans", status_code=status.HTTP_200_OK)
async def get_premium_plans(db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    plans = (await db.execute(select(PremiumPlan))).scalars().all()
    return [plan.to_dict() for plan in plans]

@app.put("/api/premium_plans/{plan_id}", status_code=status.HTTP_200_OK)
async def update_premium_plan(plan_id: int, request: Request, db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    plan = (await db.execute(select(PremiumPlan).filter(PremiumPlan.id == plan_id))).scalar_one_or_none()
    if not plan:
        raise HTTPException(status_code=404, detail="Premium plan not found")
    
    try:
        data = await request.json()
    except:
        # Fallback to form data for backward compatibility
        form_data = await request.form()
        data = {
            "name": form_data.get("name"),
            "price": float(form_data.get("price")),
            "duration_days": int(form_data.get("duration_days")),
            "data_limit": int(form_data.get("data_limit")),
            "description": form_data.get("description"),
            "is_active": form_data.get("is_active") == "on"
        }
    
    plan.name = data.get("name", plan.name)
    plan.price = float(data.get("price", plan.price))
    plan.duration_days = int(data.get("duration_days", plan.duration_days))
    plan.data_limit = int(data.get("data_limit", plan.data_limit))
    plan.description = data.get("description", plan.description)
    plan.is_active = data.get("is_active", plan.is_active)
    
    await db.commit()
    await db.refresh(plan)
    return {"message": "Premium plan updated successfully", "plan": plan.to_dict()}

@app.delete("/api/premium_plans/{plan_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_premium_plan(plan_id: int, db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    plan = (await db.execute(select(PremiumPlan).filter(PremiumPlan.id == plan_id))).scalar_one_or_none()
    if not plan:
        raise HTTPException(status_code=404, detail="Premium plan not found")
    await db.delete(plan)
    await db.commit()
    return

# API Endpoints for User Management
@app.get("/api/users", status_code=status.HTTP_200_OK)
async def search_users(query: Optional[str] = None, db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    stmt = select(User).order_by(User.created_at.desc())
    if query:
        search_query = f"%{query.lower()}%"
        stmt = stmt.filter(
            (User.username.ilike(search_query)) |
            (User.first_name.ilike(search_query)) |
            (User.last_name.ilike(search_query)) |
            (User.telegram_id == query) # Allow searching by exact telegram_id as string
        )
    users = (await db.execute(stmt)).scalars().all()
    return [user.to_dict() for user in users]

@app.post("/api/users/{user_id}/reset_vpn_usage", status_code=status.HTTP_200_OK)
async def reset_user_vpn_usage(user_id: int, db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    user = (await db.execute(select(User).filter(User.id == user_id))).scalar_one_or_none()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    vpn_account = (await db.execute(select(VPNAccount).filter(VPNAccount.user_id == user_id))).scalar_one_or_none()
    if not vpn_account:
        raise HTTPException(status_code=404, detail="VPN account not found for this user")
    
    # Call the Marzban API to reset usage
    success, message = await reset_user_usage_marzban(vpn_account.vpn_panel_id, vpn_account.username)

    if not success:
        raise HTTPException(status_code=500, detail=f"Failed to reset VPN usage: {message}")
    
    return {"message": f"VPN usage for user {user.username or user.telegram_id} reset successfully"}

@app.post("/api/users/{user_id}/reset_trial", status_code=status.HTTP_200_OK)
async def reset_user_trial(user_id: int, db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    user = (await db.execute(select(User).filter(User.id == user_id))).scalar_one_or_none()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Reset trial status
    user.has_used_trial = False
    user.trial_count = 0
    user.last_trial_at = None
    
    await db.commit()
    await db.refresh(user)
    
    return {"message": f"Trial status for user {user.username or user.telegram_id} reset successfully"}

@app.get("/api/dashboard/stats", status_code=status.HTTP_200_OK)
async def get_dashboard_stats(db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    from sqlalchemy import func
    
    # Get dashboard statistics
    total_users = (await db.execute(select(func.count(User.id)))).scalar()
    active_users = (await db.execute(select(func.count(User.id)).filter(User.is_active == True))).scalar()
    total_panels = (await db.execute(select(func.count(VPNPanel.id)))).scalar()
    total_plans = (await db.execute(select(func.count(PremiumPlan.id)))).scalar()
    
    return {
        "totalUsers": total_users or 0,
        "activeUsers": active_users or 0,
        "totalPanels": total_panels or 0,
        "totalPlans": total_plans or 0
    }

@app.get("/api/trial_stats", status_code=status.HTTP_200_OK)
async def get_trial_stats(db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    from sqlalchemy import func
    
    # Get trial statistics
    total_users = (await db.execute(select(func.count(User.id)))).scalar()
    users_with_trial = (await db.execute(select(func.count(User.id)).filter(User.has_used_trial == True))).scalar()
    users_without_trial = total_users - users_with_trial
    
    return {
        "total_users": total_users,
        "users_with_trial": users_with_trial,
        "users_without_trial": users_without_trial
    }

# API Endpoints for Channel Management
@app.post("/api/channels", status_code=status.HTTP_201_CREATED)
async def add_channel(request: Request, db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    try:
        data = await request.json()
    except:
        # Fallback to form data for backward compatibility
        form_data = await request.form()
        data = {
            "channel_id": form_data.get("channel_id"),
            "channel_name": form_data.get("channel_name"),
            "channel_url": form_data.get("channel_url", ""),
            "invite_link": form_data.get("invite_link", ""),
            "is_required": form_data.get("is_required") == "on",
            "is_active": form_data.get("is_active", "on") == "on",
            "priority": int(form_data.get("priority", 0)),
            "description": form_data.get("description", ""),
            "advertising_enabled": form_data.get("advertising_enabled") == "on",
            "advertising_message": form_data.get("advertising_message", "")
        }

    # Check if channel already exists
    existing_channel = (await db.execute(select(Channel).filter(Channel.channel_id == data.get("channel_id")))).scalar_one_or_none()
    if existing_channel:
        raise HTTPException(status_code=400, detail="Channel with this ID already exists")

    new_channel = Channel(
        channel_id=data.get("channel_id"),
        channel_name=data.get("channel_name"),
        channel_url=data.get("channel_url", ""),
        invite_link=data.get("invite_link", ""),
        is_required=data.get("is_required", True),
        is_active=data.get("is_active", True),
        priority=data.get("priority", 0),
        description=data.get("description", ""),
        advertising_enabled=data.get("advertising_enabled", False),
        advertising_message=data.get("advertising_message", "")
    )
    db.add(new_channel)
    await db.commit()
    await db.refresh(new_channel)
    return {"message": "Channel added successfully", "channel": new_channel.to_dict()}

@app.get("/api/channels", status_code=status.HTTP_200_OK)
async def get_channels(db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    channels = (await db.execute(select(Channel))).scalars().all()
    return [channel.to_dict() for channel in channels]

@app.put("/api/channels/{channel_id}", status_code=status.HTTP_200_OK)
async def update_channel(channel_id: int, request: Request, db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    try:
        data = await request.json()
    except:
        # Fallback to form data for backward compatibility
        form_data = await request.form()
        data = {
            "channel_name": form_data.get("channel_name"),
            "channel_url": form_data.get("channel_url"),
            "invite_link": form_data.get("invite_link"),
            "is_required": form_data.get("is_required") == "on",
            "is_active": form_data.get("is_active") == "on",
            "priority": int(form_data.get("priority", 0)) if form_data.get("priority") else None,
            "description": form_data.get("description"),
            "advertising_enabled": form_data.get("advertising_enabled") == "on",
            "advertising_message": form_data.get("advertising_message")
        }

    channel = (await db.execute(select(Channel).filter(Channel.id == channel_id))).scalar_one_or_none()
    if not channel:
        raise HTTPException(status_code=404, detail="Channel not found")

    # Update channel fields
    if data.get("channel_name") is not None:
        channel.channel_name = data.get("channel_name")
    if data.get("channel_url") is not None:
        channel.channel_url = data.get("channel_url")
    if data.get("invite_link") is not None:
        channel.invite_link = data.get("invite_link")
    if data.get("is_required") is not None:
        channel.is_required = data.get("is_required")
    if data.get("is_active") is not None:
        channel.is_active = data.get("is_active")
    if data.get("priority") is not None:
        channel.priority = data.get("priority")
    if data.get("description") is not None:
        channel.description = data.get("description")
    if data.get("advertising_enabled") is not None:
        channel.advertising_enabled = data.get("advertising_enabled")
    if data.get("advertising_message") is not None:
        channel.advertising_message = data.get("advertising_message")

    await db.commit()
    await db.refresh(channel)
    return {"message": "Channel updated successfully", "channel": channel.to_dict()}

@app.delete("/api/channels/{channel_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_channel(channel_id: int, db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    channel = (await db.execute(select(Channel).filter(Channel.id == channel_id))).scalar_one_or_none()
    if not channel:
        raise HTTPException(status_code=404, detail="Channel not found")
    await db.delete(channel)
    await db.commit()
    return

@app.post("/api/channels/{channel_id}/toggle-active", status_code=status.HTTP_200_OK)
async def toggle_channel_active(channel_id: int, db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    """Toggle channel active status"""
    channel = (await db.execute(select(Channel).filter(Channel.id == channel_id))).scalar_one_or_none()
    if not channel:
        raise HTTPException(status_code=404, detail="Channel not found")

    channel.is_active = not channel.is_active
    await db.commit()
    await db.refresh(channel)

    status_text = "enabled" if channel.is_active else "disabled"
    return {"message": f"Channel {status_text} successfully", "channel": channel.to_dict()}

@app.post("/api/channels/{channel_id}/toggle-advertising", status_code=status.HTTP_200_OK)
async def toggle_channel_advertising(channel_id: int, db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    """Toggle channel advertising status"""
    channel = (await db.execute(select(Channel).filter(Channel.id == channel_id))).scalar_one_or_none()
    if not channel:
        raise HTTPException(status_code=404, detail="Channel not found")

    channel.advertising_enabled = not channel.advertising_enabled
    await db.commit()
    await db.refresh(channel)

    status_text = "enabled" if channel.advertising_enabled else "disabled"
    return {"message": f"Channel advertising {status_text} successfully", "channel": channel.to_dict()}

@app.post("/api/channels/bulk-enable", status_code=status.HTTP_200_OK)
async def bulk_enable_channels(request: Request, db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    """Enable multiple channels"""
    try:
        data = await request.json()
        channel_ids = data.get('channel_ids', [])
        if not channel_ids:
            raise HTTPException(status_code=400, detail="No channel IDs provided")

        channels = (await db.execute(select(Channel).filter(Channel.id.in_(channel_ids)))).scalars().all()

        for channel in channels:
            channel.is_active = True

        await db.commit()

        return {"message": f"Enabled {len(channels)} channels"}
    except Exception as e:
        logger.error(f"Error bulk enabling channels: {e}")
        raise HTTPException(status_code=500, detail="Failed to bulk enable channels")

@app.post("/api/channels/bulk-disable", status_code=status.HTTP_200_OK)
async def bulk_disable_channels(request: Request, db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    """Disable multiple channels"""
    try:
        data = await request.json()
        channel_ids = data.get('channel_ids', [])
        if not channel_ids:
            raise HTTPException(status_code=400, detail="No channel IDs provided")

        channels = (await db.execute(select(Channel).filter(Channel.id.in_(channel_ids)))).scalars().all()

        for channel in channels:
            channel.is_active = False

        await db.commit()

        return {"message": f"Disabled {len(channels)} channels"}
    except Exception as e:
        logger.error(f"Error bulk disabling channels: {e}")
        raise HTTPException(status_code=500, detail="Failed to bulk disable channels")

# Debug endpoint to list all routes
@app.post("/api/users/{user_id}/reset-trial-status")
async def reset_user_trial_status(user_id: int, db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    """Reset user trial status"""
    user = (await db.execute(select(User).filter(User.id == user_id))).scalar_one_or_none()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Reset trial status
    user.has_used_trial = False
    user.trial_count = 0
    user.last_trial_at = None
    
    await db.commit()
    await db.refresh(user)
    
    return {"message": "Trial status reset successfully"}

@app.post("/api/users/bulk-reset-trials")
async def bulk_reset_trials(request: Request, db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    """Reset trial status for multiple users"""
    try:
        data = await request.json()
        user_ids = data.get('user_ids', [])
        if not user_ids:
            raise HTTPException(status_code=400, detail="No user IDs provided")

        # Reset trial status for all specified users
        users = (await db.execute(select(User).filter(User.id.in_(user_ids)))).scalars().all()

        for user in users:
            user.has_used_trial = False
            user.trial_count = 0
            user.last_trial_at = None

        await db.commit()

        return {"message": f"Trials reset for {len(users)} users"}
    except Exception as e:
        logger.error(f"Error bulk resetting trials: {e}")
        raise HTTPException(status_code=500, detail="Failed to bulk reset trials")

@app.post("/api/users/reset-trials-for-channel-followers")
async def reset_trials_for_channel_followers(db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    """Reset trial status for users who have followed all required channels"""
    try:
        # Get all users who have subscribed to all required channels
        query = """
            SELECT DISTINCT u.id, u.telegram_id, u.username, u.first_name
            FROM users u
            WHERE u.has_used_trial = true
            AND NOT EXISTS (
                SELECT 1 FROM channels c
                WHERE c.is_required = true AND c.is_active = true
                AND NOT EXISTS (
                    SELECT 1 FROM channel_subscriptions cs
                    WHERE cs.user_id = u.id
                    AND cs.channel_id = c.id
                    AND cs.is_subscribed = true
                )
            )
            AND EXISTS (
                SELECT 1 FROM channels c
                WHERE c.is_required = true AND c.is_active = true
            )
        """

        result = await db.execute(query)
        eligible_users = result.fetchall()

        if not eligible_users:
            return {"message": "No users found who have followed all required channels"}

        # Reset trial status for eligible users
        user_ids = [user.id for user in eligible_users]
        users = (await db.execute(select(User).filter(User.id.in_(user_ids)))).scalars().all()

        for user in users:
            user.has_used_trial = False
            user.trial_count = 0
            user.last_trial_at = None

        await db.commit()

        return {
            "message": f"Trials reset for {len(users)} users who followed all required channels",
            "affected_users": len(users),
            "user_details": [
                {
                    "id": user.id,
                    "telegram_id": user.telegram_id,
                    "username": user.username,
                    "first_name": user.first_name
                } for user in eligible_users
            ]
        }
    except Exception as e:
        logger.error(f"Error resetting trials for channel followers: {e}")
        raise HTTPException(status_code=500, detail="Failed to reset trials for channel followers")

@app.get("/api/users/channel-followers-stats")
async def get_channel_followers_stats(db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    """Get statistics about users who have followed all required channels"""
    try:
        # Get total required channels
        total_required = await db.execute(
            "SELECT COUNT(*) FROM channels WHERE is_required = true AND is_active = true"
        )
        total_required_count = total_required.scalar()

        if total_required_count == 0:
            return {
                "total_required_channels": 0,
                "users_followed_all": 0,
                "users_with_trial_followed_all": 0,
                "users_without_trial_followed_all": 0
            }

        # Get users who followed all required channels
        query = """
            SELECT
                COUNT(*) as total_followers,
                SUM(CASE WHEN u.has_used_trial = true THEN 1 ELSE 0 END) as followers_with_trial,
                SUM(CASE WHEN u.has_used_trial = false THEN 1 ELSE 0 END) as followers_without_trial
            FROM users u
            WHERE NOT EXISTS (
                SELECT 1 FROM channels c
                WHERE c.is_required = true AND c.is_active = true
                AND NOT EXISTS (
                    SELECT 1 FROM channel_subscriptions cs
                    WHERE cs.user_id = u.id
                    AND cs.channel_id = c.id
                    AND cs.is_subscribed = true
                )
            )
        """

        result = await db.execute(query)
        stats = result.fetchone()

        return {
            "total_required_channels": total_required_count,
            "users_followed_all": stats.total_followers or 0,
            "users_with_trial_followed_all": stats.followers_with_trial or 0,
            "users_without_trial_followed_all": stats.followers_without_trial or 0
        }
    except Exception as e:
        logger.error(f"Error getting channel followers stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get channel followers stats")

@app.get("/api/admin/system-stats")
async def get_system_stats(db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    """Get comprehensive system statistics for admin dashboard"""
    try:
        from sqlalchemy import func, text

        # User statistics
        total_users = await db.execute(select(func.count(User.id)))
        total_users_count = total_users.scalar()

        active_users = await db.execute(select(func.count(User.id)).filter(User.is_active == True))
        active_users_count = active_users.scalar()

        users_with_trial = await db.execute(select(func.count(User.id)).filter(User.has_used_trial == True))
        users_with_trial_count = users_with_trial.scalar()

        # VPN Account statistics
        total_vpn_accounts = await db.execute(select(func.count(VPNAccount.id)))
        total_vpn_accounts_count = total_vpn_accounts.scalar()

        active_vpn_accounts = await db.execute(
            select(func.count(VPNAccount.id)).filter(
                VPNAccount.is_active == True,
                VPNAccount.expire_date > func.now()
            )
        )
        active_vpn_accounts_count = active_vpn_accounts.scalar()

        trial_accounts = await db.execute(select(func.count(VPNAccount.id)).filter(VPNAccount.is_trial == True))
        trial_accounts_count = trial_accounts.scalar()

        # Channel statistics
        total_channels = await db.execute(select(func.count(Channel.id)))
        total_channels_count = total_channels.scalar()

        required_channels = await db.execute(
            select(func.count(Channel.id)).filter(Channel.is_required == True, Channel.is_active == True)
        )
        required_channels_count = required_channels.scalar()

        advertising_channels = await db.execute(
            select(func.count(Channel.id)).filter(Channel.advertising_enabled == True, Channel.is_active == True)
        )
        advertising_channels_count = advertising_channels.scalar()

        # Panel statistics
        total_panels = await db.execute(select(func.count(VPNPanel.id)))
        total_panels_count = total_panels.scalar()

        active_panels = await db.execute(select(func.count(VPNPanel.id)).filter(VPNPanel.is_active == True))
        active_panels_count = active_panels.scalar()

        # Premium plan statistics
        total_plans = await db.execute(select(func.count(PremiumPlan.id)))
        total_plans_count = total_plans.scalar()

        active_plans = await db.execute(select(func.count(PremiumPlan.id)).filter(PremiumPlan.is_active == True))
        active_plans_count = active_plans.scalar()

        return {
            "users": {
                "total": total_users_count or 0,
                "active": active_users_count or 0,
                "with_trial": users_with_trial_count or 0,
                "without_trial": (total_users_count or 0) - (users_with_trial_count or 0)
            },
            "vpn_accounts": {
                "total": total_vpn_accounts_count or 0,
                "active": active_vpn_accounts_count or 0,
                "trial": trial_accounts_count or 0,
                "premium": (total_vpn_accounts_count or 0) - (trial_accounts_count or 0)
            },
            "channels": {
                "total": total_channels_count or 0,
                "required": required_channels_count or 0,
                "advertising": advertising_channels_count or 0
            },
            "panels": {
                "total": total_panels_count or 0,
                "active": active_panels_count or 0
            },
            "plans": {
                "total": total_plans_count or 0,
                "active": active_plans_count or 0
            }
        }
    except Exception as e:
        logger.error(f"Error getting system stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get system statistics")

@app.post("/api/admin/cleanup-expired-accounts")
async def cleanup_expired_accounts(db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    """Clean up expired VPN accounts"""
    try:
        from sqlalchemy import func

        # Find expired accounts
        expired_accounts = await db.execute(
            select(VPNAccount).filter(
                VPNAccount.expire_date < func.now(),
                VPNAccount.is_active == True
            )
        )
        expired_accounts_list = expired_accounts.scalars().all()

        # Deactivate expired accounts
        for account in expired_accounts_list:
            account.is_active = False
            account.status = 'expired'

        await db.commit()

        return {
            "message": f"Cleaned up {len(expired_accounts_list)} expired accounts",
            "cleaned_accounts": len(expired_accounts_list)
        }
    except Exception as e:
        logger.error(f"Error cleaning up expired accounts: {e}")
        raise HTTPException(status_code=500, detail="Failed to cleanup expired accounts")

@app.get("/api/admin/recent-activity")
async def get_recent_activity(
    limit: int = 50,
    db: AsyncSession = Depends(get_db),
    username: str = Depends(authenticate_admin)
):
    """Get recent system activity"""
    try:
        # Get recent user registrations
        recent_users = await db.execute(
            select(User.telegram_id, User.username, User.first_name, User.created_at)
            .order_by(User.created_at.desc())
            .limit(limit)
        )
        recent_users_list = [dict(user) for user in recent_users.fetchall()]

        # Get recent VPN account creations
        recent_accounts = await db.execute(
            select(VPNAccount.username, VPNAccount.is_trial, VPNAccount.created_at)
            .order_by(VPNAccount.created_at.desc())
            .limit(limit)
        )
        recent_accounts_list = [dict(account) for account in recent_accounts.fetchall()]

        return {
            "recent_users": recent_users_list,
            "recent_accounts": recent_accounts_list
        }
    except Exception as e:
        logger.error(f"Error getting recent activity: {e}")
        raise HTTPException(status_code=500, detail="Failed to get recent activity")

@app.post("/api/admin/retry-failed-payments")
async def retry_failed_payments(db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    """Manually retry failed payments"""
    try:
        from bot.services.payment_service import payment_service

        result = await payment_service.retry_failed_payments()

        return {
            "message": "Failed payment retry completed",
            "results": result
        }
    except Exception as e:
        logger.error(f"Error retrying failed payments: {e}")
        raise HTTPException(status_code=500, detail="Failed to retry failed payments")

@app.get("/api/admin/failed-payments-stats")
async def get_failed_payments_stats(db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    """Get statistics about failed payments"""
    try:
        from sqlalchemy import func, text

        # Get failed payments statistics
        total_failed = await db.execute(text("SELECT COUNT(*) FROM failed_payments"))
        total_failed_count = total_failed.scalar()

        recent_failed = await db.execute(
            text("SELECT COUNT(*) FROM failed_payments WHERE created_at > NOW() - INTERVAL 24 HOUR")
        )
        recent_failed_count = recent_failed.scalar()

        high_retry = await db.execute(
            text("SELECT COUNT(*) FROM failed_payments WHERE retry_count >= 3")
        )
        high_retry_count = high_retry.scalar()

        return {
            "total_failed_payments": total_failed_count or 0,
            "recent_failed_payments": recent_failed_count or 0,
            "high_retry_count": high_retry_count or 0
        }
    except Exception as e:
        logger.error(f"Error getting failed payments stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get failed payments statistics")

@app.post("/api/admin/verify-pending-payments")
async def verify_pending_payments(db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    """Manually verify all pending payments"""
    try:
        from bot.services.payment_verification_service import payment_verification_service

        result = await payment_verification_service.verify_pending_payments()

        return {
            "message": "Payment verification completed",
            "results": result
        }
    except Exception as e:
        logger.error(f"Error verifying pending payments: {e}")
        raise HTTPException(status_code=500, detail="Failed to verify pending payments")

@app.post("/api/admin/cleanup-expired-payments")
async def cleanup_expired_payments_admin(db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    """Clean up expired payments"""
    try:
        from bot.services.payment_verification_service import payment_verification_service

        result = await payment_verification_service.cleanup_expired_payments()

        return {
            "message": "Payment cleanup completed",
            "results": result
        }
    except Exception as e:
        logger.error(f"Error cleaning up expired payments: {e}")
        raise HTTPException(status_code=500, detail="Failed to cleanup expired payments")

@app.get("/api/admin/payment-stats")
async def get_payment_stats(db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    """Get comprehensive payment statistics"""
    try:
        from sqlalchemy import func, text

        # Get payment statistics
        total_payments = await db.execute(text("SELECT COUNT(*) FROM payments"))
        total_payments_count = total_payments.scalar()

        successful_payments = await db.execute(
            text("SELECT COUNT(*) FROM payments WHERE created_at > NOW() - INTERVAL 30 DAY")
        )
        successful_payments_count = successful_payments.scalar()

        # Crypto payments
        pending_crypto = await db.execute(
            text("SELECT COUNT(*) FROM crypto_payments WHERE status IN ('waiting', 'confirming')")
        )
        pending_crypto_count = pending_crypto.scalar()

        # TON payments
        pending_ton = await db.execute(
            text("SELECT COUNT(*) FROM ton_payments WHERE status IN ('waiting', 'confirming')")
        )
        pending_ton_count = pending_ton.scalar()

        return {
            "total_payments": total_payments_count or 0,
            "successful_payments_30d": successful_payments_count or 0,
            "pending_crypto_payments": pending_crypto_count or 0,
            "pending_ton_payments": pending_ton_count or 0
        }
    except Exception as e:
        logger.error(f"Error getting payment stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get payment statistics")

@app.get("/api/admin/referral-stats")
async def get_referral_stats(db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    """Get comprehensive referral statistics"""
    try:
        from sqlalchemy import func, text

        # Get referral statistics
        total_referrals = await db.execute(text("SELECT COUNT(*) FROM referrals"))
        total_referrals_count = total_referrals.scalar()

        completed_referrals = await db.execute(
            text("SELECT COUNT(*) FROM referrals WHERE status = 'completed'")
        )
        completed_referrals_count = completed_referrals.scalar()

        pending_referrals = await db.execute(
            text("SELECT COUNT(*) FROM referrals WHERE status = 'pending'")
        )
        pending_referrals_count = pending_referrals.scalar()

        # Get top referrers
        top_referrers = await db.execute(
            text("""
                SELECT u.telegram_id, u.username, u.first_name, u.referral_count, u.total_referral_rewards
                FROM users u
                WHERE u.referral_count > 0
                ORDER BY u.referral_count DESC
                LIMIT 10
            """)
        )
        top_referrers_list = [dict(row) for row in top_referrers.fetchall()]

        # Get recent referrals
        recent_referrals = await db.execute(
            text("""
                SELECT r.*,
                       u1.username as referrer_username, u1.first_name as referrer_name,
                       u2.username as referred_username, u2.first_name as referred_name
                FROM referrals r
                JOIN users u1 ON r.referrer_id = u1.id
                JOIN users u2 ON r.referred_id = u2.id
                ORDER BY r.created_at DESC
                LIMIT 20
            """)
        )
        recent_referrals_list = [dict(row) for row in recent_referrals.fetchall()]

        return {
            "total_referrals": total_referrals_count or 0,
            "completed_referrals": completed_referrals_count or 0,
            "pending_referrals": pending_referrals_count or 0,
            "top_referrers": top_referrers_list,
            "recent_referrals": recent_referrals_list
        }
    except Exception as e:
        logger.error(f"Error getting referral stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get referral statistics")

@app.post("/api/admin/complete-referral/{referral_id}")
async def complete_referral_admin(
    referral_id: int,
    request: Request,
    db: AsyncSession = Depends(get_db),
    username: str = Depends(authenticate_admin)
):
    """Manually complete a referral and give rewards"""
    try:
        data = await request.json()
        reward_type = data.get('reward_type', 'data')
        reward_amount = data.get('reward_amount', 1073741824)  # 1GB default

        from bot.services.referral_service import referral_service

        success = await referral_service.complete_referral(referral_id, reward_type, reward_amount)

        if success:
            return {"message": "Referral completed successfully"}
        else:
            raise HTTPException(status_code=400, detail="Failed to complete referral")
    except Exception as e:
        logger.error(f"Error completing referral: {e}")
        raise HTTPException(status_code=500, detail="Failed to complete referral")

@app.get("/api/admin/user-referrals/{user_id}")
async def get_user_referrals(
    user_id: int,
    db: AsyncSession = Depends(get_db),
    username: str = Depends(authenticate_admin)
):
    """Get referral information for a specific user"""
    try:
        from bot.services.referral_service import referral_service

        stats = await referral_service.get_user_referral_stats(user_id)

        return {
            "user_id": user_id,
            "referral_stats": stats
        }
    except Exception as e:
        logger.error(f"Error getting user referrals: {e}")
        raise HTTPException(status_code=500, detail="Failed to get user referrals")

@app.get("/api/admin/referral-analytics")
async def get_referral_analytics(
    days: int = 30,
    db: AsyncSession = Depends(get_db),
    username: str = Depends(authenticate_admin)
):
    """Get comprehensive referral analytics"""
    try:
        from bot.services.referral_analytics_service import referral_analytics_service

        analytics = await referral_analytics_service.get_comprehensive_analytics(days)

        return {
            "analytics": analytics
        }
    except Exception as e:
        logger.error(f"Error getting referral analytics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get referral analytics")

@app.get("/api/admin/referral-funnel")
async def get_referral_funnel(db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    """Get referral funnel analysis"""
    try:
        from bot.services.referral_analytics_service import referral_analytics_service

        funnel = await referral_analytics_service.get_referral_funnel_analysis()

        return {
            "funnel_analysis": funnel
        }
    except Exception as e:
        logger.error(f"Error getting referral funnel: {e}")
        raise HTTPException(status_code=500, detail="Failed to get referral funnel analysis")

@app.get("/api/admin/referral-leaderboard")
async def get_referral_leaderboard(
    days: int = 30,
    limit: int = 50,
    db: AsyncSession = Depends(get_db),
    username: str = Depends(authenticate_admin)
):
    """Get referral leaderboard"""
    try:
        from bot.services.referral_analytics_service import referral_analytics_service

        leaderboard = await referral_analytics_service.get_referral_leaderboard(days, limit)

        return {
            "leaderboard": leaderboard,
            "period_days": days
        }
    except Exception as e:
        logger.error(f"Error getting referral leaderboard: {e}")
        raise HTTPException(status_code=500, detail="Failed to get referral leaderboard")

@app.get("/api/admin/user-referral-performance/{user_id}")
async def get_user_referral_performance(
    user_id: int,
    db: AsyncSession = Depends(get_db),
    username: str = Depends(authenticate_admin)
):
    """Get detailed referral performance for a specific user"""
    try:
        from bot.services.referral_analytics_service import referral_analytics_service

        performance = await referral_analytics_service.get_user_referral_performance(user_id)

        return {
            "user_performance": performance
        }
    except Exception as e:
        logger.error(f"Error getting user referral performance: {e}")
        raise HTTPException(status_code=500, detail="Failed to get user referral performance")

@app.get("/api/admin/referral-rewards-available")
async def get_available_referral_rewards(db: AsyncSession = Depends(get_db), username: str = Depends(authenticate_admin)):
    """Get available referral reward types and amounts"""
    try:
        from bot.services.referral_service import referral_service

        rewards = await referral_service.get_available_rewards()

        return {
            "available_rewards": rewards
        }
    except Exception as e:
        logger.error(f"Error getting available rewards: {e}")
        raise HTTPException(status_code=500, detail="Failed to get available rewards")

# Debug endpoint to list all routes
@app.get("/debug/routes")
async def debug_routes():
    routes = []
    for route in app.routes:
        if hasattr(route, 'methods') and hasattr(route, 'path'):
            routes.append({
                "path": route.path,
                "methods": list(route.methods),
                "name": getattr(route, 'name', 'N/A')
            })
    return {"routes": routes}