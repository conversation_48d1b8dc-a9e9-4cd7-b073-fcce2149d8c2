"""Authentication middleware for the Telegram bot."""

import logging
from typing import Callable, Any, Awaitable
from telegram import Update
from telegram.ext import ContextTypes
from bot.database import get_db_connection
from bot.models import User

logger = logging.getLogger(__name__)


class AuthMiddleware:
    """Middleware for user authentication and registration."""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def __call__(
        self,
        update: Update,
        context: ContextTypes.DEFAULT_TYPE,
        next_handler: Callable[[Update, ContextTypes.DEFAULT_TYPE], Awaitable[Any]]
    ) -> Any:
        """Process authentication middleware."""
        if not update.effective_user:
            return await next_handler(update, context)
        
        user_id = update.effective_user.id
        username = update.effective_user.username
        first_name = update.effective_user.first_name
        last_name = update.effective_user.last_name
        language_code = update.effective_user.language_code
        
        try:
            # Get or create user
            async with get_db_connection() as conn:
                # Check if user exists
                user = await conn.fetchrow(
                    "SELECT * FROM users WHERE telegram_id = $1", user_id
                )
                
                if not user:
                    # Create new user without language (will be set after language selection)
                    await conn.execute(
                        """
                        INSERT INTO users (telegram_id, username, first_name, last_name, language_code, created_at)
                        VALUES ($1, $2, $3, $4, $5, NOW())
                        """,
                        user_id, username, first_name, last_name, None
                    )
                    self.logger.info(f"New user registered: {user_id} (@{username}) - language selection pending")
                    
                    # Mark user as needing language selection
                    context.user_data['needs_language_selection'] = True
                else:
                    # Update user info if changed
                    if (user['username'] != username or 
                        user['first_name'] != first_name or 
                        user['last_name'] != last_name):
                        await conn.execute(
                            """
                            UPDATE users 
                            SET username = $2, first_name = $3, last_name = $4, updated_at = NOW()
                            WHERE telegram_id = $1
                            """,
                            user_id, username, first_name, last_name
                        )
                        self.logger.info(f"User info updated: {user_id} (@{username})")
                
                # Get updated user data to load language
                user = await conn.fetchrow(
                    "SELECT * FROM users WHERE telegram_id = $1", user_id
                )
                
                # Store user in context for handlers (both formats for compatibility)
                context.user_data['user_id'] = user_id
                context.user_data['username'] = username
                context.user_data['language_code'] = user['language_code'] if user and user['language_code'] else 'en'

                # Store user data in nested format for handlers that expect it
                context.user_data['user'] = {
                    'id': user_id,
                    'telegram_id': user_id,
                    'username': username,
                    'first_name': user.get('first_name', '') if user else '',
                    'last_name': user.get('last_name', '') if user else '',
                    'language_code': user['language_code'] if user and user['language_code'] else 'en',
                    'is_premium': user.get('is_premium', False) if user else False,
                    'is_active': user.get('is_active', True) if user else True
                }
                
                # Check if user needs language selection
                if user and not user['language_code']:
                    context.user_data['needs_language_selection'] = True
                
        except Exception as e:
            self.logger.error(f"Auth middleware error for user {user_id}: {e}")
            # Continue to handler even if auth fails
        
        return await next_handler(update, context)


    async def set_user_language(self, user_id: int, language_code: str) -> bool:
        """
        Set user's language preference in the database.
        """
        try:
            async with get_db_connection() as conn:
                await conn.execute(
                    "UPDATE users SET language_code = $1, updated_at = NOW() WHERE telegram_id = $2",
                    language_code, user_id
                )
                self.logger.info(f"Language set to {language_code} for user {user_id}")
                return True
        except Exception as e:
            self.logger.error(f"Error setting language for user {user_id}: {e}")
            return False


# Global instance
auth_middleware = AuthMiddleware()