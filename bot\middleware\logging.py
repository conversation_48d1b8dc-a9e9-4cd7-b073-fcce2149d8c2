"""Logging middleware for the Telegram bot."""

import logging
import time
from typing import Callable, Any, Awaitable
from telegram import Update
from telegram.ext import ContextTypes
from bot.database import get_db_connection

logger = logging.getLogger(__name__)


class LoggingMiddleware:
    """Middleware for logging user interactions."""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def __call__(
        self,
        update: Update,
        context: ContextTypes.DEFAULT_TYPE,
        next_handler: Callable[[Update, ContextTypes.DEFAULT_TYPE], Awaitable[Any]]
    ) -> Any:
        """Process logging middleware."""
        start_time = time.time()
        
        # Log incoming update
        user_id = update.effective_user.id if update.effective_user else None
        username = update.effective_user.username if update.effective_user else None
        
        # Determine update type and content
        update_type = "unknown"
        content = ""
        
        if update.message:
            update_type = "message"
            if update.message.text:
                content = update.message.text[:100]  # Truncate long messages
            elif update.message.photo:
                content = "[photo]"
            elif update.message.document:
                content = "[document]"
            elif update.message.voice:
                content = "[voice]"
        elif update.callback_query:
            update_type = "callback_query"
            content = update.callback_query.data or ""
        elif update.inline_query:
            update_type = "inline_query"
            content = update.inline_query.query or ""
        elif update.chosen_inline_result:
            update_type = "chosen_inline_result"
            content = update.chosen_inline_result.result_id or ""
        elif update.pre_checkout_query:
            update_type = "pre_checkout_query"
            content = f"payload: {update.pre_checkout_query.invoice_payload}"
        elif update.successful_payment:
            update_type = "successful_payment"
            content = f"amount: {update.successful_payment.total_amount}"
        elif update.chat_member:
            update_type = "chat_member"
            content = f"status: {update.chat_member.new_chat_member.status}"
        
        self.logger.info(
            f"Incoming {update_type} from user {user_id} (@{username}): {content}"
        )
        
        try:
            # Execute the handler
            result = await next_handler(update, context)
            
            # Log execution time
            execution_time = time.time() - start_time
            self.logger.info(
                f"Handler executed in {execution_time:.3f}s for user {user_id}"
            )
            
            # Log to database for analytics
            await self._log_to_database(
                user_id, update_type, content, execution_time, success=True
            )
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(
                f"Handler failed in {execution_time:.3f}s for user {user_id}: {e}"
            )
            
            # Log error to database
            await self._log_to_database(
                user_id, update_type, content, execution_time, 
                success=False, error=str(e)
            )
            
            raise
    
    async def _log_to_database(
        self, 
        user_id: int, 
        update_type: str, 
        content: str, 
        execution_time: float,
        success: bool = True,
        error: str = None
    ) -> None:
        """Log interaction to database for analytics."""
        try:
            async with get_db_connection() as conn:
                await conn.execute(
                    """
                    INSERT INTO user_interactions 
                    (user_id, update_type, content, execution_time, success, error, created_at)
                    VALUES ($1, $2, $3, $4, $5, $6, NOW())
                    """,
                    user_id, update_type, content[:500], execution_time, success, error
                )
        except Exception as e:
            self.logger.error(f"Failed to log interaction to database: {e}")


class ErrorLoggingMiddleware:
    """Middleware for comprehensive error logging."""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def __call__(
        self,
        update: Update,
        context: ContextTypes.DEFAULT_TYPE,
        next_handler: Callable[[Update, ContextTypes.DEFAULT_TYPE], Awaitable[Any]]
    ) -> Any:
        """Process error logging middleware."""
        try:
            return await next_handler(update, context)
        except Exception as e:
            user_id = update.effective_user.id if update.effective_user else None
            username = update.effective_user.username if update.effective_user else None
            
            # Log detailed error information
            self.logger.error(
                f"Unhandled error for user {user_id} (@{username}): {e}",
                exc_info=True
            )
            
            # Send user-friendly error message
            try:
                if update.message:
                    await update.message.reply_text(
                        "❌ Sorry, something went wrong. Please try again later or contact support.",
                        parse_mode='HTML'
                    )
                elif update.callback_query:
                    await update.callback_query.answer(
                        "❌ Something went wrong. Please try again.",
                        show_alert=True
                    )
            except Exception as send_error:
                self.logger.error(f"Failed to send error message: {send_error}")
            
            # Re-raise the original exception
            raise


# Global instances
logging_middleware = LoggingMiddleware()
error_logging_middleware = ErrorLoggingMiddleware()