<?php

declare(strict_types=1);

namespace VpnBot\Models;

use VpnBot\Database\Connection;
use PDO;

class Payment
{
    public ?int $id = null;
    public int $user_id;
    public ?int $plan_id = null;
    public string $payment_id;
    public float $amount;
    public string $currency = 'USD';
    public string $payment_method;
    public string $status = 'pending';
    public ?string $provider_payment_id = null;
    public ?array $payment_data = null;
    public ?string $paid_at = null;
    public ?string $created_at = null;
    public ?string $updated_at = null;

    public static function findByPaymentId(string $paymentId): ?Payment
    {
        $pdo = Connection::getInstance();
        $stmt = $pdo->prepare('SELECT * FROM payments WHERE payment_id = ?');
        $stmt->execute([$paymentId]);
        
        $data = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!$data) {
            return null;
        }

        return self::fromArray($data);
    }

    public static function findById(int $id): ?Payment
    {
        $pdo = Connection::getInstance();
        $stmt = $pdo->prepare('SELECT * FROM payments WHERE id = ?');
        $stmt->execute([$id]);
        
        $data = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!$data) {
            return null;
        }

        return self::fromArray($data);
    }

    public static function findByUserId(int $userId): array
    {
        $pdo = Connection::getInstance();
        $stmt = $pdo->prepare('SELECT * FROM payments WHERE user_id = ? ORDER BY created_at DESC');
        $stmt->execute([$userId]);
        
        $payments = [];
        while ($data = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $payments[] = self::fromArray($data);
        }

        return $payments;
    }

    public function save(): bool
    {
        $pdo = Connection::getInstance();
        
        if ($this->id === null) {
            return $this->insert($pdo);
        } else {
            return $this->update($pdo);
        }
    }

    private function insert(PDO $pdo): bool
    {
        $sql = '
            INSERT INTO payments (
                user_id, plan_id, payment_id, amount, currency,
                payment_method, status, provider_payment_id,
                payment_data, paid_at, created_at, updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW()
            )
        ';

        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            $this->user_id,
            $this->plan_id,
            $this->payment_id,
            $this->amount,
            $this->currency,
            $this->payment_method,
            $this->status,
            $this->provider_payment_id,
            $this->payment_data ? json_encode($this->payment_data) : null,
            $this->paid_at,
        ]);

        if ($result) {
            $this->id = (int)$pdo->lastInsertId();
        }

        return $result;
    }

    private function update(PDO $pdo): bool
    {
        $sql = '
            UPDATE payments SET
                plan_id = ?, amount = ?, currency = ?, payment_method = ?,
                status = ?, provider_payment_id = ?, payment_data = ?,
                paid_at = ?, updated_at = NOW()
            WHERE id = ?
        ';

        $stmt = $pdo->prepare($sql);
        return $stmt->execute([
            $this->plan_id,
            $this->amount,
            $this->currency,
            $this->payment_method,
            $this->status,
            $this->provider_payment_id,
            $this->payment_data ? json_encode($this->payment_data) : null,
            $this->paid_at,
            $this->id,
        ]);
    }

    public static function fromArray(array $data): Payment
    {
        $payment = new Payment();
        $payment->id = $data['id'] ?? null;
        $payment->user_id = (int)$data['user_id'];
        $payment->plan_id = $data['plan_id'] ? (int)$data['plan_id'] : null;
        $payment->payment_id = $data['payment_id'];
        $payment->amount = (float)$data['amount'];
        $payment->currency = $data['currency'];
        $payment->payment_method = $data['payment_method'];
        $payment->status = $data['status'];
        $payment->provider_payment_id = $data['provider_payment_id'];
        $payment->payment_data = $data['payment_data'] ? json_decode($data['payment_data'], true) : null;
        $payment->paid_at = $data['paid_at'];
        $payment->created_at = $data['created_at'];
        $payment->updated_at = $data['updated_at'];

        return $payment;
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'plan_id' => $this->plan_id,
            'payment_id' => $this->payment_id,
            'amount' => $this->amount,
            'currency' => $this->currency,
            'payment_method' => $this->payment_method,
            'status' => $this->status,
            'provider_payment_id' => $this->provider_payment_id,
            'payment_data' => $this->payment_data,
            'paid_at' => $this->paid_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }

    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }
}
