#!/bin/bash

# Docker Rebuild Script for VPN Bot
# This script stops, rebuilds, and restarts the VPN bot with all latest fixes

echo "🐳 VPN Bot Docker Rebuild Script"
echo "================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

print_status "Docker is running. Proceeding with rebuild..."

# Step 1: Stop current containers
print_status "Step 1: Stopping current containers..."
if docker-compose ps -q | grep -q .; then
    docker-compose down
    if [ $? -eq 0 ]; then
        print_success "Containers stopped successfully"
    else
        print_warning "Some containers may not have stopped cleanly"
    fi
else
    print_status "No running containers found"
fi

# Step 2: Remove old images (optional, uncomment if needed)
# print_status "Step 2: Removing old images..."
# docker-compose down --rmi all

# Step 3: Build with no cache to ensure all changes are applied
print_status "Step 2: Building containers with latest changes (no cache)..."
docker-compose build --no-cache
if [ $? -eq 0 ]; then
    print_success "Build completed successfully"
else
    print_error "Build failed. Please check the output above for errors."
    exit 1
fi

# Step 4: Start containers
print_status "Step 3: Starting containers..."
docker-compose up -d
if [ $? -eq 0 ]; then
    print_success "Containers started successfully"
else
    print_error "Failed to start containers. Please check the logs."
    exit 1
fi

# Step 5: Wait a moment for containers to initialize
print_status "Step 4: Waiting for containers to initialize..."
sleep 10

# Step 6: Check container status
print_status "Step 5: Checking container status..."
docker-compose ps

# Step 7: Show logs
print_status "Step 6: Showing recent logs..."
echo ""
print_status "Bot logs (last 20 lines):"
docker-compose logs --tail=20 bot

echo ""
print_status "Database logs (last 10 lines):"
docker-compose logs --tail=10 postgres

# Step 8: Health check
print_status "Step 7: Performing health check..."
sleep 5

# Check if bot container is running
if docker-compose ps bot | grep -q "Up"; then
    print_success "✅ Bot container is running"
else
    print_error "❌ Bot container is not running properly"
    print_status "Showing bot logs for debugging:"
    docker-compose logs bot
    exit 1
fi

# Check if database container is running
if docker-compose ps postgres | grep -q "Up"; then
    print_success "✅ Database container is running"
else
    print_error "❌ Database container is not running properly"
    print_status "Showing database logs for debugging:"
    docker-compose logs postgres
    exit 1
fi

# Final success message
echo ""
echo "🎉 Docker rebuild completed successfully!"
echo "================================="
print_success "All containers are running properly"
print_status "You can now test your bot by sending /start on Telegram"
print_status "To view live logs, run: docker-compose logs -f bot"
print_status "To check status anytime, run: docker-compose ps"

# Optional: Show how to test
echo ""
print_status "Testing recommendations:"
echo "1. Send /start to your bot on Telegram"
echo "2. Try /help to see all commands"
echo "3. Test /trial for trial VPN functionality"
echo "4. Test /premium for payment integration"
echo "5. Test /referral for referral system"

echo ""
print_status "If you encounter any issues:"
echo "1. Check logs: docker-compose logs bot"
echo "2. Restart containers: docker-compose restart"
echo "3. Full rebuild: ./docker_rebuild.sh"

echo ""
print_success "VPN Bot is ready for production! 🚀"
