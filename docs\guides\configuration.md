# Configuration Guide

This guide covers all configuration options for the VPN Telegram Bot project.

## Table of Contents

1. [Environment Variables](#environment-variables)
2. [Bot Configuration](#bot-configuration)
3. [Database Configuration](#database-configuration)
4. [Redis Configuration](#redis-configuration)
5. [VPN Service Configuration](#vpn-service-configuration)
6. [Payment Configuration](#payment-configuration)
7. [Security Configuration](#security-configuration)
8. [Logging Configuration](#logging-configuration)
9. [Performance Configuration](#performance-configuration)
10. [Advanced Configuration](#advanced-configuration)

## Environment Variables

### Required Variables

Create a `.env` file in the project root with the following variables:

```bash
# Bot Configuration
BOT_TOKEN=your_telegram_bot_token_here
BOT_USERNAME=your_bot_username
WEBHOOK_URL=https://your-domain.com/webhook
WEBHOOK_SECRET=your_webhook_secret_token

# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/vpn_bot
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_password
POSTGRES_DB=vpn_bot
POSTGRES_HOST=postgres
POSTGRES_PORT=5432

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0

# VPN Service (Marzban) Configuration
MARZBAN_URL=https://your-marzban-panel.com
MARZBAN_USERNAME=admin
MARZBAN_PASSWORD=your_marzban_password
MARZBAN_TOKEN=your_marzban_api_token

# Payment Configuration
PAYMENT_PROVIDER_TOKEN=your_payment_provider_token
PAYMENT_CURRENCY=USD
PAYMENT_WEBHOOK_SECRET=your_payment_webhook_secret

# Cryptocurrency Payment Configuration
NOWPAYMENTS_API_KEY=your_nowpayments_api_key
NOWPAYMENTS_IPN_SECRET=your_nowpayments_ipn_secret
NOWPAYMENTS_SANDBOX=false

# TON Payment Configuration
TON_MASTER_WALLET=your_ton_master_wallet_address
TON_MASTER_PRIVATE_KEY=your_ton_master_private_key
TON_NETWORK=mainnet
TON_API_KEY=your_ton_api_key
TON_PAYMENT_TIMEOUT_MINUTES=30

# Channel Configuration
CHANNEL_ID=-1001234567890
CHANNEL_USERNAME=@your_channel
CHANNEL_INVITE_LINK=https://t.me/your_channel

# Admin Configuration
ADMIN_USER_IDS=123456789,987654321
SUPPORT_USERNAME=@support_username

# Security Configuration
SECRET_KEY=your_secret_key_for_jwt_and_encryption
ENCRYPTION_KEY=your_32_byte_encryption_key
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRY=3600

# Application Configuration
DEBUG=false
ENVIRONMENT=production
LOG_LEVEL=INFO
TIMEZONE=UTC
```

### Optional Variables

```bash
# Performance Configuration
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30
REDIS_POOL_SIZE=10
WORKER_PROCESSES=4
MAX_CONCURRENT_REQUESTS=100

# Rate Limiting
RATE_LIMIT_REQUESTS=30
RATE_LIMIT_WINDOW=60
RATE_LIMIT_STORAGE=redis

# Caching
CACHE_TTL=3600
CACHE_MAX_SIZE=1000
CACHE_BACKEND=redis

# Monitoring
METRICS_ENABLED=true
METRICS_PORT=8080
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_PORT=8081

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket
BACKUP_S3_ACCESS_KEY=your-s3-access-key
BACKUP_S3_SECRET_KEY=your-s3-secret-key

# Notification Configuration
NOTIFICATION_WEBHOOK=https://your-notification-webhook.com
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/...
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/...

# Feature Flags
TRIAL_ACCOUNTS_ENABLED=true
PAYMENTS_ENABLED=true
REFERRAL_SYSTEM_ENABLED=true
ANALYTICS_ENABLED=true
```

## Bot Configuration

### Basic Bot Settings

```python
# bot/config.py
class BotConfig:
    # Bot Identity
    TOKEN = os.getenv('BOT_TOKEN')
    USERNAME = os.getenv('BOT_USERNAME')
    
    # Webhook Configuration
    WEBHOOK_URL = os.getenv('WEBHOOK_URL')
    WEBHOOK_SECRET = os.getenv('WEBHOOK_SECRET')
    WEBHOOK_PATH = '/webhook'
    
    # Server Configuration
    HOST = os.getenv('HOST', '0.0.0.0')
    PORT = int(os.getenv('PORT', 8000))
    
    # Request Configuration
    REQUEST_TIMEOUT = int(os.getenv('REQUEST_TIMEOUT', 30))
    CONNECTION_POOL_SIZE = int(os.getenv('CONNECTION_POOL_SIZE', 256))
    
    # Update Processing
    MAX_UPDATES_IN_QUEUE = int(os.getenv('MAX_UPDATES_IN_QUEUE', 100))
    UPDATE_QUEUE_TIMEOUT = int(os.getenv('UPDATE_QUEUE_TIMEOUT', 10))
```

### Command Configuration

```python
# bot/config.py
class CommandConfig:
    # Command Prefixes
    COMMAND_PREFIXES = ['/', '!']
    
    # Command Aliases
    COMMAND_ALIASES = {
        'start': ['begin', 'init'],
        'help': ['h', 'info'],
        'trial': ['test', 'demo'],
        'premium': ['pro', 'paid'],
        'account': ['acc', 'profile'],
        'support': ['contact', 'help_desk']
    }
    
    # Command Permissions
    ADMIN_COMMANDS = [
        'admin', 'stats', 'broadcast', 'ban', 'unban',
        'add_admin', 'remove_admin', 'system_info'
    ]
    
    # Command Rate Limits (requests per minute)
    COMMAND_RATE_LIMITS = {
        'trial': 1,
        'premium': 5,
        'account': 10,
        'help': 20,
        'start': 30
    }
```

### Message Configuration

```python
# bot/config.py
class MessageConfig:
    # Message Limits
    MAX_MESSAGE_LENGTH = 4096
    MAX_CAPTION_LENGTH = 1024
    
    # Message Formatting
    PARSE_MODE = 'MarkdownV2'
    DISABLE_WEB_PAGE_PREVIEW = True
    
    # Message Templates
    WELCOME_MESSAGE = """
🎉 *Welcome to VPN Bot!*

Get secure and fast VPN access:
• 🆓 Free trial account
• 💎 Premium plans available
• 🔒 Secure and private
• ⚡ High-speed servers

Use /trial to get started!
    """
    
    ERROR_MESSAGE = """
❌ *Something went wrong*

Please try again later or contact support: {support_username}
    """
    
    MAINTENANCE_MESSAGE = """
🔧 *Maintenance Mode*

The bot is currently under maintenance.
Please try again later.
    """
```

## Database Configuration

### Connection Settings

```python
# bot/config.py
class DatabaseConfig:
    # Connection URL
    URL = os.getenv('DATABASE_URL')
    
    # Connection Pool
    POOL_SIZE = int(os.getenv('DATABASE_POOL_SIZE', 20))
    MAX_OVERFLOW = int(os.getenv('DATABASE_MAX_OVERFLOW', 30))
    POOL_TIMEOUT = int(os.getenv('DATABASE_POOL_TIMEOUT', 30))
    POOL_RECYCLE = int(os.getenv('DATABASE_POOL_RECYCLE', 3600))
    
    # Query Configuration
    QUERY_TIMEOUT = int(os.getenv('DATABASE_QUERY_TIMEOUT', 30))
    STATEMENT_TIMEOUT = int(os.getenv('DATABASE_STATEMENT_TIMEOUT', 60))
    
    # SSL Configuration
    SSL_MODE = os.getenv('DATABASE_SSL_MODE', 'prefer')
    SSL_CERT = os.getenv('DATABASE_SSL_CERT')
    SSL_KEY = os.getenv('DATABASE_SSL_KEY')
    SSL_ROOT_CERT = os.getenv('DATABASE_SSL_ROOT_CERT')
```

### Migration Settings

```python
# bot/config.py
class MigrationConfig:
    # Migration Directory
    MIGRATIONS_DIR = 'bot/migrations/versions'
    
    # Migration Table
    MIGRATION_TABLE = 'migrations'
    
    # Auto-migration
    AUTO_MIGRATE = os.getenv('AUTO_MIGRATE', 'false').lower() == 'true'
    
    # Backup Before Migration
    BACKUP_BEFORE_MIGRATION = True
    
    # Migration Timeout
    MIGRATION_TIMEOUT = int(os.getenv('MIGRATION_TIMEOUT', 300))
```

### Backup Configuration

```python
# bot/config.py
class BackupConfig:
    # Backup Settings
    ENABLED = os.getenv('BACKUP_ENABLED', 'true').lower() == 'true'
    SCHEDULE = os.getenv('BACKUP_SCHEDULE', '0 2 * * *')  # Daily at 2 AM
    RETENTION_DAYS = int(os.getenv('BACKUP_RETENTION_DAYS', 30))
    
    # Local Backup
    LOCAL_BACKUP_DIR = os.getenv('LOCAL_BACKUP_DIR', './backups')
    
    # S3 Backup
    S3_ENABLED = os.getenv('BACKUP_S3_ENABLED', 'false').lower() == 'true'
    S3_BUCKET = os.getenv('BACKUP_S3_BUCKET')
    S3_ACCESS_KEY = os.getenv('BACKUP_S3_ACCESS_KEY')
    S3_SECRET_KEY = os.getenv('BACKUP_S3_SECRET_KEY')
    S3_REGION = os.getenv('BACKUP_S3_REGION', 'us-east-1')
```

## Redis Configuration

### Connection Settings

```python
# bot/config.py
class RedisConfig:
    # Connection
    URL = os.getenv('REDIS_URL')
    HOST = os.getenv('REDIS_HOST', 'localhost')
    PORT = int(os.getenv('REDIS_PORT', 6379))
    PASSWORD = os.getenv('REDIS_PASSWORD')
    DB = int(os.getenv('REDIS_DB', 0))
    
    # Connection Pool
    POOL_SIZE = int(os.getenv('REDIS_POOL_SIZE', 10))
    MAX_CONNECTIONS = int(os.getenv('REDIS_MAX_CONNECTIONS', 50))
    
    # Timeouts
    CONNECT_TIMEOUT = int(os.getenv('REDIS_CONNECT_TIMEOUT', 5))
    SOCKET_TIMEOUT = int(os.getenv('REDIS_SOCKET_TIMEOUT', 5))
    
    # SSL Configuration
    SSL_ENABLED = os.getenv('REDIS_SSL_ENABLED', 'false').lower() == 'true'
    SSL_CERT_REQS = os.getenv('REDIS_SSL_CERT_REQS', 'required')
    SSL_CA_CERTS = os.getenv('REDIS_SSL_CA_CERTS')
```

### Cache Configuration

```python
# bot/config.py
class CacheConfig:
    # Default TTL
    DEFAULT_TTL = int(os.getenv('CACHE_TTL', 3600))  # 1 hour
    
    # Cache Keys TTL
    USER_CACHE_TTL = int(os.getenv('USER_CACHE_TTL', 1800))  # 30 minutes
    VPN_ACCOUNT_CACHE_TTL = int(os.getenv('VPN_ACCOUNT_CACHE_TTL', 600))  # 10 minutes
    PAYMENT_CACHE_TTL = int(os.getenv('PAYMENT_CACHE_TTL', 300))  # 5 minutes
    
    # Cache Prefixes
    USER_PREFIX = 'user:'
    VPN_ACCOUNT_PREFIX = 'vpn_account:'
    PAYMENT_PREFIX = 'payment:'
    SESSION_PREFIX = 'session:'
    RATE_LIMIT_PREFIX = 'rate_limit:'
    
    # Cache Sizes
    MAX_CACHE_SIZE = int(os.getenv('CACHE_MAX_SIZE', 1000))
```

## VPN Service Configuration

### Marzban Settings

```python
# bot/config.py
class VPNConfig:
    # Marzban API
    MARZBAN_URL = os.getenv('MARZBAN_URL')
    MARZBAN_USERNAME = os.getenv('MARZBAN_USERNAME')
    MARZBAN_PASSWORD = os.getenv('MARZBAN_PASSWORD')
    MARZBAN_TOKEN = os.getenv('MARZBAN_TOKEN')
    
    # API Configuration
    API_TIMEOUT = int(os.getenv('MARZBAN_API_TIMEOUT', 30))
    MAX_RETRIES = int(os.getenv('MARZBAN_MAX_RETRIES', 3))
    RETRY_DELAY = int(os.getenv('MARZBAN_RETRY_DELAY', 1))
    
    # Token Management
    TOKEN_REFRESH_THRESHOLD = int(os.getenv('TOKEN_REFRESH_THRESHOLD', 300))  # 5 minutes
    TOKEN_CACHE_KEY = 'marzban_token'
    
    # Account Defaults
    DEFAULT_PROTOCOL = os.getenv('VPN_DEFAULT_PROTOCOL', 'vless')
    DEFAULT_FLOW = os.getenv('VPN_DEFAULT_FLOW', 'xtls-rprx-vision')
    DEFAULT_SECURITY = os.getenv('VPN_DEFAULT_SECURITY', 'reality')
```

### Account Configuration

```python
# bot/config.py
class AccountConfig:
    # Trial Account Settings
    TRIAL_DATA_LIMIT_GB = int(os.getenv('TRIAL_DATA_LIMIT_GB', 5))
    TRIAL_DURATION_DAYS = int(os.getenv('TRIAL_DURATION_DAYS', 3))
    TRIAL_MAX_CONNECTIONS = int(os.getenv('TRIAL_MAX_CONNECTIONS', 1))
    
    # Premium Account Settings
    PREMIUM_DATA_LIMITS = {
        'basic': int(os.getenv('PREMIUM_BASIC_DATA_GB', 50)),
        'standard': int(os.getenv('PREMIUM_STANDARD_DATA_GB', 100)),
        'premium': int(os.getenv('PREMIUM_PREMIUM_DATA_GB', 200))
    }
    
    PREMIUM_DURATIONS = {
        'monthly': 30,
        'quarterly': 90,
        'yearly': 365
    }
    
    # Account Limits
    MAX_ACCOUNTS_PER_USER = int(os.getenv('MAX_ACCOUNTS_PER_USER', 3))
    MAX_CONCURRENT_CONNECTIONS = int(os.getenv('MAX_CONCURRENT_CONNECTIONS', 5))
    
    # Username Generation
    USERNAME_PREFIX = os.getenv('USERNAME_PREFIX', 'user')
    USERNAME_LENGTH = int(os.getenv('USERNAME_LENGTH', 12))
    USERNAME_INCLUDE_TIMESTAMP = True
```

### Server Configuration

```python
# bot/config.py
class ServerConfig:
    # Server Selection
    DEFAULT_SERVER = os.getenv('DEFAULT_VPN_SERVER', 'auto')
    
    # Server List
    AVAILABLE_SERVERS = {
        'us1': {
            'name': 'US East',
            'location': 'New York',
            'endpoint': 'us1.example.com',
            'port': 443
        },
        'eu1': {
            'name': 'EU West',
            'location': 'London',
            'endpoint': 'eu1.example.com',
            'port': 443
        },
        'asia1': {
            'name': 'Asia Pacific',
            'location': 'Singapore',
            'endpoint': 'asia1.example.com',
            'port': 443
        }
    }
    
    # Load Balancing
    LOAD_BALANCING_ENABLED = True
    SERVER_HEALTH_CHECK_INTERVAL = 300  # 5 minutes
    MAX_USERS_PER_SERVER = int(os.getenv('MAX_USERS_PER_SERVER', 1000))
```

## Payment Configuration

### Provider Settings

```python
# bot/config.py
class PaymentConfig:
    # Payment Provider
    PROVIDER_TOKEN = os.getenv('PAYMENT_PROVIDER_TOKEN')
    PROVIDER_NAME = os.getenv('PAYMENT_PROVIDER_NAME', 'Stripe')
    
    # Currency Settings
    DEFAULT_CURRENCY = os.getenv('PAYMENT_CURRENCY', 'USD')
    SUPPORTED_CURRENCIES = ['USD', 'EUR', 'GBP', 'RUB']
    
    # Webhook Configuration
    WEBHOOK_SECRET = os.getenv('PAYMENT_WEBHOOK_SECRET')
    WEBHOOK_TIMEOUT = int(os.getenv('PAYMENT_WEBHOOK_TIMEOUT', 30))
    
    # Payment Limits
    MIN_PAYMENT_AMOUNT = float(os.getenv('MIN_PAYMENT_AMOUNT', 1.0))
    MAX_PAYMENT_AMOUNT = float(os.getenv('MAX_PAYMENT_AMOUNT', 1000.0))
    
    # Refund Settings
    REFUND_ENABLED = os.getenv('REFUND_ENABLED', 'true').lower() == 'true'
    REFUND_WINDOW_DAYS = int(os.getenv('REFUND_WINDOW_DAYS', 7))
    AUTO_REFUND_ENABLED = False
```

### Cryptocurrency Payment Settings

```python
# bot/config.py
class CryptoPaymentConfig:
    # NowPayments Configuration
    NOWPAYMENTS_API_KEY = os.getenv('NOWPAYMENTS_API_KEY')
    NOWPAYMENTS_IPN_SECRET = os.getenv('NOWPAYMENTS_IPN_SECRET')
    NOWPAYMENTS_SANDBOX = os.getenv('NOWPAYMENTS_SANDBOX', 'false').lower() == 'true'
    
    # Supported Cryptocurrencies
    SUPPORTED_CURRENCIES = ['btc', 'eth', 'ltc', 'ton']
    DEFAULT_CRYPTO_CURRENCY = 'btc'
    
    # Payment Timeouts
    CRYPTO_PAYMENT_TIMEOUT = int(os.getenv('CRYPTO_PAYMENT_TIMEOUT', 3600))  # 1 hour
    PAYMENT_CHECK_INTERVAL = int(os.getenv('PAYMENT_CHECK_INTERVAL', 60))  # 1 minute
```

### TON Payment Settings

```python
# bot/config.py
class TONPaymentConfig:
    # TON Wallet Configuration
    TON_MASTER_WALLET = os.getenv('TON_MASTER_WALLET')
    TON_MASTER_PRIVATE_KEY = os.getenv('TON_MASTER_PRIVATE_KEY')
    
    # Network Configuration
    TON_NETWORK = os.getenv('TON_NETWORK', 'mainnet')  # mainnet or testnet
    TON_API_KEY = os.getenv('TON_API_KEY')
    
    # Payment Settings
    TON_PAYMENT_TIMEOUT_MINUTES = int(os.getenv('TON_PAYMENT_TIMEOUT_MINUTES', 30))
    TON_CONFIRMATION_BLOCKS = int(os.getenv('TON_CONFIRMATION_BLOCKS', 1))
    
    # Price Configuration
    TON_PRICE_UPDATE_INTERVAL = int(os.getenv('TON_PRICE_UPDATE_INTERVAL', 300))  # 5 minutes
    TON_PRICE_API_URL = 'https://api.coingecko.com/api/v3/simple/price?ids=the-open-network&vs_currencies=usd'
```

### Plan Configuration

```python
# bot/config.py
class PlanConfig:
    # Available Plans
    PLANS = {
        'basic_monthly': {
            'name': 'Basic Monthly',
            'price': 9.99,
            'currency': 'USD',
            'duration_days': 30,
            'data_limit_gb': 50,
            'max_connections': 2,
            'features': ['Basic support', 'Standard servers']
        },
        'standard_monthly': {
            'name': 'Standard Monthly',
            'price': 19.99,
            'currency': 'USD',
            'duration_days': 30,
            'data_limit_gb': 100,
            'max_connections': 3,
            'features': ['Priority support', 'All servers', 'No ads']
        },
        'premium_monthly': {
            'name': 'Premium Monthly',
            'price': 29.99,
            'currency': 'USD',
            'duration_days': 30,
            'data_limit_gb': 200,
            'max_connections': 5,
            'features': ['24/7 support', 'Premium servers', 'No ads', 'Priority bandwidth']
        }
    }
    
    # Discounts
    QUARTERLY_DISCOUNT = 0.15  # 15% off
    YEARLY_DISCOUNT = 0.25     # 25% off
    
    # Referral Bonuses
    REFERRAL_BONUS_DAYS = 7
    REFERRAL_BONUS_DATA_GB = 10
```

## Security Configuration

### Authentication Settings

```python
# bot/config.py
class SecurityConfig:
    # JWT Configuration
    JWT_SECRET = os.getenv('JWT_SECRET')
    JWT_ALGORITHM = 'HS256'
    JWT_EXPIRY = int(os.getenv('JWT_EXPIRY', 3600))  # 1 hour
    
    # Encryption
    SECRET_KEY = os.getenv('SECRET_KEY')
    ENCRYPTION_KEY = os.getenv('ENCRYPTION_KEY')
    ENCRYPTION_ALGORITHM = 'AES-256-GCM'
    
    # Password Hashing
    PASSWORD_HASH_ALGORITHM = 'bcrypt'
    PASSWORD_HASH_ROUNDS = 12
    
    # Session Management
    SESSION_TIMEOUT = int(os.getenv('SESSION_TIMEOUT', 1800))  # 30 minutes
    MAX_SESSIONS_PER_USER = int(os.getenv('MAX_SESSIONS_PER_USER', 5))
    
    # API Security
    API_KEY_LENGTH = 32
    API_KEY_EXPIRY_DAYS = 90
    REQUIRE_API_KEY = True
```

### Rate Limiting

```python
# bot/config.py
class RateLimitConfig:
    # Global Rate Limits
    GLOBAL_RATE_LIMIT = int(os.getenv('RATE_LIMIT_REQUESTS', 100))
    GLOBAL_RATE_WINDOW = int(os.getenv('RATE_LIMIT_WINDOW', 60))  # seconds
    
    # Per-User Rate Limits
    USER_RATE_LIMITS = {
        'commands': {'requests': 30, 'window': 60},
        'callbacks': {'requests': 50, 'window': 60},
        'payments': {'requests': 5, 'window': 300},
        'trial_requests': {'requests': 1, 'window': 86400}  # 1 per day
    }
    
    # Admin Rate Limits
    ADMIN_RATE_LIMITS = {
        'commands': {'requests': 100, 'window': 60},
        'broadcast': {'requests': 1, 'window': 300}
    }
    
    # Rate Limit Storage
    STORAGE_BACKEND = os.getenv('RATE_LIMIT_STORAGE', 'redis')
    STORAGE_PREFIX = 'rate_limit:'
```

### Input Validation

```python
# bot/config.py
class ValidationConfig:
    # User Input Limits
    MAX_USERNAME_LENGTH = 32
    MAX_MESSAGE_LENGTH = 1000
    MAX_CALLBACK_DATA_LENGTH = 64
    
    # Allowed Characters
    USERNAME_PATTERN = r'^[a-zA-Z0-9_]{3,32}$'
    EMAIL_PATTERN = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    
    # File Upload Limits
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    ALLOWED_FILE_TYPES = ['.jpg', '.jpeg', '.png', '.gif', '.pdf']
    
    # Content Filtering
    ENABLE_CONTENT_FILTER = True
    BLOCKED_WORDS = ['spam', 'abuse', 'hack']
    
    # Sanitization
    SANITIZE_HTML = True
    ESCAPE_MARKDOWN = True
```

## Logging Configuration

### Log Settings

```python
# bot/config.py
class LoggingConfig:
    # Log Level
    LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    
    # Log Format
    FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    DATE_FORMAT = '%Y-%m-%d %H:%M:%S'
    
    # Log Files
    LOG_DIR = os.getenv('LOG_DIR', './logs')
    LOG_FILE = os.path.join(LOG_DIR, 'bot.log')
    ERROR_LOG_FILE = os.path.join(LOG_DIR, 'error.log')
    ACCESS_LOG_FILE = os.path.join(LOG_DIR, 'access.log')
    
    # Log Rotation
    MAX_LOG_SIZE = int(os.getenv('MAX_LOG_SIZE', 10 * 1024 * 1024))  # 10MB
    LOG_BACKUP_COUNT = int(os.getenv('LOG_BACKUP_COUNT', 5))
    
    # External Logging
    SENTRY_DSN = os.getenv('SENTRY_DSN')
    SENTRY_ENVIRONMENT = os.getenv('SENTRY_ENVIRONMENT', 'production')
    
    # Structured Logging
    JSON_LOGGING = os.getenv('JSON_LOGGING', 'false').lower() == 'true'
    LOG_CORRELATION_ID = True
```

### Logger Configuration

```python
# bot/logging_config.py
import logging
import logging.handlers
from pythonjsonlogger import jsonlogger

def setup_logging():
    """Setup logging configuration."""
    
    # Create logs directory
    os.makedirs(LoggingConfig.LOG_DIR, exist_ok=True)
    
    # Root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, LoggingConfig.LEVEL))
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # File handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        LoggingConfig.LOG_FILE,
        maxBytes=LoggingConfig.MAX_LOG_SIZE,
        backupCount=LoggingConfig.LOG_BACKUP_COUNT
    )
    file_handler.setLevel(logging.DEBUG)
    
    # Error file handler
    error_handler = logging.handlers.RotatingFileHandler(
        LoggingConfig.ERROR_LOG_FILE,
        maxBytes=LoggingConfig.MAX_LOG_SIZE,
        backupCount=LoggingConfig.LOG_BACKUP_COUNT
    )
    error_handler.setLevel(logging.ERROR)
    
    # Formatters
    if LoggingConfig.JSON_LOGGING:
        formatter = jsonlogger.JsonFormatter(
            '%(asctime)s %(name)s %(levelname)s %(message)s'
        )
    else:
        formatter = logging.Formatter(
            LoggingConfig.FORMAT,
            datefmt=LoggingConfig.DATE_FORMAT
        )
    
    # Apply formatters
    console_handler.setFormatter(formatter)
    file_handler.setFormatter(formatter)
    error_handler.setFormatter(formatter)
    
    # Add handlers
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(error_handler)
    
    # Configure specific loggers
    logging.getLogger('telegram').setLevel(logging.WARNING)
    logging.getLogger('aiohttp').setLevel(logging.WARNING)
    logging.getLogger('asyncpg').setLevel(logging.WARNING)
```

## Performance Configuration

### Optimization Settings

```python
# bot/config.py
class PerformanceConfig:
    # Worker Configuration
    WORKER_PROCESSES = int(os.getenv('WORKER_PROCESSES', 4))
    WORKER_THREADS = int(os.getenv('WORKER_THREADS', 10))
    
    # Request Handling
    MAX_CONCURRENT_REQUESTS = int(os.getenv('MAX_CONCURRENT_REQUESTS', 100))
    REQUEST_TIMEOUT = int(os.getenv('REQUEST_TIMEOUT', 30))
    KEEP_ALIVE_TIMEOUT = int(os.getenv('KEEP_ALIVE_TIMEOUT', 5))
    
    # Memory Management
    MAX_MEMORY_USAGE = int(os.getenv('MAX_MEMORY_USAGE', 512 * 1024 * 1024))  # 512MB
    GARBAGE_COLLECTION_THRESHOLD = 0.8
    
    # Background Tasks
    BACKGROUND_TASK_INTERVAL = int(os.getenv('BACKGROUND_TASK_INTERVAL', 300))  # 5 minutes
    CLEANUP_TASK_INTERVAL = int(os.getenv('CLEANUP_TASK_INTERVAL', 3600))  # 1 hour
    
    # Caching Strategy
    CACHE_STRATEGY = os.getenv('CACHE_STRATEGY', 'lru')
    CACHE_COMPRESSION = True
    CACHE_SERIALIZATION = 'pickle'
```

### Resource Limits

```python
# bot/config.py
class ResourceConfig:
    # CPU Limits
    MAX_CPU_USAGE = float(os.getenv('MAX_CPU_USAGE', 80.0))  # 80%
    CPU_MONITORING_INTERVAL = 60  # seconds
    
    # Memory Limits
    MAX_MEMORY_USAGE = int(os.getenv('MAX_MEMORY_USAGE', 1024 * 1024 * 1024))  # 1GB
    MEMORY_WARNING_THRESHOLD = 0.8
    
    # Disk Limits
    MAX_DISK_USAGE = int(os.getenv('MAX_DISK_USAGE', 10 * 1024 * 1024 * 1024))  # 10GB
    DISK_CLEANUP_THRESHOLD = 0.9
    
    # Network Limits
    MAX_BANDWIDTH_MBPS = int(os.getenv('MAX_BANDWIDTH_MBPS', 100))
    CONNECTION_LIMIT = int(os.getenv('CONNECTION_LIMIT', 1000))
```

## Advanced Configuration

### Feature Flags

```python
# bot/config.py
class FeatureFlags:
    # Core Features
    TRIAL_ACCOUNTS_ENABLED = os.getenv('TRIAL_ACCOUNTS_ENABLED', 'true').lower() == 'true'
    PAYMENTS_ENABLED = os.getenv('PAYMENTS_ENABLED', 'true').lower() == 'true'
    REFERRAL_SYSTEM_ENABLED = os.getenv('REFERRAL_SYSTEM_ENABLED', 'false').lower() == 'true'
    
    # Analytics
    ANALYTICS_ENABLED = os.getenv('ANALYTICS_ENABLED', 'true').lower() == 'true'
    USER_TRACKING_ENABLED = os.getenv('USER_TRACKING_ENABLED', 'false').lower() == 'true'
    
    # Experimental Features
    AI_SUPPORT_ENABLED = os.getenv('AI_SUPPORT_ENABLED', 'false').lower() == 'true'
    VOICE_COMMANDS_ENABLED = os.getenv('VOICE_COMMANDS_ENABLED', 'false').lower() == 'true'
    
    # Maintenance
    MAINTENANCE_MODE = os.getenv('MAINTENANCE_MODE', 'false').lower() == 'true'
    READ_ONLY_MODE = os.getenv('READ_ONLY_MODE', 'false').lower() == 'true'
```

### Monitoring Configuration

```python
# bot/config.py
class MonitoringConfig:
    # Metrics
    METRICS_ENABLED = os.getenv('METRICS_ENABLED', 'true').lower() == 'true'
    METRICS_PORT = int(os.getenv('METRICS_PORT', 8080))
    METRICS_PATH = '/metrics'
    
    # Health Checks
    HEALTH_CHECK_ENABLED = os.getenv('HEALTH_CHECK_ENABLED', 'true').lower() == 'true'
    HEALTH_CHECK_PORT = int(os.getenv('HEALTH_CHECK_PORT', 8081))
    HEALTH_CHECK_PATH = '/health'
    
    # Alerting
    ALERT_WEBHOOK = os.getenv('ALERT_WEBHOOK')
    SLACK_WEBHOOK = os.getenv('SLACK_WEBHOOK_URL')
    DISCORD_WEBHOOK = os.getenv('DISCORD_WEBHOOK_URL')
    
    # Thresholds
    CPU_ALERT_THRESHOLD = 90.0
    MEMORY_ALERT_THRESHOLD = 90.0
    DISK_ALERT_THRESHOLD = 90.0
    ERROR_RATE_THRESHOLD = 5.0  # errors per minute
```

### Deployment Configuration

```python
# bot/config.py
class DeploymentConfig:
    # Environment
    ENVIRONMENT = os.getenv('ENVIRONMENT', 'production')
    DEBUG = os.getenv('DEBUG', 'false').lower() == 'true'
    
    # Version
    VERSION = os.getenv('APP_VERSION', '1.0.0')
    BUILD_NUMBER = os.getenv('BUILD_NUMBER', 'unknown')
    
    # Deployment Strategy
    ROLLING_UPDATE = True
    HEALTH_CHECK_GRACE_PERIOD = 30  # seconds
    SHUTDOWN_GRACE_PERIOD = 30  # seconds
    
    # Auto-scaling
    AUTO_SCALING_ENABLED = False
    MIN_REPLICAS = 1
    MAX_REPLICAS = 10
    TARGET_CPU_UTILIZATION = 70
```

## Configuration Validation

```python
# bot/config_validator.py
class ConfigValidator:
    """Validate configuration settings."""
    
    @staticmethod
    def validate_required_vars():
        """Validate required environment variables."""
        required_vars = [
            'BOT_TOKEN',
            'DATABASE_URL',
            'REDIS_URL',
            'MARZBAN_URL',
            'SECRET_KEY'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {missing_vars}")
    
    @staticmethod
    def validate_database_config():
        """Validate database configuration."""
        url = os.getenv('DATABASE_URL')
        if not url or not url.startswith('postgresql://'):
            raise ValueError("Invalid DATABASE_URL format")
    
    @staticmethod
    def validate_redis_config():
        """Validate Redis configuration."""
        url = os.getenv('REDIS_URL')
        if not url or not url.startswith('redis://'):
            raise ValueError("Invalid REDIS_URL format")
    
    @staticmethod
    def validate_all():
        """Validate all configuration."""
        ConfigValidator.validate_required_vars()
        ConfigValidator.validate_database_config()
        ConfigValidator.validate_redis_config()
        
        print("✅ Configuration validation passed")
```

## Usage Examples

### Loading Configuration

```python
# bot/main.py
from bot.config import BotConfig, DatabaseConfig, RedisConfig
from bot.config_validator import ConfigValidator

def load_config():
    """Load and validate configuration."""
    # Validate configuration
    ConfigValidator.validate_all()
    
    # Load configuration classes
    bot_config = BotConfig()
    db_config = DatabaseConfig()
    redis_config = RedisConfig()
    
    return {
        'bot': bot_config,
        'database': db_config,
        'redis': redis_config
    }
```

### Environment-Specific Configuration

```python
# bot/config.py
class Config:
    """Base configuration."""
    
    @classmethod
    def get_config(cls):
        """Get configuration based on environment."""
        env = os.getenv('ENVIRONMENT', 'production')
        
        if env == 'development':
            return DevelopmentConfig()
        elif env == 'testing':
            return TestingConfig()
        else:
            return ProductionConfig()

class DevelopmentConfig(Config):
    DEBUG = True
    LOG_LEVEL = 'DEBUG'
    DATABASE_POOL_SIZE = 5
    CACHE_TTL = 60

class TestingConfig(Config):
    TESTING = True
    DATABASE_URL = 'postgresql://test:test@localhost:5432/test_vpn_bot'
    REDIS_URL = 'redis://localhost:6379/1'

class ProductionConfig(Config):
    DEBUG = False
    LOG_LEVEL = 'INFO'
    DATABASE_POOL_SIZE = 20
    CACHE_TTL = 3600
```

This configuration guide provides comprehensive coverage of all configuration options for the VPN Telegram Bot project. Adjust the values according to your specific requirements and environment.