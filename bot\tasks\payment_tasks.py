"""Payment-related background tasks."""

import asyncio
import logging
from datetime import datetime, timedelta
from bot.services.payment_service import payment_service
from bot.services.payment_verification_service import payment_verification_service

logger = logging.getLogger(__name__)


class PaymentTaskManager:
    """Manager for payment-related background tasks."""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.running = False
    
    async def start_payment_tasks(self):
        """Start all payment-related background tasks."""
        if self.running:
            return
        
        self.running = True
        self.logger.info("Starting payment background tasks")
        
        # Start tasks concurrently
        tasks = [
            self._retry_failed_payments_task(),
            self._verify_pending_payments_task(),
            self._cleanup_expired_payments_task()
        ]
        
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def stop_payment_tasks(self):
        """Stop all payment background tasks."""
        self.running = False
        self.logger.info("Stopping payment background tasks")
    
    async def _retry_failed_payments_task(self):
        """Background task to retry failed payments."""
        while self.running:
            try:
                self.logger.info("Running failed payments retry task")
                
                result = await payment_service.retry_failed_payments()
                
                if result['total_retried'] > 0:
                    self.logger.info(
                        f"Retried {result['total_retried']} failed payments: "
                        f"{result['successful_retries']} successful, "
                        f"{result['failed_retries']} failed"
                    )
                
                # Wait 30 minutes before next retry
                await asyncio.sleep(30 * 60)
                
            except Exception as e:
                self.logger.error(f"Error in failed payments retry task: {e}")
                await asyncio.sleep(60)  # Wait 1 minute on error
    
    async def _verify_pending_payments_task(self):
        """Background task to verify pending payments."""
        while self.running:
            try:
                self.logger.info("Running pending payments verification task")
                
                result = await payment_verification_service.verify_pending_payments()
                
                if result['total_checked'] > 0:
                    self.logger.info(
                        f"Verified {result['total_checked']} pending payments: "
                        f"{result['completed']} completed, "
                        f"{result['failed']} failed, "
                        f"{result['still_pending']} still pending"
                    )
                
                # Wait 10 minutes before next verification
                await asyncio.sleep(10 * 60)
                
            except Exception as e:
                self.logger.error(f"Error in pending payments verification task: {e}")
                await asyncio.sleep(60)  # Wait 1 minute on error
    
    async def _cleanup_expired_payments_task(self):
        """Background task to cleanup expired payments."""
        while self.running:
            try:
                self.logger.info("Running expired payments cleanup task")
                
                result = await payment_verification_service.cleanup_expired_payments()
                
                if result['total_cleaned'] > 0:
                    self.logger.info(
                        f"Cleaned up {result['total_cleaned']} expired payments: "
                        f"{result['crypto_cleaned']} crypto, "
                        f"{result['ton_cleaned']} TON"
                    )
                
                # Wait 1 hour before next cleanup
                await asyncio.sleep(60 * 60)
                
            except Exception as e:
                self.logger.error(f"Error in expired payments cleanup task: {e}")
                await asyncio.sleep(60)  # Wait 1 minute on error
    
    async def manual_retry_failed_payments(self) -> dict:
        """Manually trigger failed payments retry."""
        try:
            self.logger.info("Manual retry of failed payments triggered")
            result = await payment_service.retry_failed_payments()
            return result
        except Exception as e:
            self.logger.error(f"Error in manual failed payments retry: {e}")
            return {
                'total_retried': 0,
                'successful_retries': 0,
                'failed_retries': 0,
                'error': str(e)
            }
    
    async def manual_verify_pending_payments(self) -> dict:
        """Manually trigger pending payments verification."""
        try:
            self.logger.info("Manual verification of pending payments triggered")
            result = await payment_verification_service.verify_pending_payments()
            return result
        except Exception as e:
            self.logger.error(f"Error in manual pending payments verification: {e}")
            return {
                'total_checked': 0,
                'completed': 0,
                'failed': 0,
                'still_pending': 0,
                'errors': 1,
                'error': str(e)
            }
    
    async def manual_cleanup_expired_payments(self) -> dict:
        """Manually trigger expired payments cleanup."""
        try:
            self.logger.info("Manual cleanup of expired payments triggered")
            result = await payment_verification_service.cleanup_expired_payments()
            return result
        except Exception as e:
            self.logger.error(f"Error in manual expired payments cleanup: {e}")
            return {
                'crypto_cleaned': 0,
                'ton_cleaned': 0,
                'total_cleaned': 0,
                'error': str(e)
            }


# Global instance
payment_task_manager = PaymentTaskManager()


# Convenience functions for external use
async def start_payment_background_tasks():
    """Start payment background tasks."""
    await payment_task_manager.start_payment_tasks()


async def stop_payment_background_tasks():
    """Stop payment background tasks."""
    await payment_task_manager.stop_payment_tasks()


async def retry_failed_payments_now():
    """Manually retry failed payments."""
    return await payment_task_manager.manual_retry_failed_payments()


async def verify_pending_payments_now():
    """Manually verify pending payments."""
    return await payment_task_manager.manual_verify_pending_payments()


async def cleanup_expired_payments_now():
    """Manually cleanup expired payments."""
    return await payment_task_manager.manual_cleanup_expired_payments()
