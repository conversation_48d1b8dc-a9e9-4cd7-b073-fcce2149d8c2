<?php

declare(strict_types=1);

namespace VpnBot\Services;

use VpnBot\Config\Config;
use VpnBot\Models\User;
use VpnBot\Models\VpnAccount;
use VpnBot\Models\VpnPanel;
use Psr\Log\LoggerInterface;

class VpnService
{
    private Config $config;
    private LoggerInterface $logger;

    public function __construct(Config $config, LoggerInterface $logger)
    {
        $this->config = $config;
        $this->logger = $logger;
    }

    public function checkTrialEligibility(int $telegramId): array
    {
        try {
            $user = User::findByTelegramId($telegramId);
            if (!$user) {
                return [
                    'eligible' => false,
                    'reason' => 'User not found'
                ];
            }

            if ($user->has_used_trial) {
                return [
                    'eligible' => false,
                    'reason' => 'Trial already used'
                ];
            }

            // Check channel subscriptions
            $channelService = new ChannelService($this->config, $this->logger);
            $subscriptionStatus = $channelService->checkUserSubscriptions($telegramId);
            
            if (!$subscriptionStatus['is_subscribed']) {
                return [
                    'eligible' => false,
                    'reason' => 'Channel subscription required',
                    'missing_channels' => $subscriptionStatus['missing_channels'],
                    'subscribed_count' => $subscriptionStatus['subscribed_count'],
                    'total_required' => $subscriptionStatus['total_required']
                ];
            }

            return [
                'eligible' => true,
                'reason' => 'Eligible for trial'
            ];

        } catch (\Exception $e) {
            $this->logger->error('Error checking trial eligibility: ' . $e->getMessage());
            return [
                'eligible' => false,
                'reason' => 'System error'
            ];
        }
    }

    public function createTrialAccount(int $userId): ?array
    {
        try {
            $user = User::findById($userId);
            if (!$user) {
                return null;
            }

            // Check eligibility first
            $eligibility = $this->checkTrialEligibility($user->telegram_id);
            if (!$eligibility['eligible']) {
                return null;
            }

            // Get available VPN panel
            $panel = $this->getAvailablePanel();
            if (!$panel) {
                $this->logger->error('No available VPN panel for trial account');
                return null;
            }

            // Generate unique username
            $username = 'trial_' . $user->telegram_id . '_' . time();
            
            // Create VPN account
            $vpnAccount = new VpnAccount();
            $vpnAccount->user_id = $user->id;
            $vpnAccount->vpn_panel_id = $panel->id;
            $vpnAccount->username = $username;
            $vpnAccount->data_limit = $this->config->get('vpn.trial_data_limit');
            $vpnAccount->expire_date = date('Y-m-d H:i:s', strtotime('+' . $this->config->get('vpn.trial_duration_days') . ' days'));
            $vpnAccount->is_trial = true;
            $vpnAccount->status = 'active';
            
            if ($vpnAccount->save()) {
                // Update user trial status
                $user->has_used_trial = true;
                $user->trial_count++;
                $user->last_trial_at = date('Y-m-d H:i:s');
                $user->save();

                // Create account on Marzban panel (placeholder)
                $config = $this->createMarzbanAccount($panel, $vpnAccount);

                return [
                    'username' => $username,
                    'config' => $config,
                    'data_limit' => $vpnAccount->formatDataLimit(),
                    'expire_date' => $vpnAccount->expire_date,
                    'days_remaining' => $vpnAccount->getDaysUntilExpiry()
                ];
            }

            return null;

        } catch (\Exception $e) {
            $this->logger->error('Error creating trial account: ' . $e->getMessage());
            return null;
        }
    }

    public function getUserAccounts(int $userId): array
    {
        try {
            $accounts = VpnAccount::findByUserId($userId);
            $result = [];

            foreach ($accounts as $account) {
                $result[] = [
                    'id' => $account->id,
                    'username' => $account->username,
                    'status' => $account->status,
                    'is_trial' => $account->is_trial,
                    'data_limit' => $account->formatDataLimit(),
                    'used_data' => $account->formatUsedData(),
                    'usage_percentage' => $account->getUsagePercentage(),
                    'expire_date' => $account->expire_date,
                    'days_remaining' => $account->getDaysUntilExpiry(),
                    'is_expired' => $account->isExpired(),
                    'is_active' => $account->is_active,
                    'account_type' => $account->is_trial ? 'trial' : 'premium'
                ];
            }

            return $result;

        } catch (\Exception $e) {
            $this->logger->error('Error getting user accounts: ' . $e->getMessage());
            return [];
        }
    }

    private function getAvailablePanel(): ?VpnPanel
    {
        // In a real implementation, this would query the database for available panels
        // For now, return a mock panel
        $panel = new VpnPanel();
        $panel->id = 1;
        $panel->name = 'Default Panel';
        $panel->base_url = $this->config->get('marzban.url');
        $panel->api_username = $this->config->get('marzban.username');
        $panel->api_password = $this->config->get('marzban.password');
        $panel->is_active = true;
        
        return $panel;
    }

    private function createMarzbanAccount(VpnPanel $panel, VpnAccount $account): string
    {
        // Placeholder for Marzban API integration
        // In a real implementation, this would call the Marzban API to create the account
        // and return the actual configuration
        
        $config = "vless://" . $account->uuid . "@" . parse_url($panel->base_url, PHP_URL_HOST) . ":443" .
                  "?type=tcp&security=tls&sni=" . parse_url($panel->base_url, PHP_URL_HOST) .
                  "#" . urlencode($account->username);
        
        return $config;
    }

    public function updateAccountUsage(int $accountId, int $usedData): bool
    {
        try {
            $account = VpnAccount::findById($accountId);
            if (!$account) {
                return false;
            }

            $account->used_data = $usedData;
            $account->last_usage_check = date('Y-m-d H:i:s');
            
            // Check if account should be disabled due to usage or expiry
            if ($account->used_data >= $account->data_limit || $account->isExpired()) {
                $account->status = 'expired';
                $account->is_active = false;
            }

            return $account->save();

        } catch (\Exception $e) {
            $this->logger->error('Error updating account usage: ' . $e->getMessage());
            return false;
        }
    }

    public function renewAccount(int $accountId, int $planId): bool
    {
        try {
            $account = VpnAccount::findById($accountId);
            if (!$account) {
                return false;
            }

            // In a real implementation, this would:
            // 1. Get the plan details
            // 2. Update the account with new limits and expiry
            // 3. Reset usage data
            // 4. Update the account on Marzban panel

            $account->status = 'active';
            $account->is_active = true;
            $account->used_data = 0;
            $account->expire_date = date('Y-m-d H:i:s', strtotime('+30 days')); // Example
            
            return $account->save();

        } catch (\Exception $e) {
            $this->logger->error('Error renewing account: ' . $e->getMessage());
            return false;
        }
    }
}
