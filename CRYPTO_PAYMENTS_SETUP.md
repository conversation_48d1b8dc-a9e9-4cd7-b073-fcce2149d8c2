# Cryptocurrency Payments Setup Guide

This guide will help you set up cryptocurrency payments using NowPayments for your Telegram VPN bot.

## Prerequisites

1. A NowPayments account (sign up at https://nowpayments.io/)
2. Your bot should be running and accessible via a public domain/IP
3. SSL certificate for webhook notifications (recommended)

## Step 1: Create NowPayments Account

1. Visit https://nowpayments.io/ and create an account
2. Complete the verification process
3. Navigate to your dashboard

## Step 2: Get API Credentials

1. In your NowPayments dashboard, go to **Settings** → **API Keys**
2. Generate a new API key
3. Copy your API key
4. Generate an IPN (Instant Payment Notification) secret
5. Copy your IPN secret

## Step 3: Configure Environment Variables

Update your `.env` file with the following:

```env
# NowPayments Configuration
NOWPAYMENTS_API_KEY="your_api_key_here"
NOWPAYMENTS_IPN_SECRET="your_ipn_secret_here"
WEBHOOK_URL="https://yourdomain.com"
```

## Step 4: Set Up Webhook (Optional but Recommended)

For automatic payment status updates, set up a webhook:

1. In NowPayments dashboard, go to **Settings** → **IPN Settings**
2. Set your IPN URL to: `https://yourdomain.com/webhook/nowpayments`
3. Enable IPN notifications
4. Save the settings

## Step 5: Test the Integration

1. Restart your bot: `docker-compose restart vpn-bot`
2. Check bot logs: `docker-compose logs vpn-bot`
3. Test a small payment with a supported cryptocurrency

## Supported Cryptocurrencies

The bot supports popular cryptocurrencies including:
- Bitcoin (BTC)
- Ethereum (ETH)
- Litecoin (LTC)
- Bitcoin Cash (BCH)
- Dogecoin (DOGE)
- Tether (USDT)
- USD Coin (USDC)
- Binance Coin (BNB)
- Cardano (ADA)
- Polygon (MATIC)

## How It Works

1. User selects a premium plan
2. User chooses "Crypto Payment" option
3. User selects preferred cryptocurrency
4. Bot creates a payment request with NowPayments
5. User receives payment address and amount
6. User sends payment to the provided address
7. Bot monitors payment status and activates premium subscription upon confirmation

## Troubleshooting

### Bot doesn't show crypto payment option
- Ensure `NOWPAYMENTS_API_KEY` is set in `.env`
- Check bot logs for API connection errors
- Verify your NowPayments account is active

### Payments not being detected
- Check if webhook URL is correctly configured
- Verify IPN secret matches in both NowPayments and `.env`
- Monitor bot logs for webhook notifications

### API errors
- Verify API key is correct and active
- Check NowPayments service status
- Ensure your account has sufficient permissions

## Security Notes

1. Keep your API key and IPN secret secure
2. Use HTTPS for webhook URLs
3. Regularly monitor payment transactions
4. Set up proper logging for payment events

## Support

For NowPayments-specific issues:
- Documentation: https://documenter.getpostman.com/view/7907941/S1a32n38
- Support: https://nowpayments.io/help

For bot-related issues:
- Check the bot logs: `docker-compose logs vpn-bot`
- Review the payment service logs in the application