#!/usr/bin/env python3
"""
Script to fix remaining MySQL syntax in database_schema.sql
"""

import re

def fix_mysql_syntax():
    """Fix MySQL syntax to PostgreSQL syntax."""
    
    # Read the current schema
    with open('database_schema.sql', 'r') as f:
        content = f.read()
    
    # Replace AUTO_INCREMENT with SERIAL
    content = re.sub(r'id INT AUTO_INCREMENT PRIMARY KEY', 'id SERIAL PRIMARY KEY', content)
    
    # Replace INT with INTEGER
    content = re.sub(r'\bINT\b(?!\s+AUTO_INCREMENT)', 'INTEGER', content)
    
    # Replace JSON with JSONB
    content = re.sub(r'\bJSON\b', 'JSONB', content)
    
    # Remove MySQL-specific ON UPDATE CURRENT_TIMESTAMP
    content = re.sub(r'\s+ON UPDATE CURRENT_TIMESTAMP', '', content)
    
    # Fix ENUM syntax - replace inline ENUM with type references
    content = re.sub(
        r"ENUM\('data', 'time', 'premium', 'trial_reset'\)",
        'reward_type',
        content
    )
    
    # Fix INDEX syntax to CREATE INDEX
    lines = content.split('\n')
    new_lines = []
    current_table = None
    indexes_to_create = []
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        if line.startswith('CREATE TABLE'):
            current_table = line.split()[2]
            new_lines.append(lines[i])
            
        elif line.startswith('INDEX ') and current_table:
            # Extract index definition
            index_match = re.match(r'INDEX\s+(\w+)\s*\(([^)]+)\)', line)
            if index_match:
                index_name = index_match.group(1)
                columns = index_match.group(2)
                indexes_to_create.append(f"CREATE INDEX {index_name} ON {current_table}({columns});")
            # Skip this line (don't add to new_lines)
            
        elif line == ');' and current_table and indexes_to_create:
            # End of table, add the closing parenthesis
            new_lines.append(lines[i])
            new_lines.append('')
            
            # Add the indexes
            for index_sql in indexes_to_create:
                new_lines.append(index_sql)
            
            indexes_to_create = []
            current_table = None
            
        else:
            new_lines.append(lines[i])
        
        i += 1
    
    # Join lines back
    content = '\n'.join(new_lines)
    
    # Write the fixed schema
    with open('database_schema.sql', 'w') as f:
        f.write(content)
    
    print("✅ Fixed MySQL syntax in database_schema.sql")
    print("✅ Replaced AUTO_INCREMENT with SERIAL")
    print("✅ Replaced INT with INTEGER") 
    print("✅ Replaced JSON with JSONB")
    print("✅ Removed MySQL ON UPDATE CURRENT_TIMESTAMP")
    print("✅ Fixed INDEX syntax to CREATE INDEX statements")

if __name__ == "__main__":
    fix_mysql_syntax()
