
services:
  # Database
  postgres:
    image: postgres:16-alpine
    container_name: vpn-postgres
    restart: unless-stopped
    env_file: .env
    environment:
      POSTGRES_DB: ${POSTGRES_DATABASE}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./01-schema.sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - vpn-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DATABASE}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Cache
  redis:
    image: redis:7.4-alpine
    container_name: vpn-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - vpn-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Telegram Bot
  vpn-bot:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: vpn-bot
    restart: unless-stopped
    env_file: .env
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - vpn-network
    volumes:
      - ./logs:/app/logs
    command: python -m bot.main

  # Celery Worker
  vpn-celery:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: vpn-celery
    restart: unless-stopped
    env_file: .env
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - vpn-network
    volumes:
      - ./logs:/app/logs
    command: celery -A bot.tasks worker --loglevel=info --pool=solo

  # Admin API
  vpn-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: vpn-app
    restart: unless-stopped
    env_file: .env
    ports:
      - "8000:8000"  # Admin API
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - vpn-network
    volumes:
      - ./logs:/app/logs
    command: uvicorn admin.main:app --host 0.0.0.0 --port 8000

  # Admin Panel Frontend
  admin-panel:
    build:
      context: ./admin-panel
      dockerfile: Dockerfile
    container_name: vpn-admin-panel
    restart: unless-stopped
    ports:
      - "3002:80"
    depends_on:
      - vpn-app
      - vpn-bot
    networks:
      - vpn-network

volumes:
  postgres_data:
  redis_data:

networks:
  vpn-network:
    driver: bridge