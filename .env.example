# Telegram Bot Configuration
BOT_TOKEN=your_telegram_bot_token_here
ADMIN_USER_ID=your_telegram_user_id

# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_USER=vpn_bot
POSTGRES_PASSWORD=vpn_bot_pass
POSTGRES_DATABASE=telegram_vpn_bot
POSTGRES_PORT=5432

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# VPN Configuration
FREE_DATA_LIMIT=**********
FREE_DURATION_DAYS=7

# Channel Configuration
REQUIRED_CHANNELS=@your_channel1,@your_channel2

# Payment Configuration
PAYMENT_PROVIDER_TOKEN=your_payment_provider_token

# Cryptocurrency Payment Configuration
NOWPAYMENTS_API_KEY=your_nowpayments_api_key
NOWPAYMENTS_IPN_SECRET=your_nowpayments_ipn_secret
WEBHOOK_URL=https://your-domain.com

# TON Payment Configuration
TON_MASTER_WALLET=your_ton_master_wallet_address
TON_MASTER_PRIVATE_KEY=your_ton_master_private_key
TON_NETWORK=mainnet
TON_API_KEY=your_ton_api_key_optional
TON_PAYMENT_TIMEOUT_MINUTES=30

# Marzban Configuration
MARZBAN_URL=https://your-marzban-panel.com
MARZBAN_USERNAME=admin
MARZBAN_PASSWORD=your_marzban_password
MARZBAN_TOKEN=your_marzban_api_token
MARZBAN_API_TIMEOUT=30