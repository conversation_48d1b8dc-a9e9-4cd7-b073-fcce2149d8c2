# Frequently Asked Questions (FAQ)

This FAQ covers common questions, issues, and solutions for the VPN Telegram Bot project.

## Table of Contents

1. [General Questions](#general-questions)
2. [Installation & Setup](#installation--setup)
3. [Bot Configuration](#bot-configuration)
4. [VPN Service Integration](#vpn-service-integration)
5. [Payment Issues](#payment-issues)
6. [User Management](#user-management)
7. [Performance & Scaling](#performance--scaling)
8. [Security Concerns](#security-concerns)
9. [Troubleshooting](#troubleshooting)
10. [Development Questions](#development-questions)

## General Questions

### Q: What is this VPN Telegram Bot?

**A:** This is a comprehensive Telegram bot that manages VPN services, allowing users to:
- Create trial and premium VPN accounts
- Make payments through Telegram
- Manage their VPN subscriptions
- Access VPN configurations and connection details
- Get support and assistance

The bot integrates with Marzban VPN panel and supports multiple payment methods.

### Q: What VPN protocols are supported?

**A:** The bot supports multiple VPN protocols through Marzban integration:
- **Shadowsocks**: Fast and secure proxy protocol
- **VMess**: V2Ray protocol with various transport options
- **VLESS**: Lightweight V2Ray protocol
- **Trojan**: TLS-based proxy protocol
- **WireGuard**: Modern, fast VPN protocol

### Q: How much does it cost to run this bot?

**A:** Running costs depend on your setup:
- **VPS**: $5-50/month depending on specifications
- **Domain & SSL**: $10-20/year
- **Telegram Bot**: Free
- **Database**: Included in VPS or $5-20/month for managed service
- **Monitoring Tools**: $0-50/month depending on requirements

### Q: Can I customize the bot for my needs?

**A:** Yes! The bot is designed to be highly customizable:
- Modify pricing and subscription plans
- Change bot messages and interface
- Add custom features and commands
- Integrate with different payment providers
- Customize VPN server configurations

## Installation & Setup

### Q: What are the minimum system requirements?

**A:** Minimum requirements:
- **CPU**: 1 vCPU (2+ recommended)
- **RAM**: 1GB (2GB+ recommended)
- **Storage**: 10GB SSD
- **OS**: Ubuntu 20.04+ or any Docker-compatible system
- **Network**: Stable internet connection

### Q: Can I install this on shared hosting?

**A:** No, shared hosting typically doesn't support:
- Docker containers
- Long-running processes
- Custom Python applications
- Database management

You need a VPS or dedicated server.

### Q: Do I need a domain name?

**A:** A domain is recommended but not strictly required:
- **Required for**: SSL certificates, webhook setup, admin panel
- **Optional for**: Basic bot functionality with polling
- **Recommendation**: Use a domain for production deployment

### Q: How do I get a Telegram Bot Token?

**A:** Follow these steps:
1. Message [@BotFather](https://t.me/BotFather) on Telegram
2. Send `/newbot` command
3. Choose a name and username for your bot
4. Copy the provided token
5. Keep the token secure and never share it publicly

### Q: Installation fails with permission errors?

**A:** Common solutions:
```bash
# Fix Docker permissions
sudo usermod -aG docker $USER
newgrp docker

# Fix file permissions
sudo chown -R $USER:$USER /path/to/project
chmod +x scripts/*.sh

# Use sudo for system-wide installations
sudo apt update && sudo apt install docker.io
```

## Bot Configuration

### Q: How do I configure environment variables?

**A:** Create a `.env` file in the project root:
```bash
# Copy example file
cp .env.example .env

# Edit with your values
nano .env

# Required variables
BOT_TOKEN=your_bot_token
DB_URL=postgresql://user:pass@localhost/dbname
REDIS_URL=redis://localhost:6379
MARZBAN_URL=https://your-marzban-panel.com
MARZBAN_USERNAME=admin
MARZBAN_PASSWORD=your_password
```

### Q: Bot doesn't respond to commands?

**A:** Check these common issues:
1. **Bot Token**: Verify token is correct and bot is active
2. **Webhook**: Ensure webhook URL is accessible
3. **Permissions**: Bot needs message permissions in groups
4. **Commands**: Use `/start` to initialize bot
5. **Logs**: Check application logs for errors

### Q: How do I set up webhooks?

**A:** Webhooks require HTTPS:
```python
# Set webhook URL
bot.set_webhook(
    url="https://yourdomain.com/webhook",
    certificate=open('cert.pem', 'rb')  # If using self-signed
)

# Or use ngrok for development
ngrok http 8000
# Use the HTTPS URL provided by ngrok
```

### Q: Can I change the bot's language?

**A:** Yes, the bot supports internationalization:
1. Edit language files in `bot/locales/`
2. Add new language files for additional languages
3. Set `DEFAULT_LANGUAGE` in configuration
4. Users can change language with `/language` command

## VPN Service Integration

### Q: How do I set up Marzban integration?

**A:** Follow these steps:
1. **Install Marzban** on your VPN server
2. **Create admin user** in Marzban panel
3. **Configure API access** in Marzban settings
4. **Set environment variables**:
   ```bash
   MARZBAN_URL=https://your-server.com:8000
   MARZBAN_USERNAME=admin
   MARZBAN_PASSWORD=your_password
   ```
5. **Test connection** using the health check endpoint

### Q: Marzban connection fails?

**A:** Common troubleshooting steps:
```bash
# Check Marzban is running
curl https://your-server.com:8000/api/admin/token

# Verify credentials
curl -X POST https://your-server.com:8000/api/admin/token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=admin&password=your_password"

# Check firewall
sudo ufw status
sudo ufw allow 8000

# Check SSL certificate
openssl s_client -connect your-server.com:8000
```

### Q: How do I add multiple VPN servers?

**A:** Configure multiple Marzban instances:
```python
# In bot/config.py
MARZBAN_SERVERS = [
    {
        'name': 'Server 1',
        'url': 'https://server1.com:8000',
        'username': 'admin',
        'password': 'password1',
        'location': 'US',
        'capacity': 1000
    },
    {
        'name': 'Server 2',
        'url': 'https://server2.com:8000',
        'username': 'admin',
        'password': 'password2',
        'location': 'EU',
        'capacity': 500
    }
]
```

### Q: VPN accounts are not created?

**A:** Check these issues:
1. **Marzban API**: Verify API is accessible
2. **Permissions**: Ensure bot user has admin rights
3. **Server capacity**: Check if server has available slots
4. **Configuration**: Verify inbound configurations exist
5. **Logs**: Check both bot and Marzban logs

## Payment Issues

### Q: How do I set up Telegram Payments?

**A:** Configure payment provider:
1. **Contact @BotFather**
2. **Send `/mybots`** and select your bot
3. **Choose "Payments"**
4. **Select payment provider** (Stripe, etc.)
5. **Configure provider settings**
6. **Set provider token** in environment variables

### Q: Payments fail or don't process?

**A:** Common payment issues:
```python
# Check payment configuration
PAYMENT_PROVIDER_TOKEN=your_provider_token
CURRENCY=USD  # or your preferred currency

# Verify webhook handling
@app.route('/payment_webhook', methods=['POST'])
async def payment_webhook():
    # Process payment notifications
    pass

# Check payment logs
tail -f logs/payments.log
```

### Q: Can I add custom payment methods?

**A:** Yes, implement custom payment handlers:
```python
# bot/handlers/payments/custom.py
class CustomPaymentHandler:
    async def create_invoice(self, amount, description):
        # Implement custom payment logic
        pass
    
    async def verify_payment(self, payment_id):
        # Verify payment status
        pass
```

### Q: How do I handle refunds?

**A:** Implement refund logic:
```python
# bot/services/payment.py
async def process_refund(self, payment_id, amount=None):
    # Process refund through payment provider
    # Update database records
    # Notify user
    pass
```

## User Management

### Q: How do I manage user permissions?

**A:** Use the admin panel or database:
```sql
-- Make user admin
UPDATE users SET is_admin = true WHERE user_id = *********;

-- Set user permissions
UPDATE users SET permissions = ARRAY['user.read', 'vpn.write'] 
WHERE user_id = *********;

-- Ban user
UPDATE users SET is_banned = true WHERE user_id = *********;
```

### Q: How do I reset user data?

**A:** Use admin commands or database:
```python
# Reset user trial
await user_service.reset_trial(user_id)

# Delete user VPN accounts
await vpn_service.delete_user_accounts(user_id)

# Reset user subscription
await subscription_service.cancel_subscription(user_id)
```

### Q: Can users have multiple VPN accounts?

**A:** Yes, configure in settings:
```python
# bot/config.py
MAX_ACCOUNTS_PER_USER = 3  # Allow 3 accounts per user
ALLOW_MULTIPLE_TRIALS = False  # One trial per user
```

### Q: How do I handle user complaints?

**A:** Implement support system:
1. **Support command**: `/support` for user issues
2. **Admin notifications**: Forward complaints to admins
3. **Ticket system**: Track and manage support requests
4. **User feedback**: Collect and analyze feedback

## Performance & Scaling

### Q: Bot is slow or unresponsive?

**A:** Optimize performance:
```python
# Increase worker processes
WORKERS = 4

# Use connection pooling
DB_POOL_SIZE = 20
REDIS_POOL_SIZE = 10

# Enable caching
CACHE_TTL = 300  # 5 minutes

# Optimize database queries
# Use indexes, limit results, paginate
```

### Q: How do I scale the bot?

**A:** Scaling strategies:
1. **Horizontal scaling**: Multiple bot instances
2. **Database optimization**: Read replicas, indexing
3. **Caching**: Redis for frequent data
4. **Load balancing**: Distribute webhook requests
5. **CDN**: Static content delivery

### Q: Database performance issues?

**A:** Optimize database:
```sql
-- Add indexes
CREATE INDEX idx_users_user_id ON users(user_id);
CREATE INDEX idx_vpn_accounts_user_id ON vpn_accounts(user_id);
CREATE INDEX idx_payments_created_at ON payments(created_at);

-- Analyze query performance
EXPLAIN ANALYZE SELECT * FROM users WHERE user_id = $1;

-- Regular maintenance
VACUUM ANALYZE;
REINDEX DATABASE your_db;
```

### Q: Memory usage is high?

**A:** Reduce memory usage:
```python
# Limit concurrent connections
DB_POOL_SIZE = 10
REDIS_POOL_SIZE = 5

# Use pagination
PAGE_SIZE = 50

# Clear unused data
await cache.clear_expired()

# Monitor memory usage
import psutil
print(f"Memory usage: {psutil.virtual_memory().percent}%")
```

## Security Concerns

### Q: How secure is the bot?

**A:** Security features implemented:
- **Authentication**: JWT tokens, Telegram verification
- **Authorization**: Role-based access control
- **Encryption**: Data encryption at rest and in transit
- **Input validation**: SQL injection prevention
- **Rate limiting**: Prevent abuse and DDoS
- **Logging**: Security event monitoring

### Q: How do I secure the admin panel?

**A:** Admin panel security:
```python
# Strong authentication
JWT_SECRET_KEY = "your-very-long-secret-key"
JWT_EXPIRATION = 3600  # 1 hour

# IP restrictions
ALLOWED_ADMIN_IPS = ['*************', '*********']

# Two-factor authentication
ENABLE_2FA = True

# Session management
SESSION_TIMEOUT = 1800  # 30 minutes
```

### Q: How do I protect against attacks?

**A:** Security measures:
```python
# Rate limiting
RATE_LIMIT_REQUESTS = 60  # per minute
RATE_LIMIT_BURST = 10

# Input validation
from bot.security.validation import SecurityValidator
validator = SecurityValidator()

# Monitoring
from bot.security.monitoring import SecurityMonitor
monitor = SecurityMonitor()
```

### Q: Data privacy and GDPR compliance?

**A:** Privacy measures:
- **Data minimization**: Collect only necessary data
- **Encryption**: Encrypt sensitive information
- **Anonymization**: Remove identifying information
- **Right to deletion**: Users can delete their data
- **Data export**: Users can export their data
- **Privacy policy**: Clear data usage policies

## Troubleshooting

### Q: Bot crashes frequently?

**A:** Debug crashes:
```bash
# Check logs
tail -f logs/bot.log

# Monitor system resources
top
htop
df -h

# Check for memory leaks
valgrind python main.py

# Use process manager
supervisor or systemd
```

### Q: Database connection errors?

**A:** Fix database issues:
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Test connection
psql -h localhost -U username -d database

# Check connection limits
SHOW max_connections;
SELECT count(*) FROM pg_stat_activity;

# Restart PostgreSQL
sudo systemctl restart postgresql
```

### Q: Redis connection issues?

**A:** Fix Redis problems:
```bash
# Check Redis status
redis-cli ping

# Monitor Redis
redis-cli monitor

# Check memory usage
redis-cli info memory

# Clear Redis cache
redis-cli flushall
```

### Q: SSL certificate errors?

**A:** Fix SSL issues:
```bash
# Check certificate validity
openssl x509 -in cert.pem -text -noout

# Renew Let's Encrypt certificate
certbot renew

# Test SSL configuration
ssl-checker yourdomain.com

# Update certificate in application
sudo systemctl restart your-app
```

## Development Questions

### Q: How do I contribute to the project?

**A:** Contribution guidelines:
1. **Fork the repository**
2. **Create feature branch**: `git checkout -b feature/new-feature`
3. **Follow coding standards**: PEP 8, type hints
4. **Write tests**: Unit and integration tests
5. **Update documentation**: README, guides
6. **Submit pull request**: Clear description of changes

### Q: How do I add new features?

**A:** Feature development process:
```python
# 1. Create handler
# bot/handlers/new_feature.py
class NewFeatureHandler:
    async def handle_command(self, update, context):
        pass

# 2. Add routes
# bot/main.py
app.add_handler(CommandHandler('newfeature', handler.handle_command))

# 3. Add tests
# tests/test_new_feature.py
def test_new_feature():
    pass

# 4. Update documentation
# docs/guides/features.md
```

### Q: How do I debug the bot?

**A:** Debugging techniques:
```python
# Enable debug logging
import logging
logging.basicConfig(level=logging.DEBUG)

# Use debugger
import pdb; pdb.set_trace()

# Add debug prints
print(f"Debug: {variable}")

# Use IDE debugger
# Set breakpoints in PyCharm/VSCode

# Monitor with tools
# Use Sentry for error tracking
```

### Q: Testing best practices?

**A:** Testing guidelines:
```python
# Unit tests
pytest tests/unit/

# Integration tests
pytest tests/integration/

# Coverage report
pytest --cov=bot tests/

# Mock external services
from unittest.mock import Mock, patch

# Test fixtures
@pytest.fixture
def mock_user():
    return User(id=123, username='test')
```

### Q: How do I deploy updates?

**A:** Deployment process:
```bash
# 1. Test changes
pytest

# 2. Build new image
docker build -t vpn-bot:latest .

# 3. Backup database
pg_dump database > backup.sql

# 4. Deploy with zero downtime
docker-compose up -d --no-deps app

# 5. Run migrations
docker-compose exec app python -m alembic upgrade head

# 6. Verify deployment
curl https://yourdomain.com/health
```

---

## Need More Help?

If you can't find the answer to your question:

1. **Check the documentation**: [docs/guides/](../)
2. **Search existing issues**: GitHub Issues
3. **Join our community**: Telegram group or Discord
4. **Create an issue**: Provide detailed information
5. **Contact support**: Email or direct message

### Useful Resources

- **Project Repository**: [GitHub Link]
- **Documentation**: [docs/guides/README.md](README.md)
- **Telegram Bot API**: https://core.telegram.org/bots/api
- **Marzban Documentation**: https://github.com/Gozargah/Marzban
- **Python Telegram Bot**: https://python-telegram-bot.org/

### Support Channels

- **GitHub Issues**: Bug reports and feature requests
- **Telegram Group**: Community support and discussions
- **Email Support**: Direct technical support
- **Documentation**: Comprehensive guides and tutorials

Remember to provide detailed information when asking for help:
- Bot version and configuration
- Error messages and logs
- Steps to reproduce the issue
- System information (OS, Python version, etc.)