"""Referral analytics and tracking service."""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from bot.database import get_db_connection


class ReferralAnalyticsService:
    """Service for referral analytics and tracking."""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def get_comprehensive_analytics(self, days: int = 30) -> Dict[str, Any]:
        """Get comprehensive referral analytics for the specified period."""
        try:
            async with get_db_connection() as conn:
                # Date range
                start_date = datetime.now() - timedelta(days=days)
                
                # Basic statistics
                total_referrals = await conn.fetchval(
                    "SELECT COUNT(*) FROM referrals WHERE created_at >= $1",
                    start_date
                )
                
                completed_referrals = await conn.fetchval(
                    "SELECT COUNT(*) FROM referrals WHERE created_at >= $1 AND status = 'completed'",
                    start_date
                )
                
                pending_referrals = await conn.fetchval(
                    "SELECT COUNT(*) FROM referrals WHERE created_at >= $1 AND status = 'pending'",
                    start_date
                )
                
                # Conversion rate
                conversion_rate = (completed_referrals / total_referrals * 100) if total_referrals > 0 else 0
                
                # Top referrers
                top_referrers = await conn.fetch(
                    """
                    SELECT u.telegram_id, u.username, u.first_name, 
                           COUNT(r.id) as referrals_count,
                           SUM(CASE WHEN r.status = 'completed' THEN 1 ELSE 0 END) as completed_count,
                           u.total_referral_rewards
                    FROM users u
                    JOIN referrals r ON u.id = r.referrer_id
                    WHERE r.created_at >= $1
                    GROUP BY u.id, u.telegram_id, u.username, u.first_name, u.total_referral_rewards
                    ORDER BY referrals_count DESC
                    LIMIT 10
                    """,
                    start_date
                )
                
                # Daily referral trends
                daily_trends = await conn.fetch(
                    """
                    SELECT DATE(created_at) as date,
                           COUNT(*) as total_referrals,
                           SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_referrals
                    FROM referrals
                    WHERE created_at >= $1
                    GROUP BY DATE(created_at)
                    ORDER BY date DESC
                    """,
                    start_date
                )
                
                # Reward distribution
                reward_stats = await conn.fetch(
                    """
                    SELECT reward_type, 
                           COUNT(*) as count,
                           SUM(reward_amount) as total_amount,
                           AVG(reward_amount) as avg_amount
                    FROM referral_rewards
                    WHERE created_at >= $1
                    GROUP BY reward_type
                    """,
                    start_date
                )
                
                # Most active referral codes
                active_codes = await conn.fetch(
                    """
                    SELECT rc.code, rc.uses_count, u.username, u.first_name
                    FROM referral_codes rc
                    JOIN users u ON rc.user_id = u.id
                    WHERE rc.uses_count > 0
                    ORDER BY rc.uses_count DESC
                    LIMIT 10
                    """,
                )
                
                return {
                    'period_days': days,
                    'basic_stats': {
                        'total_referrals': total_referrals or 0,
                        'completed_referrals': completed_referrals or 0,
                        'pending_referrals': pending_referrals or 0,
                        'conversion_rate': round(conversion_rate, 2)
                    },
                    'top_referrers': [dict(row) for row in top_referrers],
                    'daily_trends': [dict(row) for row in daily_trends],
                    'reward_stats': [dict(row) for row in reward_stats],
                    'active_codes': [dict(row) for row in active_codes]
                }
        
        except Exception as e:
            self.logger.error(f"Error getting comprehensive analytics: {e}")
            return {}
    
    async def get_user_referral_performance(self, user_id: int) -> Dict[str, Any]:
        """Get detailed referral performance for a specific user."""
        try:
            async with get_db_connection() as conn:
                # User basic info
                user_info = await conn.fetchrow(
                    "SELECT telegram_id, username, first_name, referral_code, referral_count, total_referral_rewards FROM users WHERE id = $1",
                    user_id
                )
                
                if not user_info:
                    return {}
                
                # Referral timeline
                referral_timeline = await conn.fetch(
                    """
                    SELECT r.*, u.username as referred_username, u.first_name as referred_name
                    FROM referrals r
                    JOIN users u ON r.referred_id = u.id
                    WHERE r.referrer_id = $1
                    ORDER BY r.created_at DESC
                    """,
                    user_id
                )
                
                # Rewards earned
                rewards_earned = await conn.fetch(
                    "SELECT * FROM referral_rewards WHERE user_id = $1 ORDER BY created_at DESC",
                    user_id
                )
                
                # Monthly performance
                monthly_performance = await conn.fetch(
                    """
                    SELECT DATE_TRUNC('month', created_at) as month,
                           COUNT(*) as referrals_count,
                           SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_count
                    FROM referrals
                    WHERE referrer_id = $1
                    GROUP BY DATE_TRUNC('month', created_at)
                    ORDER BY month DESC
                    LIMIT 12
                    """,
                    user_id
                )
                
                return {
                    'user_info': dict(user_info),
                    'referral_timeline': [dict(row) for row in referral_timeline],
                    'rewards_earned': [dict(row) for row in rewards_earned],
                    'monthly_performance': [dict(row) for row in monthly_performance]
                }
        
        except Exception as e:
            self.logger.error(f"Error getting user referral performance for {user_id}: {e}")
            return {}
    
    async def track_referral_event(self, event_type: str, user_id: int, referral_id: Optional[int] = None, metadata: Optional[Dict] = None) -> bool:
        """Track referral-related events for analytics."""
        try:
            async with get_db_connection() as conn:
                await conn.execute(
                    """
                    INSERT INTO referral_events (event_type, user_id, referral_id, metadata, created_at)
                    VALUES ($1, $2, $3, $4, NOW())
                    """,
                    event_type, user_id, referral_id, metadata
                )
                return True
        
        except Exception as e:
            self.logger.error(f"Error tracking referral event: {e}")
            return False
    
    async def get_referral_funnel_analysis(self) -> Dict[str, Any]:
        """Analyze the referral funnel conversion rates."""
        try:
            async with get_db_connection() as conn:
                # Get funnel data
                total_codes_created = await conn.fetchval(
                    "SELECT COUNT(*) FROM referral_codes"
                )
                
                codes_with_clicks = await conn.fetchval(
                    "SELECT COUNT(DISTINCT code) FROM referral_events WHERE event_type = 'referral_link_clicked'"
                )
                
                total_referrals = await conn.fetchval(
                    "SELECT COUNT(*) FROM referrals"
                )
                
                completed_referrals = await conn.fetchval(
                    "SELECT COUNT(*) FROM referrals WHERE status = 'completed'"
                )
                
                rewarded_referrals = await conn.fetchval(
                    "SELECT COUNT(*) FROM referrals WHERE status = 'rewarded'"
                )
                
                # Calculate conversion rates
                click_rate = (codes_with_clicks / total_codes_created * 100) if total_codes_created > 0 else 0
                signup_rate = (total_referrals / codes_with_clicks * 100) if codes_with_clicks > 0 else 0
                completion_rate = (completed_referrals / total_referrals * 100) if total_referrals > 0 else 0
                reward_rate = (rewarded_referrals / completed_referrals * 100) if completed_referrals > 0 else 0
                
                return {
                    'funnel_steps': {
                        'codes_created': total_codes_created or 0,
                        'codes_clicked': codes_with_clicks or 0,
                        'referrals_started': total_referrals or 0,
                        'referrals_completed': completed_referrals or 0,
                        'referrals_rewarded': rewarded_referrals or 0
                    },
                    'conversion_rates': {
                        'click_rate': round(click_rate, 2),
                        'signup_rate': round(signup_rate, 2),
                        'completion_rate': round(completion_rate, 2),
                        'reward_rate': round(reward_rate, 2)
                    }
                }
        
        except Exception as e:
            self.logger.error(f"Error getting funnel analysis: {e}")
            return {}
    
    async def get_referral_leaderboard(self, period_days: int = 30, limit: int = 50) -> List[Dict[str, Any]]:
        """Get referral leaderboard for specified period."""
        try:
            async with get_db_connection() as conn:
                start_date = datetime.now() - timedelta(days=period_days)
                
                leaderboard = await conn.fetch(
                    """
                    SELECT u.telegram_id, u.username, u.first_name, u.referral_code,
                           COUNT(r.id) as period_referrals,
                           SUM(CASE WHEN r.status = 'completed' THEN 1 ELSE 0 END) as period_completed,
                           u.referral_count as total_referrals,
                           u.total_referral_rewards
                    FROM users u
                    LEFT JOIN referrals r ON u.id = r.referrer_id AND r.created_at >= $1
                    WHERE u.referral_count > 0
                    GROUP BY u.id, u.telegram_id, u.username, u.first_name, u.referral_code, u.referral_count, u.total_referral_rewards
                    ORDER BY period_referrals DESC, total_referrals DESC
                    LIMIT $2
                    """,
                    start_date, limit
                )
                
                return [dict(row) for row in leaderboard]
        
        except Exception as e:
            self.logger.error(f"Error getting referral leaderboard: {e}")
            return []


# Global instance
referral_analytics_service = ReferralAnalyticsService()
