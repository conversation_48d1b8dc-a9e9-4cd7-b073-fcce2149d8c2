<?php

declare(strict_types=1);

namespace VpnBot\Bot\Commands;

use <PERSON><PERSON>\TelegramBot\Commands\SystemCommand;
use <PERSON><PERSON>\TelegramBot\Entities\ServerResponse;
use VpnBot\Bot\Handlers\MessageHandler;
use VpnBot\Config\Config;
use VpnBot\Utils\Logger;

class GenericmessageCommand extends SystemCommand
{
    protected $name = 'genericmessage';
    protected $description = 'Handle generic messages';
    protected $version = '1.0.0';

    public function execute(): ServerResponse
    {
        $message = $this->getMessage();
        $user = $message->getFrom();
        $chat = $message->getChat();
        $text = $message->getText();

        // Initialize services
        $config = Config::getInstance();
        $logger = Logger::getInstance();
        $messageHandler = new MessageHandler($config, $logger);

        // Handle the message
        return $messageHandler->handleMessage(
            $chat->getId(),
            $user->getId(),
            $text,
            $user->getFirstName() ?? ''
        );
    }
}
