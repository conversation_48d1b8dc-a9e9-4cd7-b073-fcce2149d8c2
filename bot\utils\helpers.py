"""Helper functions and utilities for the bot."""

import re
import json
import hashlib
import secrets
import string
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any, Union
import logging

logger = logging.getLogger(__name__)


class DateTimeHelper:
    """Helper class for datetime operations."""
    
    @staticmethod
    def now_utc() -> datetime:
        """Get current UTC datetime."""
        return datetime.now(timezone.utc)
    
    @staticmethod
    def add_days(dt: datetime, days: int) -> datetime:
        """Add days to datetime."""
        return dt + timedelta(days=days)
    
    @staticmethod
    def add_hours(dt: datetime, hours: int) -> datetime:
        """Add hours to datetime."""
        return dt + timedelta(hours=hours)
    
    @staticmethod
    def format_datetime(dt: datetime, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
        """Format datetime to string."""
        return dt.strftime(format_str)
    
    @staticmethod
    def parse_datetime(dt_str: str, format_str: str = "%Y-%m-%d %H:%M:%S") -> Optional[datetime]:
        """Parse string to datetime."""
        try:
            return datetime.strptime(dt_str, format_str)
        except ValueError:
            return None
    
    @staticmethod
    def is_expired(dt: datetime) -> bool:
        """Check if datetime is in the past."""
        return dt < DateTimeHelper.now_utc()
    
    @staticmethod
    def time_until_expiry(dt: datetime) -> timedelta:
        """Get time remaining until expiry."""
        return dt - DateTimeHelper.now_utc()
    
    @staticmethod
    def format_time_remaining(dt: datetime, language: str = 'en') -> str:
        """Format time remaining in human-readable format."""
        # Removed circular import - get_text is defined in this file
        
        if DateTimeHelper.is_expired(dt):
            return get_text(language, 'time.expired')
        
        remaining = DateTimeHelper.time_until_expiry(dt)
        days = remaining.days
        hours, remainder = divmod(remaining.seconds, 3600)
        minutes, _ = divmod(remainder, 60)
        
        if days > 0:
            return get_text(language, 'time.days_hours', days=days, hours=hours)
        elif hours > 0:
            return get_text(language, 'time.hours_minutes', hours=hours, minutes=minutes)
        else:
            return get_text(language, 'time.minutes', minutes=minutes)


class TextHelper:
    """Helper class for text operations."""
    
    @staticmethod
    def escape_markdown(text: str) -> str:
        """Escape special characters for Markdown."""
        escape_chars = r'_*[]()~`>#+-=|{}.!'
        return re.sub(f'([{re.escape(escape_chars)}])', r'\\\1', text)
    
    @staticmethod
    def truncate_text(text: str, max_length: int, suffix: str = "...") -> str:
        """Truncate text to maximum length."""
        if len(text) <= max_length:
            return text
        return text[:max_length - len(suffix)] + suffix
    
    @staticmethod
    def clean_username(username: str) -> str:
        """Clean and validate username."""
        # Remove @ if present
        username = username.lstrip('@')
        # Keep only alphanumeric and underscores
        username = re.sub(r'[^a-zA-Z0-9_]', '', username)
        return username.lower()
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """Format file size in human-readable format."""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
    
    @staticmethod
    def format_number(number: Union[int, float], language: str = 'en') -> str:
        """Format number with proper separators."""
        if language == 'fa':
            # Persian number formatting
            formatted = f"{number:,}"
            # Replace English digits with Persian
            persian_digits = '۰۱۲۳۴۵۶۷۸۹'
            english_digits = '0123456789'
            for eng, per in zip(english_digits, persian_digits):
                formatted = formatted.replace(eng, per)
            return formatted
        else:
            return f"{number:,}"
    
    @staticmethod
    def extract_command_args(text: str) -> List[str]:
        """Extract arguments from command text."""
        parts = text.split()
        return parts[1:] if len(parts) > 1 else []
    
    @staticmethod
    def is_valid_email(email: str) -> bool:
        """Validate email address."""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def is_valid_url(url: str) -> bool:
        """Validate URL."""
        pattern = r'^https?://(?:[-\w.])+(?:[:\d]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:#(?:\w*))?)?$'
        return re.match(pattern, url) is not None


class SecurityHelper:
    """Helper class for security operations."""
    
    @staticmethod
    def generate_random_string(length: int = 32, include_symbols: bool = False) -> str:
        """Generate a random string."""
        chars = string.ascii_letters + string.digits
        if include_symbols:
            chars += string.punctuation
        return ''.join(secrets.choice(chars) for _ in range(length))
    
    @staticmethod
    def generate_username(prefix: str = "user", length: int = 8) -> str:
        """Generate a random username."""
        random_part = SecurityHelper.generate_random_string(length, include_symbols=False)
        return f"{prefix}_{random_part}".lower()
    
    @staticmethod
    def hash_string(text: str, algorithm: str = 'sha256') -> str:
        """Hash a string using specified algorithm."""
        hash_obj = hashlib.new(algorithm)
        hash_obj.update(text.encode('utf-8'))
        return hash_obj.hexdigest()
    
    @staticmethod
    def generate_token(length: int = 32) -> str:
        """Generate a secure random token."""
        return secrets.token_urlsafe(length)
    
    @staticmethod
    def mask_sensitive_data(data: str, visible_chars: int = 4) -> str:
        """Mask sensitive data showing only first/last characters."""
        if len(data) <= visible_chars * 2:
            return '*' * len(data)
        
        start = data[:visible_chars]
        end = data[-visible_chars:]
        middle = '*' * (len(data) - visible_chars * 2)
        return f"{start}{middle}{end}"


class ValidationHelper:
    """Helper class for data validation."""
    
    @staticmethod
    def validate_user_id(user_id: Any) -> bool:
        """Validate Telegram user ID."""
        try:
            uid = int(user_id)
            return uid > 0
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def validate_plan_data(plan_data: Dict[str, Any]) -> List[str]:
        """Validate premium plan data."""
        errors = []
        
        required_fields = ['name', 'price', 'duration_days', 'data_limit_gb']
        for field in required_fields:
            if field not in plan_data:
                errors.append(f"Missing required field: {field}")
        
        if 'price' in plan_data:
            try:
                price = float(plan_data['price'])
                if price <= 0:
                    errors.append("Price must be positive")
            except (ValueError, TypeError):
                errors.append("Invalid price format")
        
        if 'duration_days' in plan_data:
            try:
                days = int(plan_data['duration_days'])
                if days <= 0:
                    errors.append("Duration must be positive")
            except (ValueError, TypeError):
                errors.append("Invalid duration format")
        
        if 'data_limit_gb' in plan_data:
            try:
                data_limit = float(plan_data['data_limit_gb'])
                if data_limit <= 0:
                    errors.append("Data limit must be positive")
            except (ValueError, TypeError):
                errors.append("Invalid data limit format")
        
        return errors
    
    @staticmethod
    def validate_vpn_config(config_data: Dict[str, Any]) -> List[str]:
        """Validate VPN configuration data."""
        errors = []
        
        required_fields = ['username', 'subscription_url']
        for field in required_fields:
            if field not in config_data:
                errors.append(f"Missing required field: {field}")
        
        if 'username' in config_data:
            username = config_data['username']
            if not isinstance(username, str) or len(username) < 3:
                errors.append("Username must be at least 3 characters")
        
        if 'subscription_url' in config_data:
            url = config_data['subscription_url']
            if not TextHelper.is_valid_url(url):
                errors.append("Invalid subscription URL")
        
        return errors


class CacheHelper:
    """Helper class for caching operations."""
    
    def __init__(self):
        self._cache: Dict[str, Dict[str, Any]] = {}
    
    def set(self, key: str, value: Any, ttl_seconds: int = 300) -> None:
        """Set cache value with TTL."""
        expiry = DateTimeHelper.now_utc() + timedelta(seconds=ttl_seconds)
        self._cache[key] = {
            'value': value,
            'expiry': expiry
        }
    
    def get(self, key: str) -> Optional[Any]:
        """Get cache value if not expired."""
        if key not in self._cache:
            return None
        
        cache_item = self._cache[key]
        if DateTimeHelper.now_utc() > cache_item['expiry']:
            del self._cache[key]
            return None
        
        return cache_item['value']
    
    def delete(self, key: str) -> bool:
        """Delete cache key."""
        if key in self._cache:
            del self._cache[key]
            return True
        return False
    
    def clear_expired(self) -> int:
        """Clear all expired cache entries."""
        now = DateTimeHelper.now_utc()
        expired_keys = [
            key for key, item in self._cache.items()
            if now > item['expiry']
        ]
        
        for key in expired_keys:
            del self._cache[key]
        
        return len(expired_keys)
    
    def clear_all(self) -> None:
        """Clear all cache entries."""
        self._cache.clear()


class ConfigHelper:
    """Helper class for configuration management."""
    
    @staticmethod
    def load_json_config(file_path: str) -> Optional[Dict[str, Any]]:
        """Load configuration from JSON file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            logger.error(f"Error loading config from {file_path}: {e}")
            return None
    
    @staticmethod
    def save_json_config(data: Dict[str, Any], file_path: str) -> bool:
        """Save configuration to JSON file."""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            logger.error(f"Error saving config to {file_path}: {e}")
            return False
    
    @staticmethod
    def get_env_var(var_name: str, default: Any = None, var_type: type = str) -> Any:
        """Get environment variable with type conversion."""
        import os
        
        value = os.getenv(var_name, default)
        
        if value is None:
            return default
        
        try:
            if var_type == bool:
                return value.lower() in ('true', '1', 'yes', 'on')
            elif var_type == int:
                return int(value)
            elif var_type == float:
                return float(value)
            else:
                return str(value)
        except (ValueError, TypeError):
            logger.warning(f"Invalid type conversion for {var_name}: {value}")
            return default


class MessageHelper:
    """Helper class for message formatting."""
    
    @staticmethod
    def create_progress_bar(current: int, total: int, length: int = 10) -> str:
        """Create a text progress bar."""
        if total == 0:
            return "█" * length
        
        filled = int(length * current / total)
        bar = "█" * filled + "░" * (length - filled)
        percentage = int(100 * current / total)
        return f"{bar} {percentage}%"
    
    @staticmethod
    def format_user_mention(user_id: int, name: str) -> str:
        """Format user mention for Telegram."""
        return f"[{TextHelper.escape_markdown(name)}](tg://user?id={user_id})"
    
    @staticmethod
    def create_inline_url(text: str, url: str) -> str:
        """Create inline URL for Telegram."""
        return f"[{TextHelper.escape_markdown(text)}]({url})"
    
    @staticmethod
    def format_code_block(code: str, language: str = "") -> str:
        """Format code block for Telegram."""
        return f"```{language}\n{code}\n```"
    
    @staticmethod
    def format_inline_code(code: str) -> str:
        """Format inline code for Telegram."""
        return f"`{code}`"


def get_text(key: str, language: str = 'en', **kwargs) -> str:
    """Get localized text from language files.
    
    Args:
        key: The key in format 'section.subsection.key'
        language: Language code (en, fa, ru, zh)
        **kwargs: Format arguments for the text
    
    Returns:
        Localized text string
    """
    import os
    import json
    from pathlib import Path
    
    try:
        # Get the bot directory path
        bot_dir = Path(__file__).parent.parent
        locales_dir = bot_dir / 'locales'
        
        # Load language file
        lang_file = locales_dir / f'{language}.json'
        if not lang_file.exists():
            # Fallback to English
            lang_file = locales_dir / 'en.json'
            if not lang_file.exists():
                return f"Missing localization: {key}"
        
        with open(lang_file, 'r', encoding='utf-8') as f:
            translations = json.load(f)
        
        # Navigate through nested keys
        keys = key.split('.')
        text = translations
        
        for k in keys:
            if isinstance(text, dict) and k in text:
                text = text[k]
            else:
                # Key not found, return fallback
                return f"Missing translation: {key}"
        
        # Format text with provided arguments
        if isinstance(text, str) and kwargs:
            try:
                return text.format(**kwargs)
            except (KeyError, ValueError):
                return text
        
        return str(text) if text is not None else f"Empty translation: {key}"
        
    except Exception as e:
        logger.error(f"Error getting localized text for key '{key}' in language '{language}': {e}")
        return f"Translation error: {key}"


class ChannelHelper:
    """Helper class for channel operations."""
    
    @staticmethod
    def normalize_channel_id(channel_id: str) -> str:
        """Normalize channel ID by removing @ symbol if present.
        
        Args:
            channel_id: Channel ID that may or may not have @ symbol
            
        Returns:
            Channel ID without @ symbol
        """
        if not channel_id:
            return channel_id
            
        # Remove @ symbol if present
        return channel_id.lstrip('@')
    
    @staticmethod
    def format_channel_id(channel_id: str, include_at: bool = True) -> str:
        """Format channel ID with or without @ symbol.
        
        Args:
            channel_id: Channel ID
            include_at: Whether to include @ symbol
            
        Returns:
            Formatted channel ID
        """
        if not channel_id:
            return channel_id
            
        # Normalize first (remove @)
        normalized = ChannelHelper.normalize_channel_id(channel_id)
        
        # Don't add @ to numeric chat IDs
        if normalized.lstrip('-').isdigit():
            return normalized
            
        # Add @ if requested for username-based channels
        if include_at:
            return f"@{normalized}"
        
        return normalized
    
    @staticmethod
    def is_valid_channel_id(channel_id: str) -> bool:
        """Check if channel ID is valid.
        
        Args:
            channel_id: Channel ID to validate
            
        Returns:
            True if valid, False otherwise
        """
        if not channel_id:
            return False
            
        # Remove @ if present for validation
        normalized = ChannelHelper.normalize_channel_id(channel_id)
        
        # Check if it's a valid username (alphanumeric + underscore, 5-32 chars)
        # or a numeric chat ID
        if normalized.isdigit() or normalized.startswith('-'):
            # Numeric chat ID
            return True
            
        # Username validation
        if len(normalized) < 5 or len(normalized) > 32:
            return False
            
        # Must start with letter and contain only letters, digits, underscores
        return re.match(r'^[a-zA-Z][a-zA-Z0-9_]*$', normalized) is not None


# Global instances
cache_helper = CacheHelper()
channel_helper = ChannelHelper()