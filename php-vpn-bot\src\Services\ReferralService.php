<?php

declare(strict_types=1);

namespace VpnBot\Services;

use VpnBot\Config\Config;
use VpnBot\Models\User;
use VpnBot\Models\Referral;
use Psr\Log\LoggerInterface;

class ReferralService
{
    private Config $config;
    private LoggerInterface $logger;

    public function __construct(Config $config, LoggerInterface $logger)
    {
        $this->config = $config;
        $this->logger = $logger;
    }

    public function processReferral(int $userId, string $referralCode): bool
    {
        try {
            $user = User::findById($userId);
            if (!$user || $user->referred_by !== null) {
                // User not found or already has a referrer
                return false;
            }

            // Find the referrer by referral code
            $referrer = $this->findUserByReferralCode($referralCode);
            if (!$referrer || $referrer->id === $user->id) {
                // Referrer not found or user trying to refer themselves
                return false;
            }

            // Set the referral relationship
            $user->referred_by = $referrer->id;
            $user->save();

            // Update referrer stats
            $referrer->referral_count++;
            $referrer->save();

            // Create referral record
            $referral = new Referral();
            $referral->referrer_id = $referrer->id;
            $referral->referred_id = $user->id;
            $referral->referral_code = $referralCode;
            $referral->status = 'active';
            $referral->save();

            $this->logger->info("Referral processed: User {$user->id} referred by {$referrer->id}");
            return true;

        } catch (\Exception $e) {
            $this->logger->error('Error processing referral: ' . $e->getMessage());
            return false;
        }
    }

    public function generateReferralLink(int $userId): ?string
    {
        try {
            $user = User::findById($userId);
            if (!$user) {
                return null;
            }

            $referralCode = $user->generateReferralCode();
            $user->save();

            $botUsername = $this->config->get('bot.username');
            return "https://t.me/{$botUsername}?start=ref_{$referralCode}";

        } catch (\Exception $e) {
            $this->logger->error('Error generating referral link: ' . $e->getMessage());
            return null;
        }
    }

    public function getReferralStats(int $userId): array
    {
        try {
            $user = User::findById($userId);
            if (!$user) {
                return [];
            }

            $referrals = $this->getUserReferrals($userId);
            $totalRewards = $this->calculateTotalRewards($userId);

            return [
                'referral_code' => $user->referral_code,
                'referral_link' => $this->generateReferralLink($userId),
                'total_referrals' => $user->referral_count,
                'active_referrals' => count(array_filter($referrals, fn($r) => $r['status'] === 'active')),
                'total_rewards' => $totalRewards,
                'pending_rewards' => $this->getPendingRewards($userId),
                'referrals' => $referrals
            ];

        } catch (\Exception $e) {
            $this->logger->error('Error getting referral stats: ' . $e->getMessage());
            return [];
        }
    }

    private function findUserByReferralCode(string $referralCode): ?User
    {
        try {
            // Extract user ID from referral code (format: ref_telegramId_hash)
            if (preg_match('/^ref_(\d+)_[a-f0-9]+$/', $referralCode, $matches)) {
                $telegramId = (int)$matches[1];
                $user = User::findByTelegramId($telegramId);
                
                if ($user && $user->referral_code === $referralCode) {
                    return $user;
                }
            }

            return null;

        } catch (\Exception $e) {
            $this->logger->error('Error finding user by referral code: ' . $e->getMessage());
            return null;
        }
    }

    private function getUserReferrals(int $userId): array
    {
        try {
            // In a real implementation, this would query the referrals table
            // For now, return mock data
            return [];

        } catch (\Exception $e) {
            $this->logger->error('Error getting user referrals: ' . $e->getMessage());
            return [];
        }
    }

    private function calculateTotalRewards(int $userId): int
    {
        try {
            $user = User::findById($userId);
            return $user ? $user->total_referral_rewards : 0;

        } catch (\Exception $e) {
            $this->logger->error('Error calculating total rewards: ' . $e->getMessage());
            return 0;
        }
    }

    private function getPendingRewards(int $userId): int
    {
        try {
            // In a real implementation, this would calculate pending rewards
            // based on referral activity and reward rules
            return 0;

        } catch (\Exception $e) {
            $this->logger->error('Error getting pending rewards: ' . $e->getMessage());
            return 0;
        }
    }

    public function claimRewards(int $userId): array
    {
        try {
            $pendingRewards = $this->getPendingRewards($userId);
            
            if ($pendingRewards <= 0) {
                return [
                    'success' => false,
                    'message' => 'No rewards to claim',
                    'amount' => 0
                ];
            }

            $user = User::findById($userId);
            if (!$user) {
                return [
                    'success' => false,
                    'message' => 'User not found',
                    'amount' => 0
                ];
            }

            // In a real implementation, this would:
            // 1. Transfer rewards to user account
            // 2. Update reward records
            // 3. Send notification

            $user->total_referral_rewards += $pendingRewards;
            $user->save();

            return [
                'success' => true,
                'message' => 'Rewards claimed successfully',
                'amount' => $pendingRewards
            ];

        } catch (\Exception $e) {
            $this->logger->error('Error claiming rewards: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error claiming rewards',
                'amount' => 0
            ];
        }
    }

    public function getTopReferrers(int $limit = 10): array
    {
        try {
            // In a real implementation, this would query the database
            // for users with the highest referral counts
            return [];

        } catch (\Exception $e) {
            $this->logger->error('Error getting top referrers: ' . $e->getMessage());
            return [];
        }
    }
}
