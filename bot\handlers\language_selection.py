"""Language selection handler for new users."""

import logging
from telegram import Update
from telegram.ext import ContextTypes
from telegram.error import TelegramError
from bot.utils.helpers import get_text
from bot.services.auth_service import auth_service


logger = logging.getLogger(__name__)


class LanguageSelectionHandler:
    """Handler for language selection for new users."""

    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)

    async def show_language_selection(self, update: Update,
                                      _context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show language selection menu for new users."""
        try:
            # Available languages
            languages = {
                'en': '🇺🇸 English',
                'fa': '🇮🇷 فارسی',
                'ru': '🇷🇺 Русский',
                'zh': '🇨🇳 中文'
            }

            # Welcome message using locale files
            welcome_text = (
                f"{get_text('language.welcome', 'en')}\n"
                f"{get_text('language.welcome', 'fa')}\n"
                f"{get_text('language.welcome', 'ru')}\n"
                f"{get_text('language.welcome', 'zh')}"
            )
            
            # Add language options to text
            welcome_text += "\n\n📋 Available languages:\n"
            for lang_code, lang_name in languages.items():
                welcome_text += f"• {lang_name}\n"
            
            welcome_text += "\n💡 Please use the reply keyboard buttons to select your language or navigate."

            if update.callback_query:
                await update.callback_query.message.reply_text(
                    text=welcome_text,
                    parse_mode='Markdown'
                )
            else:
                await update.message.reply_text(
                    text=welcome_text,
                    parse_mode='Markdown'
                )

        except TelegramError as e:
            self.logger.error("Error showing language selection: %s", e, exc_info=True)
            await update.effective_message.reply_text(
                "❌ Error showing language selection. Please try again."
            )

    async def handle_language_selection(self, update: Update,
                                        context: ContextTypes.DEFAULT_TYPE, language_code: str) -> None:
        """Handle language selection from reply keyboard."""
        try:
            user_id = update.effective_user.id

            # Set user language in database using auth service
            success = await auth_service.update_user_language(user_id, language_code)

            if success:
                # Update context
                if 'user' not in context.user_data:
                    context.user_data['user'] = {}
                context.user_data['user']['language_code'] = language_code
                context.user_data['needs_language_selection'] = False
                context.user_data['navigation_state'] = 'main_menu'

                # Import here to avoid circular imports
                from bot.utils.buttons import ReplyKeyboardBuilder
                
                # Get localized success message and immediately update keyboard
                success_message = f"✅ {get_text('language.changed', language_code)}\n\n{get_text('welcome.description', language_code)}"
                reply_keyboard = ReplyKeyboardBuilder.create_main_menu(language_code)
                
                await update.message.reply_text(
                    success_message,
                    reply_markup=reply_keyboard,
                    parse_mode='Markdown'
                )
                
                # Note: Inline keyboard removed - all navigation now uses reply keyboard
                return

            else:
                # Language update failed
                await update.message.reply_text(
                    "❌ Failed to update language. Please try again."
                )

        except Exception as e:
            self.logger.error("Error handling language selection: %s", e, exc_info=True)
            await update.message.reply_text(
                "❌ Error updating language. Please try again."
            )


# Global instance
language_selection_handler = LanguageSelectionHandler()
