import axios, { AxiosResponse } from 'axios';
import { useMutation, useQuery, useQueryClient, UseQueryResult, UseMutationResult } from '@tanstack/react-query';
import {
  User,
  VPNPanel,
  PremiumPlan,
  BotSettings,
  DashboardStats,
  CreatePanelRequest,
  UpdatePanelRequest,
  CreatePlanRequest,
  UpdatePlanRequest,
  UpdateSettingsRequest,
  UpdateUserRequest,
} from '../types/api';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: process.env.REACT_APP_API_BASE_URL || '',
  headers: {
    'Content-Type': 'application/json',
  },
  auth: {
    username: process.env.REACT_APP_ADMIN_USERNAME || 'admin',
    password: process.env.REACT_APP_ADMIN_PASSWORD || 'your_secure_admin_password',
  },
  timeout: 30000,
  withCredentials: false,
});

// Add request interceptor for error handling
api.interceptors.request.use(
  (config) => {
    console.log(`Making ${config.method?.toUpperCase()} request to ${config.url}`);
    return config;
  },
  (error) => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    console.log(`Response from ${response.config.url}:`, response.status);
    return response;
  },
  (error) => {
    console.error('Response error:', error.response?.data || error.message);
    
    // Handle different error types
    if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
      console.error('Cannot connect to backend server. Please check if the server is running.');
      error.message = 'Cannot connect to server. Please check your connection and try again.';
    } else if (error.response?.status === 401) {
      console.error('Authentication failed. Please check your credentials.');
      error.message = 'Authentication failed. Please check your admin credentials.';
    } else if (error.response?.status === 403) {
      console.error('Access forbidden. Insufficient permissions.');
      error.message = 'Access denied. You do not have permission to perform this action.';
    } else if (error.response?.status === 404) {
      console.error('Resource not found.');
      error.message = 'The requested resource was not found.';
    } else if (error.response?.status >= 500) {
      console.error('Server error occurred.');
      error.message = 'Server error occurred. Please try again later.';
    } else if (error.code === 'ECONNABORTED') {
      console.error('Request timeout.');
      error.message = 'Request timeout. Please try again.';
    }
    
    return Promise.reject(error);
  }
);

// API functions
const apiService = {
  // Settings
  getSettings: (): Promise<AxiosResponse<BotSettings>> => api.get('/api/settings'),
  updateSettings: (data: UpdateSettingsRequest): Promise<AxiosResponse<BotSettings>> => 
    api.put('/api/settings', data),

  // VPN Panels
  getPanels: (): Promise<AxiosResponse<VPNPanel[]>> => api.get('/api/panels'),
  createPanel: (data: CreatePanelRequest): Promise<AxiosResponse<VPNPanel>> => 
    api.post('/api/panels', data),
  updatePanel: (id: number, data: UpdatePanelRequest): Promise<AxiosResponse<VPNPanel>> => 
    api.put(`/api/panels/${id}`, data),
  deletePanel: (id: number): Promise<AxiosResponse<void>> => 
    api.delete(`/api/panels/${id}`),

  // Premium Plans
  getPlans: (): Promise<AxiosResponse<PremiumPlan[]>> => api.get('/api/premium_plans'),
  createPlan: (data: CreatePlanRequest): Promise<AxiosResponse<PremiumPlan>> => 
    api.post('/api/premium_plans', data),
  updatePlan: (id: number, data: UpdatePlanRequest): Promise<AxiosResponse<PremiumPlan>> => 
    api.put(`/api/premium_plans/${id}`, data),
  deletePlan: (id: number): Promise<AxiosResponse<void>> => 
    api.delete(`/api/premium_plans/${id}`),

  // Users
  getUsers: (): Promise<AxiosResponse<User[]>> => api.get('/api/users'),
  getUser: (id: number): Promise<AxiosResponse<User>> => api.get(`/api/users/${id}`),
  updateUser: (id: number, data: UpdateUserRequest): Promise<AxiosResponse<User>> => 
    api.put(`/api/users/${id}`, data),
  deleteUser: (id: number): Promise<AxiosResponse<void>> => 
    api.delete(`/api/users/${id}`),

  // User management
  resetUserVpnUsage: (userId: number): Promise<AxiosResponse<void>> => 
    api.post(`/api/users/${userId}/reset-vpn-usage`),
  
  resetUserTrialStatus: (userId: number): Promise<AxiosResponse<void>> => 
    api.post(`/api/users/${userId}/reset-trial-status`),
  
  resetUserTrial: (userId: number): Promise<AxiosResponse<void>> => 
    api.post(`/api/users/${userId}/reset-trial`),
  
  bulkResetTrials: (userIds: number[]): Promise<AxiosResponse<void>> => 
    api.post('/api/users/bulk-reset-trials', { user_ids: userIds }),

  // Dashboard
  getDashboardStats: (): Promise<AxiosResponse<DashboardStats>> => api.get('/api/dashboard/stats'),
};

// React Query Hooks

// Settings hooks
export const useSettings = (): UseQueryResult<BotSettings, Error> => {
  return useQuery({
    queryKey: ['settings'],
    queryFn: async () => {
      const response = await apiService.getSettings();
      return response.data;
    },
  });
};

export const useUpdateSettings = (): UseMutationResult<BotSettings, Error, UpdateSettingsRequest> => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: UpdateSettingsRequest) => {
      const response = await apiService.updateSettings(data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['settings'] });
    },
  });
};

// VPN Panels hooks
export const usePanels = (): UseQueryResult<VPNPanel[], Error> => {
  return useQuery({
    queryKey: ['panels'],
    queryFn: async () => {
      const response = await apiService.getPanels();
      return response.data;
    },
  });
};

export const useCreatePanel = (): UseMutationResult<VPNPanel, Error, CreatePanelRequest> => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: CreatePanelRequest) => {
      const response = await apiService.createPanel(data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['panels'] });
    },
  });
};

export const useUpdatePanel = (): UseMutationResult<VPNPanel, Error, { id: number; data: UpdatePanelRequest }> => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ id, data }) => {
      const response = await apiService.updatePanel(id, data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['panels'] });
    },
  });
};

export const useDeletePanel = (): UseMutationResult<void, Error, number> => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: number) => {
      await apiService.deletePanel(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['panels'] });
    },
  });
};

// Premium Plans hooks
export const usePlans = (): UseQueryResult<PremiumPlan[], Error> => {
  return useQuery({
    queryKey: ['plans'],
    queryFn: async () => {
      const response = await apiService.getPlans();
      return response.data;
    },
  });
};

export const useCreatePlan = (): UseMutationResult<PremiumPlan, Error, CreatePlanRequest> => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: CreatePlanRequest) => {
      const response = await apiService.createPlan(data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['plans'] });
    },
  });
};

export const useUpdatePlan = (): UseMutationResult<PremiumPlan, Error, { id: number; data: UpdatePlanRequest }> => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ id, data }) => {
      const response = await apiService.updatePlan(id, data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['plans'] });
    },
  });
};

export const useDeletePlan = (): UseMutationResult<void, Error, number> => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: number) => {
      await apiService.deletePlan(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['plans'] });
    },
  });
};

// Users hooks
export const useUsers = (): UseQueryResult<User[], Error> => {
  return useQuery({
    queryKey: ['users'],
    queryFn: async () => {
      const response = await apiService.getUsers();
      return response.data;
    },
  });
};

export const useUser = (id: number): UseQueryResult<User, Error> => {
  return useQuery({
    queryKey: ['users', id],
    queryFn: async () => {
      const response = await apiService.getUser(id);
      return response.data;
    },
    enabled: !!id,
  });
};

export const useUpdateUser = (): UseMutationResult<User, Error, { id: number; data: UpdateUserRequest }> => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ id, data }) => {
      const response = await apiService.updateUser(id, data);
      return response.data;
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      queryClient.invalidateQueries({ queryKey: ['users', id] });
    },
  });
};

export const useDeleteUser = (): UseMutationResult<void, Error, number> => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: number) => {
      await apiService.deleteUser(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
  });
};

export const useResetUserTrial = (): UseMutationResult<void, Error, number> => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (userId: number) => {
      await apiService.resetUserTrial(userId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
  });
};

export const useBulkResetTrials = (): UseMutationResult<void, Error, number[]> => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (userIds: number[]) => {
      await apiService.bulkResetTrials(userIds);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
  });
};

// Dashboard hooks
export const useDashboardStats = (): UseQueryResult<DashboardStats, Error> => {
  return useQuery({
    queryKey: ['dashboard', 'stats'],
    queryFn: async () => {
      const response = await apiService.getDashboardStats();
      return response.data;
    },
  });
};

export default api;