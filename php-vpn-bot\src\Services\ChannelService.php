<?php

declare(strict_types=1);

namespace VpnBot\Services;

use VpnBot\Config\Config;
use VpnBot\Models\Channel;
use <PERSON><PERSON>\TelegramBot\Request;
use Psr\Log\LoggerInterface;

class ChannelService
{
    private Config $config;
    private LoggerInterface $logger;

    public function __construct(Config $config, LoggerInterface $logger)
    {
        $this->config = $config;
        $this->logger = $logger;
    }

    public function checkUserSubscriptions(int $telegramId): array
    {
        try {
            $requiredChannels = Channel::findRequired();
            $subscribedCount = 0;
            $missingChannels = [];

            foreach ($requiredChannels as $channel) {
                if ($this->isUserSubscribed($telegramId, $channel->channel_id)) {
                    $subscribedCount++;
                } else {
                    $missingChannels[] = $channel;
                }
            }

            return [
                'is_subscribed' => count($missingChannels) === 0,
                'subscribed_count' => $subscribedCount,
                'total_required' => count($requiredChannels),
                'missing_channels' => $missingChannels,
                'required_channels' => $requiredChannels
            ];

        } catch (\Exception $e) {
            $this->logger->error('Error checking user subscriptions: ' . $e->getMessage());
            return [
                'is_subscribed' => false,
                'subscribed_count' => 0,
                'total_required' => 0,
                'missing_channels' => [],
                'required_channels' => []
            ];
        }
    }

    private function isUserSubscribed(int $telegramId, string $channelId): bool
    {
        try {
            // Use Telegram Bot API to check if user is a member of the channel
            $response = Request::getChatMember([
                'chat_id' => $channelId,
                'user_id' => $telegramId
            ]);

            if ($response->isOk()) {
                $member = $response->getResult();
                $status = $member->getStatus();
                
                // User is subscribed if they are member, administrator, or creator
                return in_array($status, ['member', 'administrator', 'creator']);
            }

            return false;

        } catch (\Exception $e) {
            $this->logger->warning("Could not check subscription for channel {$channelId}: " . $e->getMessage());
            // In case of error, assume user is subscribed to avoid blocking
            return true;
        }
    }

    public function getAllChannels(): array
    {
        try {
            return Channel::findAll();
        } catch (\Exception $e) {
            $this->logger->error('Error getting all channels: ' . $e->getMessage());
            return [];
        }
    }

    public function getRequiredChannels(): array
    {
        try {
            return Channel::findRequired();
        } catch (\Exception $e) {
            $this->logger->error('Error getting required channels: ' . $e->getMessage());
            return [];
        }
    }

    public function addChannel(array $channelData): bool
    {
        try {
            $channel = new Channel();
            $channel->channel_id = $channelData['channel_id'];
            $channel->channel_name = $channelData['channel_name'];
            $channel->channel_url = $channelData['channel_url'] ?? null;
            $channel->invite_link = $channelData['invite_link'] ?? null;
            $channel->is_required = $channelData['is_required'] ?? true;
            $channel->is_active = $channelData['is_active'] ?? true;
            $channel->priority = $channelData['priority'] ?? 0;
            $channel->description = $channelData['description'] ?? null;

            return $channel->save();

        } catch (\Exception $e) {
            $this->logger->error('Error adding channel: ' . $e->getMessage());
            return false;
        }
    }

    public function updateChannel(int $channelId, array $updateData): bool
    {
        try {
            $channel = Channel::findById($channelId);
            if (!$channel) {
                return false;
            }

            foreach ($updateData as $key => $value) {
                if (property_exists($channel, $key)) {
                    $channel->$key = $value;
                }
            }

            return $channel->save();

        } catch (\Exception $e) {
            $this->logger->error('Error updating channel: ' . $e->getMessage());
            return false;
        }
    }

    public function deleteChannel(int $channelId): bool
    {
        try {
            $channel = Channel::findById($channelId);
            if (!$channel) {
                return false;
            }

            // In a real implementation, this would delete the channel from database
            // For now, just mark as inactive
            $channel->is_active = false;
            return $channel->save();

        } catch (\Exception $e) {
            $this->logger->error('Error deleting channel: ' . $e->getMessage());
            return false;
        }
    }

    public function getChannelStats(string $channelId): ?array
    {
        try {
            $response = Request::getChat(['chat_id' => $channelId]);
            
            if ($response->isOk()) {
                $chat = $response->getResult();
                
                return [
                    'id' => $chat->getId(),
                    'title' => $chat->getTitle(),
                    'username' => $chat->getUsername(),
                    'type' => $chat->getType(),
                    'member_count' => $this->getChatMemberCount($channelId),
                    'description' => $chat->getDescription(),
                ];
            }

            return null;

        } catch (\Exception $e) {
            $this->logger->error("Error getting channel stats for {$channelId}: " . $e->getMessage());
            return null;
        }
    }

    private function getChatMemberCount(string $channelId): int
    {
        try {
            $response = Request::getChatMemberCount(['chat_id' => $channelId]);
            
            if ($response->isOk()) {
                return $response->getResult();
            }

            return 0;

        } catch (\Exception $e) {
            $this->logger->warning("Could not get member count for channel {$channelId}: " . $e->getMessage());
            return 0;
        }
    }

    public function updateChannelStats(): void
    {
        try {
            $channels = $this->getAllChannels();
            
            foreach ($channels as $channel) {
                if ($channel->is_active) {
                    $memberCount = $this->getChatMemberCount($channel->channel_id);
                    $channel->subscriber_count = $memberCount;
                    $channel->save();
                }
            }

        } catch (\Exception $e) {
            $this->logger->error('Error updating channel stats: ' . $e->getMessage());
        }
    }
}
