; PHP Configuration for VPN Bot

; Error reporting
display_errors = Off
log_errors = On
error_log = /var/www/html/logs/php_errors.log

; Memory and execution limits
memory_limit = 256M
max_execution_time = 300
max_input_time = 300

; File uploads
upload_max_filesize = 100M
post_max_size = 100M
max_file_uploads = 20

; Session configuration
session.save_handler = redis
session.save_path = "tcp://redis:6379"
session.gc_maxlifetime = 3600

; OPcache configuration
opcache.enable = 1
opcache.enable_cli = 1
opcache.memory_consumption = 128
opcache.interned_strings_buffer = 8
opcache.max_accelerated_files = 4000
opcache.revalidate_freq = 2
opcache.fast_shutdown = 1

; Timezone
date.timezone = UTC

; Security
expose_php = Off
allow_url_fopen = On
allow_url_include = Off

; Logging
log_errors_max_len = 1024
