import asyncio
import hashlib
import hmac
import json
import logging
from typing import Dict, List, Optional, Any
import aiohttp
from decimal import Decimal

from ..config import settings
from ..database import get_db_connection

logger = logging.getLogger(__name__)

class NowPaymentsService:
    """Service for handling NowPayments crypto payments."""
    
    def __init__(self):
        self.api_key = getattr(settings, 'NOWPAYMENTS_API_KEY', None)
        self.ipn_secret = getattr(settings, 'NOWPAYMENTS_IPN_SECRET', None)
        self.base_url = "https://api.nowpayments.io/v1"
        self.session = None
        self.is_configured = bool(self.api_key and self.ipn_secret)
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create aiohttp session."""
        if not self.is_configured:
            raise ValueError("NowPayments API key or IPN secret not configured")
            
        if self.session is None or self.session.closed:
            headers = {
                "x-api-key": self.api_key,
                "Content-Type": "application/json"
            }
            self.session = aiohttp.ClientSession(headers=headers)
        return self.session
    
    async def close(self):
        """Close the aiohttp session."""
        if self.session and not self.session.closed:
            await self.session.close()
    
    async def get_api_status(self) -> Dict[str, Any]:
        """Check API status."""
        if not self.is_configured:
            return {"message": "NowPayments not configured - API key missing"}
            
        try:
            session = await self._get_session()
            async with session.get(f"{self.base_url}/status") as response:
                if response.status == 200:
                    return await response.json()
                else:
                    logger.error(f"API status check failed: {response.status}")
                    return {"message": "API unavailable"}
        except Exception as e:
            logger.error(f"Error checking API status: {e}")
            return {"message": "API error"}
    
    async def get_available_currencies(self) -> List[Dict[str, Any]]:
        """Get list of available payment currencies."""
        if not self.is_configured:
            logger.warning("NowPayments not configured - returning empty currencies list")
            return []
            
        try:
            session = await self._get_session()
            async with session.get(f"{self.base_url}/currencies") as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get("currencies", [])
                else:
                    logger.error(f"Failed to get currencies: {response.status}")
                    return []
        except Exception as e:
            logger.error(f"Error getting currencies: {e}")
            return []
    
    async def get_estimate(self, amount: float, currency_from: str = "usd", currency_to: str = "btc") -> Optional[Dict[str, Any]]:
        """Get estimated amount for currency conversion."""
        if not self.is_configured:
            logger.warning("NowPayments not configured - cannot get estimate")
            return None
            
        try:
            session = await self._get_session()
            params = {
                "amount": amount,
                "currency_from": currency_from,
                "currency_to": currency_to
            }
            async with session.get(f"{self.base_url}/estimate", params=params) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    logger.error(f"Failed to get estimate: {response.status}")
                    return None
        except Exception as e:
            logger.error(f"Error getting estimate: {e}")
            return None
    
    async def get_minimum_amount(self, currency_from: str = "usd", currency_to: str = "btc") -> Optional[Dict[str, Any]]:
        """Get minimum payment amount."""
        if not self.is_configured:
            logger.warning("NowPayments not configured - cannot get minimum amount")
            return None
            
        try:
            session = await self._get_session()
            params = {
                "currency_from": currency_from,
                "currency_to": currency_to
            }
            async with session.get(f"{self.base_url}/min-amount", params=params) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    logger.error(f"Failed to get minimum amount: {response.status}")
                    return None
        except Exception as e:
            logger.error(f"Error getting minimum amount: {e}")
            return None
    
    async def create_payment(self, 
                           price_amount: float,
                           price_currency: str,
                           pay_currency: str,
                           order_id: str,
                           order_description: str,
                           ipn_callback_url: str,
                           success_url: str = None,
                           cancel_url: str = None) -> Optional[Dict[str, Any]]:
        """Create a new payment."""
        if not self.is_configured:
            logger.warning("NowPayments not configured - cannot create payment")
            return None
            
        try:
            session = await self._get_session()
            payload = {
                "price_amount": price_amount,
                "price_currency": price_currency,
                "pay_currency": pay_currency,
                "order_id": order_id,
                "order_description": order_description,
                "ipn_callback_url": ipn_callback_url
            }
            
            if success_url:
                payload["success_url"] = success_url
            if cancel_url:
                payload["cancel_url"] = cancel_url
            
            async with session.post(f"{self.base_url}/payment", json=payload) as response:
                if response.status == 201:
                    return await response.json()
                else:
                    error_text = await response.text()
                    logger.error(f"Failed to create payment: {response.status} - {error_text}")
                    return None
        except Exception as e:
            logger.error(f"Error creating payment: {e}")
            return None
    
    async def get_payment_status(self, payment_id: str) -> Optional[Dict[str, Any]]:
        """Get payment status by ID."""
        if not self.is_configured:
            logger.warning("NowPayments not configured - cannot get payment status")
            return None
            
        try:
            session = await self._get_session()
            async with session.get(f"{self.base_url}/payment/{payment_id}") as response:
                if response.status == 200:
                    return await response.json()
                else:
                    logger.error(f"Failed to get payment status: {response.status}")
                    return None
        except Exception as e:
            logger.error(f"Error getting payment status: {e}")
            return None
    
    def verify_ipn_signature(self, request_body: str, received_signature: str) -> bool:
        """Verify IPN HMAC signature."""
        if not self.is_configured:
            logger.warning("NowPayments not configured - cannot verify IPN signature")
            return False
            
        try:
            expected_signature = hmac.new(
                self.ipn_secret.encode('utf-8'),
                request_body.encode('utf-8'),
                hashlib.sha512
            ).hexdigest()
            return hmac.compare_digest(expected_signature, received_signature)
        except Exception as e:
            logger.error(f"Error verifying IPN signature: {e}")
            return False
    
    async def create_invoice_for_plan(self, 
                                    user_id: int, 
                                    plan_id: int, 
                                    pay_currency: str = "btc") -> Optional[Dict[str, Any]]:
        """Create a NowPayments invoice for a premium plan."""
        if not self.is_configured:
            logger.warning("NowPayments not configured - cannot create invoice")
            return None
            
        try:
            # Get plan details
            async with get_db_connection() as conn:
                plan = await conn.fetchrow(
                    "SELECT * FROM premium_plans WHERE id = $1 AND is_active = true", 
                    plan_id
                )
                
                if not plan:
                    raise ValueError("Plan not found or inactive")
                
                # Create order ID
                order_id = f"plan_{plan_id}_user_{user_id}_{int(asyncio.get_event_loop().time())}"
                
                # Create payment
                payment_data = await self.create_payment(
                    price_amount=float(plan['price']),
                    price_currency="usd",
                    pay_currency=pay_currency,
                    order_id=order_id,
                    order_description=f"Premium Plan: {plan['name']} - {plan['duration_days']} days",
                    ipn_callback_url=f"{settings.WEBHOOK_URL}/api/nowpayments/ipn",
                    success_url=f"{settings.WEBHOOK_URL}/payment/success",
                    cancel_url=f"{settings.WEBHOOK_URL}/payment/cancel"
                )
                
                if payment_data:
                    # Store payment record in database
                    await conn.execute(
                        """
                        INSERT INTO crypto_payments 
                        (payment_id, user_id, plan_id, order_id, amount, currency, status, created_at)
                        VALUES ($1, $2, $3, $4, $5, $6, $7, NOW())
                        """,
                        payment_data['payment_id'],
                        user_id,
                        plan_id,
                        order_id,
                        payment_data['price_amount'],
                        payment_data['price_currency'],
                        payment_data['payment_status']
                    )
                
                return payment_data
                
        except Exception as e:
            logger.error(f"Error creating invoice for plan: {e}")
            return None
    
    async def process_ipn_notification(self, ipn_data: Dict[str, Any]) -> bool:
        """Process IPN notification from NowPayments."""
        try:
            payment_id = ipn_data.get('payment_id')
            payment_status = ipn_data.get('payment_status')
            order_id = ipn_data.get('order_id')
            
            if not all([payment_id, payment_status, order_id]):
                logger.error("Missing required IPN data")
                return False
            
            async with get_db_connection() as conn:
                # Update payment status
                await conn.execute(
                    "UPDATE crypto_payments SET status = $1, updated_at = NOW() WHERE payment_id = $2",
                    payment_status, payment_id
                )
                
                # If payment is finished, activate subscription
                if payment_status == 'finished':
                    payment_record = await conn.fetchrow(
                        "SELECT * FROM crypto_payments WHERE payment_id = $1", payment_id
                    )
                    
                    if payment_record:
                        # Activate premium subscription
                        from .subscription_service import SubscriptionService
                        subscription_service = SubscriptionService()
                        await subscription_service.activate_premium_subscription(
                            payment_record['user_id'],
                            payment_record['plan_id']
                        )
                        
                        logger.info(f"Premium subscription activated for user {payment_record['user_id']}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error processing IPN notification: {e}")
            return False

# Global instance
nowpayments_service = NowPaymentsService()