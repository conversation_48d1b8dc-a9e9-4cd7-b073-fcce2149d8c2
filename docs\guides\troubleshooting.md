# Troubleshooting Guide

This guide helps you diagnose and resolve common issues with the VPN Telegram Bot project.

## Table of Contents

1. [Common Issues](#common-issues)
2. [Bot Issues](#bot-issues)
3. [Database Issues](#database-issues)
4. [VPN Service Issues](#vpn-service-issues)
5. [Payment Issues](#payment-issues)
6. [Performance Issues](#performance-issues)
7. [Deployment Issues](#deployment-issues)
8. [Monitoring and Debugging](#monitoring-and-debugging)
9. [FAQ](#faq)

## Common Issues

### Bot Not Responding

**Symptoms:**
- <PERSON><PERSON> doesn't respond to commands
- Users can't interact with the bot
- Webhook not receiving updates

**Diagnosis:**
```bash
# Check bot status
docker-compose ps

# Check bot logs
docker-compose logs bot

# Test webhook
curl -X POST https://your-domain.com/webhook \
  -H "Content-Type: application/json" \
  -d '{"update_id": 1, "message": {"text": "/start"}}'

# Check Telegram webhook status
curl "https://api.telegram.org/bot<BOT_TOKEN>/getWebhookInfo"
```

**Solutions:**

1. **Invalid Bot Token:**
```bash
# Verify bot token
curl "https://api.telegram.org/bot<BOT_TOKEN>/getMe"

# Update token in environment
echo "BOT_TOKEN=your_new_token" >> .env
docker-compose restart bot
```

2. **Webhook Configuration Issues:**
```python
# Reset webhook
import asyncio
from telegram import Bot

async def reset_webhook():
    bot = Bot(token="YOUR_BOT_TOKEN")
    await bot.delete_webhook()
    await bot.set_webhook(
        url="https://your-domain.com/webhook",
        secret_token="your_webhook_secret"
    )

asyncio.run(reset_webhook())
```

3. **SSL Certificate Issues:**
```bash
# Check SSL certificate
openssl s_client -connect your-domain.com:443 -servername your-domain.com

# Renew Let's Encrypt certificate
sudo certbot renew
sudo systemctl reload nginx
```

4. **Firewall Issues:**
```bash
# Check firewall status
sudo ufw status

# Allow HTTPS traffic
sudo ufw allow 443/tcp
sudo ufw reload
```

### Database Connection Issues

**Symptoms:**
- "Connection refused" errors
- "Database does not exist" errors
- Slow query performance

**Diagnosis:**
```bash
# Check PostgreSQL status
docker-compose ps postgres

# Check database logs
docker-compose logs postgres

# Test database connection
docker-compose exec postgres psql -U postgres -d vpn_bot -c "SELECT 1;"

# Check connection pool
docker-compose exec bot python -c "
import asyncio
from bot.database import get_db_pool
async def test():
    pool = await get_db_pool()
    print(f'Pool size: {pool.get_size()}')
    print(f'Pool max size: {pool.get_max_size()}')
asyncio.run(test())
"
```

**Solutions:**

1. **Database Not Running:**
```bash
# Start database
docker-compose up -d postgres

# Check if database is ready
docker-compose exec postgres pg_isready -U postgres
```

2. **Wrong Database Credentials:**
```bash
# Verify credentials in .env
cat .env | grep DATABASE

# Test connection with correct credentials
docker-compose exec postgres psql -U $POSTGRES_USER -d $POSTGRES_DB
```

3. **Database Doesn't Exist:**
```bash
# Create database
docker-compose exec postgres createdb -U postgres vpn_bot

# Run migrations
docker-compose exec bot python -m bot.migrations.migrate
```

4. **Connection Pool Exhaustion:**
```python
# Increase pool size in config
DATABASE_POOL_SIZE=30
DATABASE_MAX_OVERFLOW=50

# Restart application
docker-compose restart bot
```

### Redis Connection Issues

**Symptoms:**
- Cache not working
- "Connection refused" to Redis
- High memory usage

**Diagnosis:**
```bash
# Check Redis status
docker-compose ps redis

# Test Redis connection
docker-compose exec redis redis-cli ping

# Check Redis memory usage
docker-compose exec redis redis-cli info memory

# Monitor Redis commands
docker-compose exec redis redis-cli monitor
```

**Solutions:**

1. **Redis Not Running:**
```bash
# Start Redis
docker-compose up -d redis

# Check Redis logs
docker-compose logs redis
```

2. **Memory Issues:**
```bash
# Check memory usage
docker-compose exec redis redis-cli info memory

# Clear cache if needed
docker-compose exec redis redis-cli flushall

# Set memory limit
echo "maxmemory 512mb" >> redis.conf
echo "maxmemory-policy allkeys-lru" >> redis.conf
```

3. **Authentication Issues:**
```bash
# Test with password
docker-compose exec redis redis-cli -a $REDIS_PASSWORD ping

# Update password in environment
echo "REDIS_PASSWORD=new_password" >> .env
```

## Bot Issues

### Command Handlers Not Working

**Symptoms:**
- Specific commands don't work
- Error messages in logs
- Handlers not being called

**Diagnosis:**
```python
# Enable debug logging
import logging
logging.getLogger('telegram').setLevel(logging.DEBUG)

# Check handler registration
from bot.main import application
print("Registered handlers:")
for group, handlers in application.handlers.items():
    print(f"Group {group}: {len(handlers)} handlers")
    for handler in handlers:
        print(f"  - {type(handler).__name__}: {handler}")
```

**Solutions:**

1. **Handler Registration Issues:**
```python
# Ensure handlers are properly registered
from telegram.ext import CommandHandler
from bot.handlers.commands import start_command

application.add_handler(CommandHandler('start', start_command))
```

2. **Middleware Blocking Requests:**
```python
# Check middleware logs
# Disable middleware temporarily for testing
# application.add_middleware(subscription_middleware)  # Comment out
```

3. **Permission Issues:**
```python
# Check user permissions
from bot.services.auth_service import AuthService

async def check_user(user_id):
    auth_service = AuthService()
    user = await auth_service.get_user(user_id)
    print(f"User status: {user.get('status')}")
    print(f"Is banned: {user.get('status') == 'banned'}")
```

### Callback Query Issues

**Symptoms:**
- Inline buttons don't work
- "Query too old" errors
- Callback data not found

**Diagnosis:**
```python
# Check callback handler registration
from telegram.ext import CallbackQueryHandler

# Log callback data
async def debug_callback(update, context):
    query = update.callback_query
    print(f"Callback data: {query.data}")
    print(f"User: {query.from_user.id}")
    await query.answer()

application.add_handler(CallbackQueryHandler(debug_callback))
```

**Solutions:**

1. **Answer Callback Queries:**
```python
# Always answer callback queries
async def handle_callback(update, context):
    query = update.callback_query
    try:
        # Process callback
        await process_callback(query)
    finally:
        # Always answer
        await query.answer()
```

2. **Handle Old Queries:**
```python
# Check query age
from datetime import datetime, timedelta

async def handle_callback(update, context):
    query = update.callback_query
    
    # Check if query is too old (older than 1 hour)
    if datetime.now() - datetime.fromtimestamp(query.message.date.timestamp()) > timedelta(hours=1):
        await query.answer("This button has expired. Please try again.", show_alert=True)
        return
    
    # Process callback
    await process_callback(query)
```

3. **Validate Callback Data:**
```python
# Validate callback data format
async def handle_callback(update, context):
    query = update.callback_query
    
    try:
        action, *params = query.data.split(':')
        if action not in VALID_ACTIONS:
            raise ValueError(f"Invalid action: {action}")
    except ValueError as e:
        await query.answer(f"Invalid request: {e}", show_alert=True)
        return
    
    # Process valid callback
    await process_callback(query, action, params)
```

## Database Issues

### Migration Failures

**Symptoms:**
- Migration scripts fail
- Database schema inconsistencies
- "Column already exists" errors

**Diagnosis:**
```bash
# Check migration status
docker-compose exec bot python -c "
import asyncio
from bot.migrations.migrate import MigrationManager
from bot.database import get_db_pool

async def check_migrations():
    pool = await get_db_pool()
    manager = MigrationManager(pool)
    applied = await manager._get_applied_migrations()
    available = manager._get_available_migrations()
    print(f'Applied: {applied}')
    print(f'Available: {available}')
    print(f'Pending: {set(available) - set(applied)}')

asyncio.run(check_migrations())
"

# Check database schema
docker-compose exec postgres psql -U postgres -d vpn_bot -c "\dt"
```

**Solutions:**

1. **Reset Migrations:**
```bash
# Backup database first
docker-compose exec postgres pg_dump -U postgres vpn_bot > backup.sql

# Drop and recreate database
docker-compose exec postgres dropdb -U postgres vpn_bot
docker-compose exec postgres createdb -U postgres vpn_bot

# Run all migrations
docker-compose exec bot python -m bot.migrations.migrate
```

2. **Fix Partial Migrations:**
```sql
-- Connect to database
-- docker-compose exec postgres psql -U postgres -d vpn_bot

-- Check migration table
SELECT * FROM migrations ORDER BY applied_at;

-- Remove failed migration record
DELETE FROM migrations WHERE name = 'failed_migration_name';

-- Fix schema manually if needed
ALTER TABLE users ADD COLUMN IF NOT EXISTS new_column VARCHAR(255);
```

3. **Handle Schema Conflicts:**
```python
# Create idempotent migrations
async def upgrade(conn):
    # Check if column exists before adding
    result = await conn.fetchval("""
        SELECT column_name FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'new_column'
    """)
    
    if not result:
        await conn.execute("""
            ALTER TABLE users ADD COLUMN new_column VARCHAR(255)
        """)
```

### Performance Issues

**Symptoms:**
- Slow database queries
- High CPU usage
- Connection timeouts

**Diagnosis:**
```sql
-- Enable query logging
ALTER SYSTEM SET log_statement = 'all';
ALTER SYSTEM SET log_min_duration_statement = 1000; -- Log queries > 1s
SELECT pg_reload_conf();

-- Check slow queries
SELECT query, mean_exec_time, calls 
FROM pg_stat_statements 
ORDER BY mean_exec_time DESC 
LIMIT 10;

-- Check table sizes
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Check index usage
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;
```

**Solutions:**

1. **Add Missing Indexes:**
```sql
-- Add indexes for frequently queried columns
CREATE INDEX CONCURRENTLY idx_users_last_active ON users(last_active);
CREATE INDEX CONCURRENTLY idx_vpn_accounts_expires_at ON vpn_accounts(expires_at);
CREATE INDEX CONCURRENTLY idx_payments_created_at ON payments(created_at);

-- Composite indexes for complex queries
CREATE INDEX CONCURRENTLY idx_vpn_accounts_user_status 
ON vpn_accounts(user_id, status);
```

2. **Optimize Queries:**
```python
# Use connection pooling
from bot.database import get_db_pool

# Batch operations
async def update_multiple_users(user_updates):
    pool = await get_db_pool()
    async with pool.acquire() as conn:
        await conn.executemany(
            "UPDATE users SET last_active = $2 WHERE id = $1",
            user_updates
        )

# Use LIMIT and OFFSET for pagination
async def get_users_paginated(page=1, limit=20):
    offset = (page - 1) * limit
    return await conn.fetch(
        "SELECT * FROM users ORDER BY created_at DESC LIMIT $1 OFFSET $2",
        limit, offset
    )
```

3. **Database Maintenance:**
```bash
# Regular maintenance script
#!/bin/bash
# maintenance.sh

# Vacuum and analyze
docker-compose exec postgres psql -U postgres -d vpn_bot -c "VACUUM ANALYZE;"

# Reindex
docker-compose exec postgres psql -U postgres -d vpn_bot -c "REINDEX DATABASE vpn_bot;"

# Update statistics
docker-compose exec postgres psql -U postgres -d vpn_bot -c "ANALYZE;"
```

## VPN Service Issues

### Marzban API Connection Issues

**Symptoms:**
- "Connection refused" to Marzban
- Authentication failures
- Account creation failures

**Diagnosis:**
```bash
# Test Marzban API connectivity
curl -X GET "$MARZBAN_URL/api/system" \
  -H "Authorization: Bearer $MARZBAN_TOKEN"

# Check Marzban logs
docker logs marzban

# Test authentication
curl -X POST "$MARZBAN_URL/api/admin/token" \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin_password"}'
```

**Solutions:**

1. **Fix Authentication:**
```python
# Refresh Marzban token
from bot.services.vpn_service import VPNService

vpn_service = VPNService()
await vpn_service.authenticate()

# Store token in Redis with expiration
from bot.redis import redis_manager

async def store_marzban_token(token):
    await redis_manager.set_with_expiry(
        "marzban_token",
        token,
        3600  # 1 hour
    )
```

2. **Handle API Errors:**
```python
# Implement retry logic
import asyncio
from aiohttp import ClientError

async def create_vpn_account_with_retry(user_data, max_retries=3):
    for attempt in range(max_retries):
        try:
            return await vpn_service.create_account(user_data)
        except ClientError as e:
            if attempt == max_retries - 1:
                raise
            await asyncio.sleep(2 ** attempt)  # Exponential backoff
```

3. **Validate Marzban Configuration:**
```python
# Check Marzban system status
async def check_marzban_health():
    try:
        response = await vpn_service.get_system_info()
        print(f"Marzban version: {response.get('version')}")
        print(f"Users count: {response.get('users_count')}")
        return True
    except Exception as e:
        print(f"Marzban health check failed: {e}")
        return False
```

### Account Creation Issues

**Symptoms:**
- Accounts not created in Marzban
- Invalid subscription URLs
- Data limit not applied

**Diagnosis:**
```python
# Test account creation
from bot.services.vpn_service import VPNService

vpn_service = VPNService()
test_account = {
    "username": "test_user_123",
    "data_limit": 10 * 1024 * 1024 * 1024,  # 10GB
    "expire": int(time.time()) + 30 * 24 * 3600  # 30 days
}

try:
    result = await vpn_service.create_account(test_account)
    print(f"Account created: {result}")
except Exception as e:
    print(f"Account creation failed: {e}")
```

**Solutions:**

1. **Fix Username Generation:**
```python
# Ensure unique usernames
import time
import random

def generate_username(user_id):
    timestamp = int(time.time())
    random_suffix = random.randint(1000, 9999)
    return f"user_{user_id}_{timestamp}_{random_suffix}"

# Validate username format
def validate_username(username):
    if len(username) > 32:
        raise ValueError("Username too long")
    if not username.replace('_', '').isalnum():
        raise ValueError("Username contains invalid characters")
```

2. **Handle Data Limit Conversion:**
```python
# Convert GB to bytes correctly
def gb_to_bytes(gb):
    return int(gb * 1024 * 1024 * 1024)

# Validate data limits
def validate_data_limit(limit_gb):
    if limit_gb <= 0:
        raise ValueError("Data limit must be positive")
    if limit_gb > 1000:  # Max 1TB
        raise ValueError("Data limit too large")
    return gb_to_bytes(limit_gb)
```

3. **Fix Expiration Dates:**
```python
# Handle timezone issues
from datetime import datetime, timezone

def calculate_expiration(days):
    now = datetime.now(timezone.utc)
    expiration = now + timedelta(days=days)
    return int(expiration.timestamp())

# Validate expiration
def validate_expiration(expire_timestamp):
    now = int(time.time())
    if expire_timestamp <= now:
        raise ValueError("Expiration date must be in the future")
    if expire_timestamp > now + 365 * 24 * 3600:  # Max 1 year
        raise ValueError("Expiration date too far in the future")
```

## Payment Issues

### Telegram Payment Failures

**Symptoms:**
- Payment buttons don't work
- Pre-checkout queries fail
- Successful payments not processed

**Diagnosis:**
```python
# Test payment provider
from telegram import Bot

bot = Bot(token=BOT_TOKEN)

# Test invoice creation
try:
    await bot.send_invoice(
        chat_id=TEST_CHAT_ID,
        title="Test Payment",
        description="Test payment description",
        payload="test_payload",
        provider_token=PAYMENT_PROVIDER_TOKEN,
        currency="USD",
        prices=[("Test", 100)]  # $1.00
    )
except Exception as e:
    print(f"Invoice creation failed: {e}")
```

**Solutions:**

1. **Fix Payment Provider Configuration:**
```python
# Validate provider token
if not PAYMENT_PROVIDER_TOKEN:
    raise ValueError("Payment provider token not configured")

# Test with different currencies
SUPPORTED_CURRENCIES = ["USD", "EUR", "RUB"]

def validate_currency(currency):
    if currency not in SUPPORTED_CURRENCIES:
        raise ValueError(f"Unsupported currency: {currency}")
```

2. **Handle Pre-checkout Validation:**
```python
# Validate pre-checkout queries
async def handle_pre_checkout_query(update, context):
    query = update.pre_checkout_query
    
    try:
        # Validate payload
        payload_parts = query.invoice_payload.split('_')
        if len(payload_parts) != 3:
            raise ValueError("Invalid payload format")
        
        plan_type, user_id, timestamp = payload_parts
        
        # Validate user
        user = await auth_service.get_user(int(user_id))
        if not user:
            raise ValueError("User not found")
        
        # Validate plan
        plan = await get_plan(plan_type)
        if not plan:
            raise ValueError("Plan not found")
        
        # Validate amount
        expected_amount = int(plan['price'] * 100)  # Convert to cents
        if query.total_amount != expected_amount:
            raise ValueError("Amount mismatch")
        
        await query.answer(ok=True)
        
    except Exception as e:
        logger.error(f"Pre-checkout validation failed: {e}")
        await query.answer(ok=False, error_message=str(e))
```

3. **Process Successful Payments:**
```python
# Handle successful payments
async def handle_successful_payment(update, context):
    payment = update.message.successful_payment
    user_id = update.effective_user.id
    
    try:
        # Parse payload
        plan_type, payload_user_id, timestamp = payment.invoice_payload.split('_')
        
        # Validate user
        if int(payload_user_id) != user_id:
            raise ValueError("User ID mismatch")
        
        # Create VPN account
        plan = await get_plan(plan_type)
        account = await vpn_service.create_premium_account(
            user_id=user_id,
            plan=plan
        )
        
        # Record payment
        await payment_service.record_payment(
            user_id=user_id,
            amount=payment.total_amount / 100,
            currency=payment.currency,
            plan_id=plan_type,
            telegram_charge_id=payment.telegram_payment_charge_id,
            provider_charge_id=payment.provider_payment_charge_id
        )
        
        # Send account details
        await send_account_details(update, account)
        
    except Exception as e:
        logger.error(f"Payment processing failed: {e}")
        await update.message.reply_text(
            "Payment received but account creation failed. Please contact support."
        )
```

## Performance Issues

### High Memory Usage

**Symptoms:**
- Application crashes with OOM errors
- Slow response times
- High swap usage

**Diagnosis:**
```bash
# Check memory usage
docker stats

# Check application memory
docker-compose exec bot python -c "
import psutil
import os
process = psutil.Process(os.getpid())
print(f'Memory usage: {process.memory_info().rss / 1024 / 1024:.2f} MB')
print(f'Memory percent: {process.memory_percent():.2f}%')
"

# Check for memory leaks
docker-compose exec bot python -c "
import gc
print(f'Objects in memory: {len(gc.get_objects())}')
print(f'Garbage collection stats: {gc.get_stats()}')
"
```

**Solutions:**

1. **Optimize Database Connections:**
```python
# Reduce connection pool size
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Close connections properly
async def cleanup_connections():
    pool = await get_db_pool()
    await pool.close()
```

2. **Implement Caching Limits:**
```python
# Limit cache size
from cachetools import TTLCache

# Use bounded cache
cache = TTLCache(maxsize=1000, ttl=3600)

# Clear cache periodically
import asyncio

async def cache_cleanup():
    while True:
        cache.clear()
        await asyncio.sleep(3600)  # Clear every hour
```

3. **Optimize Image Processing:**
```python
# Limit QR code generation
from PIL import Image
import io

def generate_qr_code_optimized(data):
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=5,  # Smaller size
        border=2,
    )
    qr.add_data(data)
    qr.make(fit=True)
    
    img = qr.make_image(fill_color="black", back_color="white")
    
    # Compress image
    buffer = io.BytesIO()
    img.save(buffer, format='PNG', optimize=True)
    return buffer.getvalue()
```

### High CPU Usage

**Symptoms:**
- Slow response times
- High load average
- Timeouts

**Diagnosis:**
```bash
# Check CPU usage
top -p $(docker-compose exec bot pgrep python)

# Profile application
docker-compose exec bot python -m cProfile -o profile.stats -m bot.main

# Analyze profile
docker-compose exec bot python -c "
import pstats
p = pstats.Stats('profile.stats')
p.sort_stats('cumulative').print_stats(20)
"
```

**Solutions:**

1. **Optimize Async Operations:**
```python
# Use asyncio.gather for concurrent operations
import asyncio

async def process_multiple_users(user_ids):
    # Instead of sequential processing
    # for user_id in user_ids:
    #     await process_user(user_id)
    
    # Use concurrent processing
    tasks = [process_user(user_id) for user_id in user_ids]
    await asyncio.gather(*tasks)

# Use semaphore to limit concurrency
semaphore = asyncio.Semaphore(10)

async def rate_limited_operation():
    async with semaphore:
        await expensive_operation()
```

2. **Cache Expensive Operations:**
```python
# Cache user data
from functools import lru_cache

@lru_cache(maxsize=1000)
def get_user_permissions(user_id):
    # Expensive permission calculation
    return calculate_permissions(user_id)

# Cache with TTL
from cachetools import TTLCache
from cachetools.decorators import cached

user_cache = TTLCache(maxsize=1000, ttl=300)  # 5 minutes

@cached(cache=user_cache)
async def get_user_data(user_id):
    return await database.fetch_user(user_id)
```

3. **Optimize Regular Expressions:**
```python
# Compile regex patterns once
import re

# Instead of
# if re.match(r'\d+', text):

# Use compiled patterns
NUMBER_PATTERN = re.compile(r'\d+')
if NUMBER_PATTERN.match(text):
    pass
```

## Deployment Issues

### Docker Build Failures

**Symptoms:**
- Build process fails
- Missing dependencies
- Image size too large

**Diagnosis:**
```bash
# Build with verbose output
docker-compose build --no-cache --progress=plain

# Check image sizes
docker images | grep vpn-bot

# Inspect build layers
docker history vpn-bot_bot:latest
```

**Solutions:**

1. **Fix Dependency Issues:**
```dockerfile
# Use specific Python version
FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .
```

2. **Optimize Image Size:**
```dockerfile
# Multi-stage build
FROM python:3.11-slim as builder
RUN pip install --user --no-cache-dir -r requirements.txt

FROM python:3.11-slim
COPY --from=builder /root/.local /root/.local
ENV PATH=/root/.local/bin:$PATH
COPY . .
```

3. **Handle Build Context:**
```bash
# Create .dockerignore
echo "node_modules
.git
*.log
__pycache__
.pytest_cache" > .dockerignore

# Reduce build context size
docker-compose build --compress
```

### SSL/TLS Issues

**Symptoms:**
- Certificate validation errors
- HTTPS not working
- Mixed content warnings

**Diagnosis:**
```bash
# Test SSL certificate
openssl s_client -connect your-domain.com:443 -servername your-domain.com

# Check certificate expiration
echo | openssl s_client -connect your-domain.com:443 2>/dev/null | openssl x509 -noout -dates

# Test with curl
curl -I https://your-domain.com
```

**Solutions:**

1. **Fix Certificate Issues:**
```bash
# Renew Let's Encrypt certificate
sudo certbot renew --dry-run
sudo certbot renew
sudo systemctl reload nginx

# Check certificate files
sudo ls -la /etc/letsencrypt/live/your-domain.com/
```

2. **Update Nginx Configuration:**
```nginx
# Strong SSL configuration
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
ssl_prefer_server_ciphers off;
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 10m;

# HSTS header
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
```

3. **Handle Certificate Renewal:**
```bash
# Automatic renewal cron job
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -

# Test renewal
sudo certbot renew --dry-run
```

## Monitoring and Debugging

### Enable Debug Logging

```python
# bot/config.py
import logging

def setup_debug_logging():
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('debug.log'),
            logging.StreamHandler()
        ]
    )
    
    # Enable specific loggers
    logging.getLogger('telegram').setLevel(logging.DEBUG)
    logging.getLogger('asyncpg').setLevel(logging.DEBUG)
    logging.getLogger('aiohttp').setLevel(logging.DEBUG)
```

### Health Check Endpoint

```python
# bot/health.py
from aiohttp import web
from bot.database import get_db_pool
from bot.redis import get_redis

async def health_check(request):
    health_status = {
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat(),
        'services': {}
    }
    
    # Check database
    try:
        pool = await get_db_pool()
        async with pool.acquire() as conn:
            await conn.fetchval('SELECT 1')
        health_status['services']['database'] = True
    except Exception as e:
        health_status['services']['database'] = False
        health_status['status'] = 'unhealthy'
    
    # Check Redis
    try:
        redis = await get_redis()
        await redis.ping()
        health_status['services']['redis'] = True
    except Exception as e:
        health_status['services']['redis'] = False
        health_status['status'] = 'unhealthy'
    
    status_code = 200 if health_status['status'] == 'healthy' else 503
    return web.json_response(health_status, status=status_code)
```

### Performance Monitoring

```python
# bot/monitoring.py
import time
import psutil
from prometheus_client import Counter, Histogram, Gauge

# Metrics
REQUEST_COUNT = Counter('bot_requests_total', 'Total requests', ['method', 'status'])
REQUEST_DURATION = Histogram('bot_request_duration_seconds', 'Request duration')
MEMORY_USAGE = Gauge('bot_memory_usage_bytes', 'Memory usage in bytes')
CPU_USAGE = Gauge('bot_cpu_usage_percent', 'CPU usage percentage')

def update_system_metrics():
    """Update system metrics."""
    process = psutil.Process()
    MEMORY_USAGE.set(process.memory_info().rss)
    CPU_USAGE.set(process.cpu_percent())

def track_request(func):
    """Decorator to track request metrics."""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            REQUEST_COUNT.labels(method=func.__name__, status='success').inc()
            return result
        except Exception as e:
            REQUEST_COUNT.labels(method=func.__name__, status='error').inc()
            raise
        finally:
            REQUEST_DURATION.observe(time.time() - start_time)
    return wrapper
```

## FAQ

### Q: Bot responds slowly or times out

**A:** Check the following:
1. Database connection pool size
2. Redis connectivity
3. Marzban API response times
4. Network latency
5. Server resources (CPU, memory)

### Q: Users can't create trial accounts

**A:** Verify:
1. Trial eligibility logic
2. Channel subscription requirements
3. Marzban account creation
4. Database constraints
5. Rate limiting settings

### Q: Payments are not processed

**A:** Check:
1. Payment provider configuration
2. Webhook endpoint accessibility
3. SSL certificate validity
4. Pre-checkout query handling
5. Payment processing logic

### Q: VPN accounts don't work

**A:** Investigate:
1. Marzban server status
2. Account configuration
3. Subscription URL format
4. Network connectivity
5. Client configuration

### Q: High server load

**A:** Optimize:
1. Database queries and indexes
2. Caching strategy
3. Async operation concurrency
4. Resource limits
5. Background task scheduling

### Q: Data not persisting

**A:** Verify:
1. Database transactions
2. Connection handling
3. Migration status
4. Disk space
5. Backup integrity

This troubleshooting guide covers the most common issues and their solutions. For additional help, check the application logs and enable debug mode for more detailed information.