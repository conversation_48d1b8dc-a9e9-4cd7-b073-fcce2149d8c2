# TON Payments Setup Guide

This guide will help you set up TON (The Open Network) cryptocurrency payments for your VPN Telegram Bot.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [TON Wallet Setup](#ton-wallet-setup)
3. [Environment Configuration](#environment-configuration)
4. [Database Migration](#database-migration)
5. [Testing TON Payments](#testing-ton-payments)
6. [Monitoring and Troubleshooting](#monitoring-and-troubleshooting)
7. [Security Best Practices](#security-best-practices)

## Prerequisites

Before setting up TON payments, ensure you have:

- A running VPN Telegram Bot instance
- PostgreSQL database access
- Basic understanding of cryptocurrency wallets
- TON wallet with some TON for testing

## TON Wallet Setup

### 1. Create a TON Wallet

You can create a TON wallet using several methods:

#### Option A: TON Wallet (Recommended)
1. Download TON Wallet from [wallet.ton.org](https://wallet.ton.org)
2. Create a new wallet and securely store your seed phrase
3. Note down your wallet address

#### Option B: Tonkeeper
1. Download Tonkeeper from [tonkeeper.com](https://tonkeeper.com)
2. Create a new wallet
3. Export your private key (Settings → Backup → Show private key)

#### Option C: Command Line (Advanced)
```bash
# Install TON CLI tools
npm install -g @ton/cli

# Generate new wallet
ton wallet create
```

### 2. Fund Your Wallet

For mainnet operations, you'll need to fund your master wallet with TON:
- Buy TON from exchanges like Binance, OKX, or Gate.io
- Transfer TON to your master wallet address
- Keep some TON for transaction fees

### 3. Export Private Key

You'll need the private key for your master wallet:

**⚠️ Security Warning**: Never share your private key. Store it securely and use environment variables.

## Environment Configuration

### 1. Add TON Configuration to .env

Add the following variables to your `.env` file:

```bash
# TON Payment Configuration
TON_MASTER_WALLET=your_ton_master_wallet_address
TON_MASTER_PRIVATE_KEY=your_ton_master_private_key
TON_NETWORK=mainnet
TON_API_KEY=your_ton_api_key_optional
TON_PAYMENT_TIMEOUT_MINUTES=30
```

### 2. Configuration Options

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `TON_MASTER_WALLET` | Your master wallet address | - | Yes |
| `TON_MASTER_PRIVATE_KEY` | Private key for the master wallet | - | Yes |
| `TON_NETWORK` | Network to use (mainnet/testnet) | mainnet | No |
| `TON_API_KEY` | API key for TON services | - | No |
| `TON_PAYMENT_TIMEOUT_MINUTES` | Payment timeout in minutes | 30 | No |

### 3. Network Selection

#### Mainnet (Production)
```bash
TON_NETWORK=mainnet
```
- Use for production environment
- Real TON transactions
- Higher security requirements

#### Testnet (Development)
```bash
TON_NETWORK=testnet
```
- Use for development and testing
- Free test TON available
- Lower security requirements

## Database Migration

### 1. Run TON Payments Migration

Execute the TON payments migration to create the necessary database tables:

```bash
# Navigate to your project directory
cd /path/to/your/vpn-bot

# Run the migration
psql -h $POSTGRES_HOST -U $POSTGRES_USER -d $POSTGRES_DATABASE -f migrations/add_ton_payments_table.sql
```

### 2. Verify Migration

Check that the `ton_payments` table was created:

```sql
-- Connect to your database
\dt ton_payments

-- Check table structure
\d ton_payments
```

Expected columns:
- `payment_id` (UUID, Primary Key)
- `user_id` (BIGINT, Foreign Key)
- `plan_id` (UUID, Foreign Key)
- `wallet_address` (VARCHAR)
- `ton_amount` (DECIMAL)
- `usd_amount` (DECIMAL)
- `memo` (VARCHAR)
- `status` (VARCHAR)
- `transaction_hash` (VARCHAR)
- `expires_at` (TIMESTAMP)
- `confirmed_at` (TIMESTAMP)
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

## Testing TON Payments

### 1. Test Environment Setup

For testing, use the TON testnet:

```bash
# Set testnet configuration
TON_NETWORK=testnet
TON_MASTER_WALLET=your_testnet_wallet_address
TON_MASTER_PRIVATE_KEY=your_testnet_private_key
```

### 2. Get Test TON

For testnet testing:
1. Create a testnet wallet
2. Use TON testnet faucet to get free test TON
3. Visit [testnet.tonscan.org](https://testnet.tonscan.org) for testnet explorer

### 3. Test Payment Flow

1. **Start the bot** and navigate to premium packages
2. **Select a plan** and choose "Pay with TON"
3. **Copy the payment details**:
   - Wallet address
   - TON amount
   - Memo (important for payment identification)
4. **Send payment** from your TON wallet
5. **Check payment status** using the bot's check payment button
6. **Verify confirmation** once the transaction is processed

### 4. Manual Testing Commands

You can test TON service functions directly:

```python
# Test in Python console
from bot.services.ton_service import TONService
from bot.config import settings

# Initialize service
ton_service = TONService()

# Test price fetching
price = await ton_service.get_ton_price_usd()
print(f"Current TON price: ${price}")

# Test amount calculation
ton_amount = await ton_service.calculate_ton_amount(9.99)
print(f"TON amount for $9.99: {ton_amount}")
```

## Monitoring and Troubleshooting

### 1. Payment Status Monitoring

Monitor TON payments through:

#### Database Queries
```sql
-- Check recent TON payments
SELECT 
    payment_id,
    user_id,
    ton_amount,
    usd_amount,
    status,
    created_at
FROM ton_payments 
ORDER BY created_at DESC 
LIMIT 10;

-- Check payment status distribution
SELECT status, COUNT(*) 
FROM ton_payments 
GROUP BY status;
```

#### Log Monitoring
```bash
# Monitor application logs
tail -f logs/app.log | grep "TON"

# Check for payment processing
grep "ton_payment" logs/app.log
```

### 2. Common Issues and Solutions

#### Issue: "TON master wallet not configured"
**Solution**: Ensure `TON_MASTER_WALLET` is set in your environment variables.

#### Issue: "Failed to fetch TON price"
**Solution**: 
- Check internet connectivity
- Verify CoinGecko API is accessible
- Consider implementing price caching

#### Issue: "Transaction not found"
**Solution**:
- Verify the transaction hash on TON explorer
- Check if the transaction is confirmed
- Ensure correct network (mainnet/testnet)

#### Issue: "Payment timeout"
**Solution**:
- Increase `TON_PAYMENT_TIMEOUT_MINUTES`
- Check TON network congestion
- Verify sufficient transaction fees

### 3. TON Network Explorers

- **Mainnet**: [tonscan.org](https://tonscan.org)
- **Testnet**: [testnet.tonscan.org](https://testnet.tonscan.org)

Use these to verify transactions and wallet balances.

## Security Best Practices

### 1. Private Key Security

- **Never commit private keys** to version control
- **Use environment variables** for sensitive data
- **Encrypt private keys** at rest if possible
- **Limit access** to production environment variables
- **Rotate keys** periodically

### 2. Wallet Management

- **Use dedicated wallets** for the bot (don't mix with personal funds)
- **Monitor wallet balance** regularly
- **Set up alerts** for low balance or unusual activity
- **Keep backup** of wallet seed phrases securely

### 3. Network Security

- **Use HTTPS** for all API communications
- **Validate transaction data** before processing
- **Implement rate limiting** for payment endpoints
- **Log all payment activities** for audit trails

### 4. Operational Security

```bash
# Set restrictive permissions on .env file
chmod 600 .env

# Use secrets management in production
# Example with Docker secrets:
docker secret create ton_private_key /path/to/private_key
```

### 5. Monitoring and Alerts

Set up monitoring for:
- Failed payment attempts
- Unusual transaction patterns
- Wallet balance changes
- API errors and timeouts

## Production Deployment Checklist

- [ ] TON master wallet created and funded
- [ ] Environment variables configured securely
- [ ] Database migration completed
- [ ] Payment flow tested on testnet
- [ ] Monitoring and logging configured
- [ ] Security measures implemented
- [ ] Backup procedures established
- [ ] Team trained on TON payment operations

## Support and Resources

### Official TON Resources
- [TON Documentation](https://docs.ton.org)
- [TON Developer Portal](https://ton.org/dev)
- [TON GitHub](https://github.com/ton-blockchain)

### Python Libraries
- [pytoniq](https://github.com/yungwine/pytoniq) - TON blockchain library
- [pytoniq-core](https://github.com/yungwine/pytoniq-core) - Core TON functionality
- [pytonconnect](https://github.com/XaBbl4/pytonconnect) - TON Connect integration

### Community
- [TON Community](https://t.me/toncoin)
- [TON Developers](https://t.me/tondev_eng)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/ton-blockchain)

For additional support, please refer to the project documentation or contact the development team.