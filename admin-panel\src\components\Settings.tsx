import React, { useState, useEffect } from 'react';
import { useSettings, useUpdateSettings } from '../services/api';
import { BotSettings } from '../types/api';

const Settings: React.FC = () => {
  const { data: settingsData, isLoading, error: fetchError, refetch } = useSettings();
  const updateSettingsMutation = useUpdateSettings();
  
  const [settings, setSettings] = useState<BotSettings>({
    id: 0,
    free_data_limit: 0,
    free_duration_days: 30,
    required_channels: [],
    payment_provider_token: '',
    trial_vpn_panel_id: undefined,
    max_trials_per_user: 1,
    trial_reset_days: 30,
    auto_notify_trial_reset: false,
    created_at: '',
    updated_at: ''
  });
  const [success, setSuccess] = useState('');

  useEffect(() => {
    if (settingsData) {
      setSettings(settingsData);
    }
  }, [settingsData]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await updateSettingsMutation.mutateAsync(settings);
      setSuccess('Settings updated successfully');
    } catch (err) {
      console.error('Settings update error:', err);
    }
  };

  const handleInputChange = (field: keyof BotSettings, value: string | number | boolean | string[]) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleReset = () => {
    if (settingsData) {
      setSettings(settingsData);
      setSuccess('');
    }
    refetch();
  };

  if (isLoading) {
    return <div className="loading">Loading settings...</div>;
  }

  return (
    <div>
      <div className="page-header">
        <h1>Settings</h1>
        <p>Configure your VPN bot settings and preferences</p>
      </div>

      {(fetchError || updateSettingsMutation.error) && (
        <div className="error">
          {fetchError?.message || updateSettingsMutation.error?.message || 'An error occurred'}
        </div>
      )}
      {success && <div className="success">{success}</div>}

      <form onSubmit={handleSubmit}>
        {/* Trial Configuration */}
        <div className="card">
          <h3 style={{ marginBottom: '1.5rem' }}>Trial Configuration</h3>
          
          <div className="form-row">
            <div className="form-group">
              <label>Free Data Limit (GB)</label>
              <input
                type="number"
                className="form-control"
                value={settings.free_data_limit}
                onChange={(e) => handleInputChange('free_data_limit', parseInt(e.target.value) || 0)}
                placeholder="10"
                min="0"
              />
            </div>
            
            <div className="form-group">
              <label>Free Duration (Days)</label>
              <input
                type="number"
                className="form-control"
                value={settings.free_duration_days}
                onChange={(e) => handleInputChange('free_duration_days', parseInt(e.target.value) || 0)}
                placeholder="30"
                min="1"
              />
            </div>
          </div>
          
          <div className="form-row">
            <div className="form-group">
              <label>Max Trials Per User</label>
              <input
                type="number"
                className="form-control"
                value={settings.max_trials_per_user}
                onChange={(e) => handleInputChange('max_trials_per_user', parseInt(e.target.value) || 1)}
                placeholder="1"
                min="1"
              />
            </div>
            
            <div className="form-group">
              <label>Trial Reset Days</label>
              <input
                type="number"
                className="form-control"
                value={settings.trial_reset_days}
                onChange={(e) => handleInputChange('trial_reset_days', parseInt(e.target.value) || 30)}
                placeholder="30"
                min="1"
              />
            </div>
          </div>
          
          <div className="form-group">
            <label>
              <input
                type="checkbox"
                checked={settings.auto_notify_trial_reset}
                onChange={(e) => handleInputChange('auto_notify_trial_reset', e.target.checked)}
                style={{ marginRight: '0.5rem' }}
              />
              Auto-notify users about trial reset
            </label>
          </div>
        </div>

        {/* Payment Configuration */}
        <div className="card">
          <h3 style={{ marginBottom: '1.5rem' }}>Payment Configuration</h3>
          
          <div className="form-group">
            <label>Payment Provider Token</label>
            <input
              type="password"
              className="form-control"
              value={settings.payment_provider_token || ''}
              onChange={(e) => handleInputChange('payment_provider_token', e.target.value)}
              placeholder="Your payment provider token"
            />
            <small style={{ color: '#6c757d' }}>Token for payment processing (e.g., Stripe, PayPal)</small>
          </div>
        </div>

        {/* Channel Requirements */}
        <div className="card">
          <h3 style={{ marginBottom: '1.5rem' }}>Required Channels</h3>
          
          <div className="form-group">
            <label>Required Channels (one per line)</label>
            <textarea
              className="form-control"
              value={settings.required_channels.join('\n')}
              onChange={(e) => handleInputChange('required_channels', e.target.value.split('\n').filter(ch => ch.trim()))}
              placeholder="@channel1\n@channel2\n@channel3"
              rows={4}
            />
            <small style={{ color: '#6c757d' }}>Users must join these channels to use the bot</small>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="card">
          <div style={{ display: 'flex', gap: '1rem', justifyContent: 'flex-end' }}>
            <button 
              type="button" 
              className="btn" 
              onClick={handleReset}
              style={{ backgroundColor: '#6c757d', color: 'white' }}
              disabled={updateSettingsMutation.isPending}
            >
              Reset
            </button>
            <button 
              type="submit" 
              className="btn btn-primary"
              disabled={updateSettingsMutation.isPending}
            >
              {updateSettingsMutation.isPending ? 'Saving...' : 'Save Settings'}
            </button>
          </div>
        </div>
      </form>

      {/* Information Card */}
      <div className="card" style={{ backgroundColor: '#e7f3ff', border: '1px solid #b3d9ff' }}>
        <h4 style={{ color: '#0066cc', marginBottom: '1rem' }}>💡 Configuration Tips</h4>
        <ul style={{ color: '#004499', marginBottom: 0 }}>
          <li>Keep your bot token secure and never share it publicly</li>
          <li>Test your welcome and help messages before saving</li>
          <li>Set reasonable limits for users per panel to maintain performance</li>
          <li>Update payment methods regularly to reflect current options</li>
          <li>Use clear and helpful messages to improve user experience</li>
        </ul>
      </div>
    </div>
  );
};

export default Settings;