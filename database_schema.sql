-- Telegram VPN Bot Database Schema
-- Optimized for PostgreSQL 12+

-- Create database (run this separately as superuser)
-- CREATE DATABASE telegram_vpn_bot;

-- Connect to the database before running the rest
-- \c telegram_vpn_bot;

-- <PERSON><PERSON> custom types
CREATE TYPE subscription_status AS ENUM ('active', 'inactive', 'pending');
CREATE TYPE payment_status AS ENUM ('pending', 'completed', 'failed', 'refunded');
CREATE TYPE reward_type AS ENUM ('data', 'time', 'premium', 'trial_reset');

-- Users table
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    telegram_id BIGINT UNIQUE NOT NULL,
    username <PERSON><PERSON><PERSON><PERSON>(255),
    first_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(255),
    language_code VARCHAR(10),
    is_admin BOOLEAN DEFAULT FALSE,
    is_premium BOOLEAN DEFAULT FALSE,
    has_used_trial BOOLEAN DEFAULT FALSE,
    trial_count INTEGER DEFAULT 0,
    last_trial_at TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    last_active TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notification_enabled BOOLEAN DEFAULT TRUE,
    total_data_used BIGINT DEFAULT 0,
    command_count INTEGER DEFAULT 0,
    -- Referral fields
    referral_code VARCHAR(50) UNIQUE,
    referred_by BIGINT REFERENCES users(id) ON DELETE SET NULL,
    referral_count INTEGER DEFAULT 0,
    total_referral_rewards BIGINT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for users table
CREATE INDEX idx_users_telegram_id ON users(telegram_id);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_is_admin ON users(is_admin);
CREATE INDEX idx_users_is_premium ON users(is_premium);
CREATE INDEX idx_users_is_active ON users(is_active);
CREATE INDEX idx_users_referral_code ON users(referral_code);
CREATE INDEX idx_users_referred_by ON users(referred_by);

-- VPN panels table
CREATE TABLE vpn_panels (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    base_url VARCHAR(255) NOT NULL,
    api_username VARCHAR(255) NOT NULL,
    api_password VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    proxies JSONB,
    inbounds JSONB,
    max_users INTEGER DEFAULT 1000,
    current_users INTEGER DEFAULT 0,
    total_accounts_created INTEGER DEFAULT 0,
    last_health_check TIMESTAMP,
    is_healthy BOOLEAN DEFAULT TRUE,
    response_time_ms REAL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Premium plans table
CREATE TABLE premium_plans (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    price DECIMAL(10, 2) NOT NULL,
    duration_days INTEGER NOT NULL,
    data_limit BIGINT NOT NULL, -- in bytes
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE vpn_accounts (
    id SERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    vpn_panel_id INTEGER NOT NULL REFERENCES vpn_panels(id) ON DELETE CASCADE,
    plan_id INTEGER REFERENCES premium_plans(id) ON DELETE SET NULL,
    username VARCHAR(255) UNIQUE NOT NULL,
    uuid VARCHAR(36) UNIQUE, -- Marzban UUID
    data_limit BIGINT NOT NULL, -- in bytes
    used_data BIGINT DEFAULT 0, -- in bytes
    expire_date TIMESTAMP,
    status VARCHAR(50) DEFAULT 'active',
    is_active BOOLEAN DEFAULT TRUE,
    is_trial BOOLEAN DEFAULT FALSE,
    last_usage_check TIMESTAMP,
    last_connection TIMESTAMP,
    connection_count INTEGER DEFAULT 0,
    config_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for vpn_accounts table
CREATE INDEX idx_vpn_accounts_user_id ON vpn_accounts(user_id);
CREATE INDEX idx_vpn_accounts_vpn_panel_id ON vpn_accounts(vpn_panel_id);
CREATE INDEX idx_vpn_accounts_plan_id ON vpn_accounts(plan_id);
CREATE INDEX idx_vpn_accounts_username ON vpn_accounts(username);
CREATE INDEX idx_vpn_accounts_uuid ON vpn_accounts(uuid);
CREATE INDEX idx_vpn_accounts_expire_date ON vpn_accounts(expire_date);
CREATE INDEX idx_vpn_accounts_status ON vpn_accounts(status);
CREATE INDEX idx_vpn_accounts_is_active ON vpn_accounts(is_active);
CREATE INDEX idx_vpn_accounts_is_trial ON vpn_accounts(is_trial);
CREATE INDEX idx_vpn_user_active ON vpn_accounts(user_id, is_active);
CREATE INDEX idx_vpn_panel_active ON vpn_accounts(vpn_panel_id, is_active);
CREATE INDEX idx_vpn_status_active ON vpn_accounts(status, is_active);
CREATE INDEX idx_vpn_expire_active ON vpn_accounts(expire_date, is_active);
CREATE INDEX idx_vpn_trial_active ON vpn_accounts(is_trial, is_active);

-- Unique constraint for user-panel-trial combination
CREATE UNIQUE INDEX uq_user_panel_trial ON vpn_accounts(user_id, vpn_panel_id, is_trial) WHERE is_trial = TRUE;

CREATE TABLE channels (
    id SERIAL PRIMARY KEY,
    channel_id VARCHAR(255) UNIQUE NOT NULL, -- Telegram internal channel ID or username
    channel_name VARCHAR(255) NOT NULL,
    channel_url TEXT,
    description TEXT,
    is_required BOOLEAN DEFAULT TRUE,
    is_active BOOLEAN DEFAULT TRUE,
    is_advertising_enabled BOOLEAN DEFAULT FALSE,
    advertising_message TEXT,
    advertising_frequency INTEGER DEFAULT 24, -- hours
    last_advertised_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for channels table
CREATE INDEX idx_channels_channel_id ON channels(channel_id);
CREATE INDEX idx_channels_is_required ON channels(is_required);
CREATE INDEX idx_channels_is_active ON channels(is_active);
CREATE INDEX idx_channels_is_advertising_enabled ON channels(is_advertising_enabled);

CREATE TABLE channel_subscriptions (
    id SERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    channel_id VARCHAR(255) NOT NULL, -- Changed to match Telegram channel ID format
    status subscription_status DEFAULT 'pending',
    subscribed_at TIMESTAMP,
    unsubscribed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, channel_id)
);

-- Create indexes for channel_subscriptions table
CREATE INDEX idx_channel_subscriptions_user_id ON channel_subscriptions(user_id);
CREATE INDEX idx_channel_subscriptions_channel_id ON channel_subscriptions(channel_id);
CREATE INDEX idx_channel_subscriptions_status ON channel_subscriptions(status);

CREATE TABLE advertisement_logs (
    id SERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    channel_id INTEGER NOT NULL REFERENCES channels(id) ON DELETE CASCADE,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for advertisement_logs table
CREATE INDEX idx_advertisement_logs_user_sent_at ON advertisement_logs(user_id, sent_at);
CREATE INDEX idx_advertisement_logs_channel_sent_at ON advertisement_logs(channel_id, sent_at);

CREATE TABLE failed_payments (
    id SERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    payment_data JSONB NOT NULL,
    failure_reason TEXT NOT NULL,
    retry_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for failed_payments table
CREATE INDEX idx_failed_payments_user_retry ON failed_payments(user_id, retry_count);
CREATE INDEX idx_failed_payments_created_retry ON failed_payments(created_at, retry_count);

CREATE TABLE referrals (
    id SERIAL PRIMARY KEY,
    referrer_id BIGINT NOT NULL,
    referred_id BIGINT NOT NULL,
    referral_code VARCHAR(50) NOT NULL,
    status ENUM('pending', 'completed', 'rewarded') DEFAULT 'pending',
    reward_type reward_type DEFAULT 'data',
    reward_amount BIGINT DEFAULT 0, -- bytes for data, days for time
    reward_given_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (referrer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (referred_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_referral (referrer_id, referred_id),
);

CREATE INDEX idx_referrer ON referrals(referrer_id);
CREATE INDEX idx_referred ON referrals(referred_id);
CREATE INDEX idx_code ON referrals(referral_code);
CREATE INDEX idx_status ON referrals(status);

CREATE TABLE referral_codes (
    id SERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    uses_count INTEGER DEFAULT 0,
    max_uses INTEGER DEFAULT 0, -- 0 = unlimited
    is_active BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
);

CREATE INDEX idx_user ON referral_codes(user_id);
CREATE INDEX idx_code ON referral_codes(code);
CREATE INDEX idx_active ON referral_codes(is_active);

CREATE TABLE referral_rewards (
    id SERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    referral_id INTEGER NOT NULL,
    reward_type reward_type NOT NULL,
    reward_amount BIGINT NOT NULL,
    description TEXT,
    is_claimed BOOLEAN DEFAULT FALSE,
    claimed_at TIMESTAMP NULL,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (referral_id) REFERENCES referrals(id) ON DELETE CASCADE,
);

CREATE INDEX idx_user ON referral_rewards(user_id);
CREATE INDEX idx_referral ON referral_rewards(referral_id);
CREATE INDEX idx_claimed ON referral_rewards(is_claimed);
CREATE INDEX idx_expires ON referral_rewards(expires_at);

CREATE TABLE referral_events (
    id SERIAL PRIMARY KEY,
    event_type VARCHAR(50) NOT NULL,
    user_id BIGINT NOT NULL,
    referral_id INTEGER NULL,
    metadata JSONB NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (referral_id) REFERENCES referrals(id) ON DELETE CASCADE,
);

CREATE INDEX idx_event_type ON referral_events(event_type);
CREATE INDEX idx_user_event ON referral_events(user_id, event_type);
CREATE INDEX idx_created_at ON referral_events(created_at);

CREATE TABLE premium_plans (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    price DECIMAL(10, 2) NOT NULL,
    duration_days INTEGER NOT NULL, -- in days
    data_limit BIGINT NOT NULL, -- in bytes
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE premium_subscriptions (
    id SERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    plan_id INTEGER NOT NULL,
    transaction_id VARCHAR(255) UNIQUE NOT NULL, -- Telegram Payment Charge ID
    amount DECIMAL(10, 2) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending', -- e.g., pending, completed, failed, refunded
    started_at TIMESTAMP,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES premium_plans(id) ON DELETE RESTRICT
);

CREATE TABLE bot_settings (
    id SERIAL PRIMARY KEY,
    free_data_limit BIGINT NOT NULL, -- in bytes
    free_duration_days INTEGER NOT NULL, -- in days
    required_channels JSONB, -- JSONB array of channel usernames or IDs
    payment_provider_token VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
); 