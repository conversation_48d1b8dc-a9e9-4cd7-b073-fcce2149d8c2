<?php

declare(strict_types=1);

namespace VpnBot\Services;

use VpnBot\Config\Config;
use VpnBot\Models\Payment;
use VpnBot\Models\User;
use Psr\Log\LoggerInterface;

class PaymentService
{
    private Config $config;
    private LoggerInterface $logger;

    public function __construct(Config $config, LoggerInterface $logger)
    {
        $this->config = $config;
        $this->logger = $logger;
    }

    public function createPayment(int $userId, int $planId, string $method, float $amount): ?array
    {
        try {
            $user = User::findById($userId);
            if (!$user) {
                return null;
            }

            $payment = new Payment();
            $payment->user_id = $userId;
            $payment->plan_id = $planId;
            $payment->amount = $amount;
            $payment->currency = 'USD';
            $payment->payment_method = $method;
            $payment->status = 'pending';
            $payment->payment_id = $this->generatePaymentId();

            if ($payment->save()) {
                return match ($method) {
                    'stars' => $this->createTelegramStarsPayment($payment),
                    'crypto' => $this->createCryptoPayment($payment),
                    'ton' => $this->createTonPayment($payment),
                    'card' => $this->createCardPayment($payment),
                    default => null,
                };
            }

            return null;

        } catch (\Exception $e) {
            $this->logger->error('Error creating payment: ' . $e->getMessage());
            return null;
        }
    }

    private function createTelegramStarsPayment(Payment $payment): array
    {
        try {
            // Create Telegram Stars invoice
            $invoice = [
                'title' => 'VPN Premium Plan',
                'description' => 'Premium VPN subscription',
                'payload' => $payment->payment_id,
                'provider_token' => '', // Empty for Telegram Stars
                'currency' => 'XTR', // Telegram Stars currency
                'prices' => [
                    ['label' => 'Premium Plan', 'amount' => (int)($payment->amount * 100)] // Stars amount
                ],
                'start_parameter' => 'payment_' . $payment->id,
            ];

            return [
                'success' => true,
                'payment_id' => $payment->payment_id,
                'invoice' => $invoice,
                'method' => 'stars'
            ];

        } catch (\Exception $e) {
            $this->logger->error('Error creating Telegram Stars payment: ' . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    private function createCryptoPayment(Payment $payment): array
    {
        try {
            // Integration with NowPayments or similar crypto payment processor
            $apiKey = $this->config->get('payments.nowpayments.api_key');
            
            if (!$apiKey) {
                throw new \Exception('NowPayments API key not configured');
            }

            // Create payment request
            $paymentData = [
                'price_amount' => $payment->amount,
                'price_currency' => 'USD',
                'pay_currency' => 'btc', // Default to Bitcoin
                'order_id' => $payment->payment_id,
                'order_description' => 'VPN Premium Plan',
                'ipn_callback_url' => $this->config->get('bot.webhook_url') . '/payment/crypto/callback',
                'success_url' => 'https://t.me/' . $this->config->get('bot.username'),
                'cancel_url' => 'https://t.me/' . $this->config->get('bot.username'),
            ];

            // In a real implementation, make HTTP request to NowPayments API
            $paymentUrl = 'https://nowpayments.io/payment/' . $payment->payment_id;

            return [
                'success' => true,
                'payment_id' => $payment->payment_id,
                'payment_url' => $paymentUrl,
                'method' => 'crypto'
            ];

        } catch (\Exception $e) {
            $this->logger->error('Error creating crypto payment: ' . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    private function createTonPayment(Payment $payment): array
    {
        try {
            // TON blockchain payment integration
            $masterWallet = $this->config->get('payments.ton.master_wallet');
            
            if (!$masterWallet) {
                throw new \Exception('TON master wallet not configured');
            }

            // Generate unique payment address or use master wallet with memo
            $paymentAddress = $masterWallet;
            $memo = $payment->payment_id;
            $tonAmount = $this->convertUsdToTon($payment->amount);

            return [
                'success' => true,
                'payment_id' => $payment->payment_id,
                'wallet_address' => $paymentAddress,
                'amount' => $tonAmount,
                'memo' => $memo,
                'method' => 'ton',
                'timeout_minutes' => $this->config->get('payments.ton.timeout_minutes', 30)
            ];

        } catch (\Exception $e) {
            $this->logger->error('Error creating TON payment: ' . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    private function createCardPayment(Payment $payment): array
    {
        try {
            // Integration with payment provider (Stripe, PayPal, etc.)
            $providerToken = $this->config->get('payments.provider_token');
            
            if (!$providerToken) {
                throw new \Exception('Payment provider token not configured');
            }

            // Create Telegram invoice for card payment
            $invoice = [
                'title' => 'VPN Premium Plan',
                'description' => 'Premium VPN subscription',
                'payload' => $payment->payment_id,
                'provider_token' => $providerToken,
                'currency' => 'USD',
                'prices' => [
                    ['label' => 'Premium Plan', 'amount' => (int)($payment->amount * 100)] // Amount in cents
                ],
                'start_parameter' => 'payment_' . $payment->id,
            ];

            return [
                'success' => true,
                'payment_id' => $payment->payment_id,
                'invoice' => $invoice,
                'method' => 'card'
            ];

        } catch (\Exception $e) {
            $this->logger->error('Error creating card payment: ' . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    public function verifyPayment(string $paymentId): bool
    {
        try {
            $payment = Payment::findByPaymentId($paymentId);
            if (!$payment) {
                return false;
            }

            // Verify payment based on method
            $verified = match ($payment->payment_method) {
                'stars' => $this->verifyTelegramStarsPayment($payment),
                'crypto' => $this->verifyCryptoPayment($payment),
                'ton' => $this->verifyTonPayment($payment),
                'card' => $this->verifyCardPayment($payment),
                default => false,
            };

            if ($verified) {
                $payment->status = 'completed';
                $payment->paid_at = date('Y-m-d H:i:s');
                $payment->save();

                // Activate premium for user
                $this->activatePremium($payment->user_id, $payment->plan_id);
            }

            return $verified;

        } catch (\Exception $e) {
            $this->logger->error('Error verifying payment: ' . $e->getMessage());
            return false;
        }
    }

    private function verifyTelegramStarsPayment(Payment $payment): bool
    {
        // In a real implementation, verify with Telegram API
        return true; // Placeholder
    }

    private function verifyCryptoPayment(Payment $payment): bool
    {
        // In a real implementation, verify with crypto payment processor
        return true; // Placeholder
    }

    private function verifyTonPayment(Payment $payment): bool
    {
        // In a real implementation, verify TON blockchain transaction
        return true; // Placeholder
    }

    private function verifyCardPayment(Payment $payment): bool
    {
        // In a real implementation, verify with payment provider
        return true; // Placeholder
    }

    private function activatePremium(int $userId, int $planId): bool
    {
        try {
            $user = User::findById($userId);
            if (!$user) {
                return false;
            }

            $user->is_premium = true;
            $user->save();

            // Create VPN account for the user
            $vpnService = new VpnService($this->config, $this->logger);
            // Implementation would create premium VPN account

            return true;

        } catch (\Exception $e) {
            $this->logger->error('Error activating premium: ' . $e->getMessage());
            return false;
        }
    }

    private function generatePaymentId(): string
    {
        return 'pay_' . time() . '_' . bin2hex(random_bytes(8));
    }

    private function convertUsdToTon(float $usdAmount): float
    {
        // In a real implementation, get current TON/USD exchange rate
        // For now, use a fixed rate (placeholder)
        $tonRate = 2.5; // 1 TON = $2.5 (example)
        return round($usdAmount / $tonRate, 2);
    }

    public function getPaymentHistory(int $userId): array
    {
        try {
            // In a real implementation, query payments table
            return [];

        } catch (\Exception $e) {
            $this->logger->error('Error getting payment history: ' . $e->getMessage());
            return [];
        }
    }
}
