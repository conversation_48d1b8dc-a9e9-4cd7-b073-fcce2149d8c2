# Architecture Overview

This document provides a comprehensive overview of the VPN Telegram Bot architecture, including system design, component structure, and data flow.

## System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Telegram      │    │   Admin Panel   │    │   Marzban API   │
│   Bot API       │    │   (React)       │    │   (VPN Backend) │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          │                      │                      │
          ▼                      ▼                      ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Application Layer                            │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   Bot Handlers  │   Middleware    │      Services               │
│   - Commands    │   - Auth        │      - Auth Service         │
│   - Callbacks   │   - Rate Limit  │      - VPN Service          │
│   - Payments    │   - Logging     │      - Payment Service      │
│   - Errors      │   - Subscription│      - Channel Service      │
│                 │                 │      - Dashboard Service    │
│                 │                 │      - QR Service           │
└─────────────────┴─────────────────┴─────────────────────────────┘
          │                      │                      │
          ▼                      ▼                      ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │     Redis       │    │   File System  │
│   Database      │    │     Cache       │    │   (Logs, QR)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Core Components

### 1. Bot Application (`bot/main.py`)

The main application orchestrates all components:

```python
class TelegramBot:
    def __init__(self):
        self.application = None
        self.db_pool = None
        self.redis_client = None
    
    async def setup_application(self):
        # Initialize database and Redis
        # Setup middleware
        # Register handlers
        # Configure error handling
```

**Responsibilities:**
- Application lifecycle management
- Component initialization
- Handler registration
- Middleware setup

### 2. Handlers (`bot/handlers/`)

Handlers process different types of Telegram updates:

#### Command Handlers (`commands.py`)
- `/start` - Welcome and registration
- `/help` - Help and support information
- `/dashboard` - User dashboard
- `/trial` - Trial VPN account creation
- `/premium` - Premium plans display
- `/accounts` - User VPN accounts

#### Callback Handlers (`callbacks.py`)
- Inline keyboard interactions
- Menu navigation
- Account management
- Payment processing

#### Payment Handlers (`payments.py`)
- Pre-checkout validation
- Successful payment processing
- Refund handling

#### Error Handlers (`errors.py`)
- Exception handling
- User-friendly error messages
- Logging and monitoring

### 3. Middleware (`bot/middleware/`)

Middleware provides cross-cutting concerns:

#### Authentication Middleware (`auth.py`)
```python
class AuthMiddleware:
    async def __call__(self, update, context, next_handler):
        # User registration/update
        # Context population
        # Authorization checks
```

#### Rate Limiting Middleware (`rate_limit.py`)
```python
class RateLimitMiddleware:
    async def __call__(self, update, context, next_handler):
        # Request counting
        # Rate limit enforcement
        # User feedback
```

#### Subscription Middleware (`subscription.py`)
```python
class ChannelSubscriptionMiddleware:
    async def __call__(self, update, context, next_handler):
        # Channel membership verification
        # Subscription status updates
        # Access control
```

#### Logging Middleware (`logging.py`)
```python
class LoggingMiddleware:
    async def __call__(self, update, context, next_handler):
        # Request/response logging
        # Performance monitoring
        # Error tracking
```

### 4. Services (`bot/services/`)

Services encapsulate business logic:

#### Auth Service (`auth_service.py`)
- User management
- Authentication
- Authorization
- Admin operations

#### VPN Service (`vpn_service.py`)
- Marzban API integration
- Account creation/management
- Usage tracking
- Configuration generation

#### Payment Service (`payment_service.py`)
- Premium plan management
- Invoice generation
- Payment processing
- Subscription management

#### Channel Service (`channel_service.py`)
- Channel management
- Membership verification
- Subscription tracking

#### Dashboard Service (`dashboard_service.py`)
- User data aggregation
- Statistics generation
- Report formatting

#### QR Service (`qr_service.py`)
- QR code generation
- Styling and branding
- Base64 encoding

### 5. Utilities (`bot/utils/`)

#### Helpers (`helpers.py`)
- Date/time utilities
- Text formatting
- Security functions
- Validation helpers
- Caching utilities

#### Validators (`validators.py`)
- Input validation
- Data sanitization
- Type checking
- Business rule validation

#### Decorators (`decorators.py`)
- Rate limiting
- Authentication
- Error handling
- Caching
- Performance monitoring

## Data Layer

### Database Schema

#### Users Table
```sql
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    telegram_id BIGINT UNIQUE NOT NULL,
    username VARCHAR(255),
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    language_code VARCHAR(10) DEFAULT 'en',
    is_active BOOLEAN DEFAULT true,
    is_admin BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### VPN Accounts Table
```sql
CREATE TABLE vpn_accounts (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),
    marzban_username VARCHAR(255) UNIQUE NOT NULL,
    account_type VARCHAR(50) NOT NULL,
    expires_at TIMESTAMP,
    data_limit BIGINT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Premium Subscriptions Table
```sql
CREATE TABLE premium_subscriptions (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),
    plan_id VARCHAR(100) NOT NULL,
    starts_at TIMESTAMP NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Redis Cache Structure

#### Rate Limiting
```
rate_limit:user:{user_id}:{window} -> count
rate_limit:command:{user_id}:{command}:{window} -> count
```

#### Session Data
```
session:user:{user_id} -> {user_data}
subscription:user:{user_id} -> {subscription_status}
```

#### Temporary Data
```
temp:trial:{user_id} -> {trial_data}
temp:payment:{user_id} -> {payment_data}
```

## Data Flow

### 1. User Registration Flow

```
User sends /start
    ↓
AuthMiddleware
    ↓
Check if user exists
    ↓
Create/Update user in database
    ↓
Populate context.user_data
    ↓
Command handler processes request
    ↓
Send welcome message
```

### 2. Trial Account Creation Flow

```
User requests trial
    ↓
SubscriptionMiddleware
    ↓
Verify channel membership
    ↓
Check trial eligibility
    ↓
VPN Service
    ↓
Call Marzban API
    ↓
Create account in database
    ↓
Generate QR code
    ↓
Send account details to user
```

### 3. Premium Purchase Flow

```
User selects premium plan
    ↓
Payment Service
    ↓
Generate invoice
    ↓
Telegram Payment API
    ↓
User completes payment
    ↓
Payment webhook
    ↓
Create premium subscription
    ↓
Create VPN account
    ↓
Send account details
```

## Security Architecture

### 1. Authentication & Authorization

- **User Authentication**: Telegram user ID verification
- **Admin Authorization**: Role-based access control
- **API Authentication**: Token-based authentication for Marzban

### 2. Rate Limiting

- **Global Rate Limiting**: Requests per minute per user
- **Command Rate Limiting**: Specific limits for expensive operations
- **Redis-based Tracking**: Distributed rate limiting

### 3. Input Validation

- **Data Sanitization**: All user inputs validated
- **Type Checking**: Strong typing with validators
- **Business Rule Validation**: Domain-specific validation

### 4. Error Handling

- **Graceful Degradation**: Fallback mechanisms
- **User-Friendly Messages**: No technical details exposed
- **Comprehensive Logging**: Security event tracking

## Performance Considerations

### 1. Asynchronous Processing

- **Async/Await**: Non-blocking I/O operations
- **Connection Pooling**: Database connection management
- **Concurrent Updates**: Parallel update processing

### 2. Caching Strategy

- **Redis Caching**: Frequently accessed data
- **Session Management**: User state persistence
- **Rate Limit Tracking**: Fast access counters

### 3. Database Optimization

- **Indexing**: Optimized query performance
- **Connection Pooling**: Efficient resource usage
- **Query Optimization**: Minimal database calls

## Monitoring & Observability

### 1. Logging

- **Structured Logging**: JSON format logs
- **Log Levels**: Debug, Info, Warning, Error
- **Request Tracing**: End-to-end request tracking

### 2. Metrics

- **Performance Metrics**: Response times, throughput
- **Business Metrics**: User registrations, payments
- **Error Metrics**: Error rates, failure patterns

### 3. Health Checks

- **Database Health**: Connection and query health
- **Redis Health**: Cache availability
- **External API Health**: Marzban API status

## Scalability Design

### 1. Horizontal Scaling

- **Stateless Design**: No server-side session state
- **Load Balancing**: Multiple bot instances
- **Database Scaling**: Read replicas, sharding

### 2. Resource Management

- **Connection Pooling**: Efficient resource usage
- **Memory Management**: Garbage collection optimization
- **CPU Optimization**: Async processing

### 3. External Dependencies

- **Circuit Breakers**: Fault tolerance
- **Retry Mechanisms**: Resilient API calls
- **Fallback Strategies**: Graceful degradation

## Development Patterns

### 1. Dependency Injection

- **Service Layer**: Loosely coupled services
- **Configuration**: Environment-based configuration
- **Testing**: Mock-friendly design

### 2. Error Handling

- **Exception Hierarchy**: Structured error types
- **Error Propagation**: Consistent error handling
- **User Feedback**: Meaningful error messages

### 3. Code Organization

- **Modular Design**: Clear separation of concerns
- **Single Responsibility**: Each module has one purpose
- **Interface Segregation**: Minimal interfaces

This architecture provides a robust, scalable, and maintainable foundation for the VPN Telegram Bot, ensuring good performance, security, and developer experience.