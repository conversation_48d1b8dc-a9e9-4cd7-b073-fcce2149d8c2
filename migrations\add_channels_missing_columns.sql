-- Migration: Add missing columns to channels table
-- This migration adds columns that were missing from the original channels table schema

-- Add missing columns to channels table if they don't exist
DO $$
BEGIN
    -- Add description column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'channels' AND column_name = 'description') THEN
        ALTER TABLE channels ADD COLUMN description TEXT;
    END IF;
    
    -- Add is_active column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'channels' AND column_name = 'is_active') THEN
        ALTER TABLE channels ADD COLUMN is_active BOOLEAN DEFAULT TRUE;
    END IF;
    
    -- Add is_advertising_enabled column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'channels' AND column_name = 'is_advertising_enabled') THEN
        ALTER TABLE channels ADD COLUMN is_advertising_enabled BOOLEAN DEFAULT FALSE;
    END IF;
    
    -- Add advertising_message column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'channels' AND column_name = 'advertising_message') THEN
        ALTER TABLE channels ADD COLUMN advertising_message TEXT;
    END IF;
    
    -- Add advertising_frequency column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'channels' AND column_name = 'advertising_frequency') THEN
        ALTER TABLE channels ADD COLUMN advertising_frequency INTEGER DEFAULT 24;
    END IF;
    
    -- Add last_advertised_at column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'channels' AND column_name = 'last_advertised_at') THEN
        ALTER TABLE channels ADD COLUMN last_advertised_at TIMESTAMP;
    END IF;
END $$;

-- Create missing indexes if they don't exist
DO $$
BEGIN
    -- Index for channel_id
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_channels_channel_id') THEN
        CREATE INDEX idx_channels_channel_id ON channels(channel_id);
    END IF;
    
    -- Index for is_required
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_channels_is_required') THEN
        CREATE INDEX idx_channels_is_required ON channels(is_required);
    END IF;
    
    -- Index for is_active
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_channels_is_active') THEN
        CREATE INDEX idx_channels_is_active ON channels(is_active);
    END IF;
    
    -- Index for is_advertising_enabled
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_channels_is_advertising_enabled') THEN
        CREATE INDEX idx_channels_is_advertising_enabled ON channels(is_advertising_enabled);
    END IF;
END $$;

-- Update existing channels to have is_active = TRUE if NULL
UPDATE channels SET is_active = TRUE WHERE is_active IS NULL;
UPDATE channels SET is_advertising_enabled = FALSE WHERE is_advertising_enabled IS NULL;
UPDATE channels SET advertising_frequency = 24 WHERE advertising_frequency IS NULL;
