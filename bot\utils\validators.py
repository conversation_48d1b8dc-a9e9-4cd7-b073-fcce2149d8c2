"""Validators for input validation and data sanitization."""

import re
import ipad<PERSON>
from typing import Any, Dict, List, Optional, Union
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class BaseValidator:
    """Base validator class."""
    
    def __init__(self, required: bool = True, allow_none: bool = False):
        self.required = required
        self.allow_none = allow_none
    
    def validate(self, value: Any) -> tuple[bool, Optional[str]]:
        """Validate value and return (is_valid, error_message)."""
        if value is None:
            if self.allow_none:
                return True, None
            if self.required:
                return False, "This field is required"
            return True, None
        
        return self._validate_value(value)
    
    def _validate_value(self, value: Any) -> tuple[bool, Optional[str]]:
        """Override this method in subclasses."""
        return True, None


class StringValidator(BaseValidator):
    """String validator with length and pattern constraints."""
    
    def __init__(self, min_length: int = 0, max_length: int = None, 
                 pattern: str = None, **kwargs):
        super().__init__(**kwargs)
        self.min_length = min_length
        self.max_length = max_length
        self.pattern = re.compile(pattern) if pattern else None
    
    def _validate_value(self, value: Any) -> tuple[bool, Optional[str]]:
        if not isinstance(value, str):
            return False, "Value must be a string"
        
        if len(value) < self.min_length:
            return False, f"String must be at least {self.min_length} characters long"
        
        if self.max_length and len(value) > self.max_length:
            return False, f"String must be at most {self.max_length} characters long"
        
        if self.pattern and not self.pattern.match(value):
            return False, "String format is invalid"
        
        return True, None


class IntegerValidator(BaseValidator):
    """Integer validator with range constraints."""
    
    def __init__(self, min_value: int = None, max_value: int = None, **kwargs):
        super().__init__(**kwargs)
        self.min_value = min_value
        self.max_value = max_value
    
    def _validate_value(self, value: Any) -> tuple[bool, Optional[str]]:
        try:
            int_value = int(value)
        except (ValueError, TypeError):
            return False, "Value must be an integer"
        
        if self.min_value is not None and int_value < self.min_value:
            return False, f"Value must be at least {self.min_value}"
        
        if self.max_value is not None and int_value > self.max_value:
            return False, f"Value must be at most {self.max_value}"
        
        return True, None


class FloatValidator(BaseValidator):
    """Float validator with range constraints."""
    
    def __init__(self, min_value: float = None, max_value: float = None, **kwargs):
        super().__init__(**kwargs)
        self.min_value = min_value
        self.max_value = max_value
    
    def _validate_value(self, value: Any) -> tuple[bool, Optional[str]]:
        try:
            float_value = float(value)
        except (ValueError, TypeError):
            return False, "Value must be a number"
        
        if self.min_value is not None and float_value < self.min_value:
            return False, f"Value must be at least {self.min_value}"
        
        if self.max_value is not None and float_value > self.max_value:
            return False, f"Value must be at most {self.max_value}"
        
        return True, None


class EmailValidator(BaseValidator):
    """Email address validator."""
    
    EMAIL_PATTERN = re.compile(
        r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    )
    
    def _validate_value(self, value: Any) -> tuple[bool, Optional[str]]:
        if not isinstance(value, str):
            return False, "Email must be a string"
        
        if not self.EMAIL_PATTERN.match(value):
            return False, "Invalid email format"
        
        return True, None


class URLValidator(BaseValidator):
    """URL validator."""
    
    URL_PATTERN = re.compile(
        r'^https?://(?:[-\w.])+(?:[:\d]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:#(?:\w*))?)?$'
    )
    
    def _validate_value(self, value: Any) -> tuple[bool, Optional[str]]:
        if not isinstance(value, str):
            return False, "URL must be a string"
        
        if not self.URL_PATTERN.match(value):
            return False, "Invalid URL format"
        
        return True, None


class UsernameValidator(BaseValidator):
    """Username validator for VPN accounts."""
    
    USERNAME_PATTERN = re.compile(r'^[a-zA-Z0-9_-]{3,32}$')
    
    def _validate_value(self, value: Any) -> tuple[bool, Optional[str]]:
        if not isinstance(value, str):
            return False, "Username must be a string"
        
        if not self.USERNAME_PATTERN.match(value):
            return False, "Username must be 3-32 characters long and contain only letters, numbers, underscores, and hyphens"
        
        return True, None


class TelegramUserIdValidator(BaseValidator):
    """Telegram user ID validator."""
    
    def _validate_value(self, value: Any) -> tuple[bool, Optional[str]]:
        try:
            user_id = int(value)
        except (ValueError, TypeError):
            return False, "User ID must be an integer"
        
        if user_id <= 0:
            return False, "User ID must be positive"
        
        # Telegram user IDs are typically in a specific range
        if user_id > **********:  # 2^31 - 1
            return False, "Invalid Telegram user ID"
        
        return True, None


class IPAddressValidator(BaseValidator):
    """IP address validator (IPv4 and IPv6)."""
    
    def __init__(self, version: int = None, **kwargs):
        super().__init__(**kwargs)
        self.version = version  # 4, 6, or None for both
    
    def _validate_value(self, value: Any) -> tuple[bool, Optional[str]]:
        if not isinstance(value, str):
            return False, "IP address must be a string"
        
        try:
            ip = ipaddress.ip_address(value)
            
            if self.version == 4 and not isinstance(ip, ipaddress.IPv4Address):
                return False, "Must be an IPv4 address"
            
            if self.version == 6 and not isinstance(ip, ipaddress.IPv6Address):
                return False, "Must be an IPv6 address"
            
            return True, None
        
        except ValueError:
            return False, "Invalid IP address format"


class DateTimeValidator(BaseValidator):
    """DateTime validator."""
    
    def __init__(self, format_str: str = "%Y-%m-%d %H:%M:%S", 
                 min_date: datetime = None, max_date: datetime = None, **kwargs):
        super().__init__(**kwargs)
        self.format_str = format_str
        self.min_date = min_date
        self.max_date = max_date
    
    def _validate_value(self, value: Any) -> tuple[bool, Optional[str]]:
        if isinstance(value, datetime):
            dt = value
        elif isinstance(value, str):
            try:
                dt = datetime.strptime(value, self.format_str)
            except ValueError:
                return False, f"Invalid datetime format. Expected: {self.format_str}"
        else:
            return False, "Value must be a datetime object or string"
        
        if self.min_date and dt < self.min_date:
            return False, f"Date must be after {self.min_date}"
        
        if self.max_date and dt > self.max_date:
            return False, f"Date must be before {self.max_date}"
        
        return True, None


class ChoiceValidator(BaseValidator):
    """Choice validator for predefined options."""
    
    def __init__(self, choices: List[Any], **kwargs):
        super().__init__(**kwargs)
        self.choices = choices
    
    def _validate_value(self, value: Any) -> tuple[bool, Optional[str]]:
        if value not in self.choices:
            return False, f"Value must be one of: {', '.join(map(str, self.choices))}"
        
        return True, None


class ListValidator(BaseValidator):
    """List validator with item validation."""
    
    def __init__(self, item_validator: BaseValidator = None, 
                 min_length: int = 0, max_length: int = None, **kwargs):
        super().__init__(**kwargs)
        self.item_validator = item_validator
        self.min_length = min_length
        self.max_length = max_length
    
    def _validate_value(self, value: Any) -> tuple[bool, Optional[str]]:
        if not isinstance(value, list):
            return False, "Value must be a list"
        
        if len(value) < self.min_length:
            return False, f"List must have at least {self.min_length} items"
        
        if self.max_length and len(value) > self.max_length:
            return False, f"List must have at most {self.max_length} items"
        
        if self.item_validator:
            for i, item in enumerate(value):
                is_valid, error = self.item_validator.validate(item)
                if not is_valid:
                    return False, f"Item {i}: {error}"
        
        return True, None


class DictValidator(BaseValidator):
    """Dictionary validator with field validation."""
    
    def __init__(self, field_validators: Dict[str, BaseValidator] = None, 
                 allow_extra_fields: bool = True, **kwargs):
        super().__init__(**kwargs)
        self.field_validators = field_validators or {}
        self.allow_extra_fields = allow_extra_fields
    
    def _validate_value(self, value: Any) -> tuple[bool, Optional[str]]:
        if not isinstance(value, dict):
            return False, "Value must be a dictionary"
        
        # Validate required fields
        for field_name, validator in self.field_validators.items():
            if validator.required and field_name not in value:
                return False, f"Missing required field: {field_name}"
        
        # Validate existing fields
        for field_name, field_value in value.items():
            if field_name in self.field_validators:
                validator = self.field_validators[field_name]
                is_valid, error = validator.validate(field_value)
                if not is_valid:
                    return False, f"Field '{field_name}': {error}"
            elif not self.allow_extra_fields:
                return False, f"Unexpected field: {field_name}"
        
        return True, None


class VPNConfigValidator:
    """Validator for VPN configuration data."""
    
    def __init__(self):
        self.validator = DictValidator({
            'username': UsernameValidator(),
            'subscription_url': URLValidator(),
            'expires_at': DateTimeValidator(allow_none=True),
            'data_limit_gb': FloatValidator(min_value=0, allow_none=True),
            'is_active': BaseValidator(allow_none=True)
        })
    
    def validate(self, config_data: Dict[str, Any]) -> tuple[bool, List[str]]:
        """Validate VPN configuration and return (is_valid, errors)."""
        is_valid, error = self.validator.validate(config_data)
        if not is_valid:
            return False, [error]
        
        return True, []


class PremiumPlanValidator:
    """Validator for premium plan data."""
    
    def __init__(self):
        self.validator = DictValidator({
            'name': StringValidator(min_length=1, max_length=100),
            'description': StringValidator(max_length=500, allow_none=True),
            'price': FloatValidator(min_value=0.01),
            'duration_days': IntegerValidator(min_value=1),
            'data_limit_gb': FloatValidator(min_value=0.1),
            'is_active': BaseValidator(allow_none=True)
        })
    
    def validate(self, plan_data: Dict[str, Any]) -> tuple[bool, List[str]]:
        """Validate premium plan and return (is_valid, errors)."""
        is_valid, error = self.validator.validate(plan_data)
        if not is_valid:
            return False, [error]
        
        return True, []


class PaymentDataValidator:
    """Validator for payment data."""
    
    def __init__(self):
        self.validator = DictValidator({
            'user_id': TelegramUserIdValidator(),
            'plan_id': IntegerValidator(min_value=1),
            'amount': FloatValidator(min_value=0.01),
            'currency': StringValidator(min_length=3, max_length=3),
            'telegram_payment_charge_id': StringValidator(min_length=1),
            'provider_payment_charge_id': StringValidator(min_length=1, allow_none=True)
        })
    
    def validate(self, payment_data: Dict[str, Any]) -> tuple[bool, List[str]]:
        """Validate payment data and return (is_valid, errors)."""
        is_valid, error = self.validator.validate(payment_data)
        if not is_valid:
            return False, [error]
        
        return True, []


class ChannelDataValidator:
    """Validator for channel data."""
    
    def __init__(self):
        self.validator = DictValidator({
            'channel_id': StringValidator(min_length=1),
            'channel_username': StringValidator(min_length=1, allow_none=True),
            'channel_title': StringValidator(min_length=1, allow_none=True),
            'is_required': BaseValidator(allow_none=True),
            'join_url': URLValidator(allow_none=True)
        })
    
    def validate(self, channel_data: Dict[str, Any]) -> tuple[bool, List[str]]:
        """Validate channel data and return (is_valid, errors)."""
        is_valid, error = self.validator.validate(channel_data)
        if not is_valid:
            return False, [error]
        
        return True, []


class ValidationError(Exception):
    """Custom validation error."""
    
    def __init__(self, message: str, field: str = None):
        self.message = message
        self.field = field
        super().__init__(self.message)


class DataValidator:
    """Main data validator class."""
    
    def __init__(self):
        self.vpn_config_validator = VPNConfigValidator()
        self.premium_plan_validator = PremiumPlanValidator()
        self.payment_data_validator = PaymentDataValidator()
        self.channel_data_validator = ChannelDataValidator()
    
    def validate_vpn_config(self, config_data: Dict[str, Any]) -> None:
        """Validate VPN configuration data."""
        is_valid, errors = self.vpn_config_validator.validate(config_data)
        if not is_valid:
            raise ValidationError(f"VPN config validation failed: {'; '.join(errors)}")
    
    def validate_premium_plan(self, plan_data: Dict[str, Any]) -> None:
        """Validate premium plan data."""
        is_valid, errors = self.premium_plan_validator.validate(plan_data)
        if not is_valid:
            raise ValidationError(f"Premium plan validation failed: {'; '.join(errors)}")
    
    def validate_payment_data(self, payment_data: Dict[str, Any]) -> None:
        """Validate payment data."""
        is_valid, errors = self.payment_data_validator.validate(payment_data)
        if not is_valid:
            raise ValidationError(f"Payment data validation failed: {'; '.join(errors)}")
    
    def validate_channel_data(self, channel_data: Dict[str, Any]) -> None:
        """Validate channel data."""
        is_valid, errors = self.channel_data_validator.validate(channel_data)
        if not is_valid:
            raise ValidationError(f"Channel data validation failed: {'; '.join(errors)}")
    
    def validate_user_input(self, input_type: str, value: Any) -> bool:
        """Validate user input based on type."""
        validators = {
            'username': UsernameValidator(),
            'email': EmailValidator(),
            'url': URLValidator(),
            'user_id': TelegramUserIdValidator(),
            'ip_address': IPAddressValidator(),
            'positive_int': IntegerValidator(min_value=1),
            'positive_float': FloatValidator(min_value=0.01)
        }
        
        if input_type not in validators:
            logger.warning(f"Unknown input type: {input_type}")
            return False
        
        validator = validators[input_type]
        is_valid, _ = validator.validate(value)
        return is_valid


# Global instance
data_validator = DataValidator()