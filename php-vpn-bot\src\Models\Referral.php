<?php

declare(strict_types=1);

namespace VpnBot\Models;

use VpnBot\Database\Connection;
use PDO;

class Referral
{
    public ?int $id = null;
    public int $referrer_id;
    public int $referred_id;
    public string $referral_code;
    public string $status = 'active';
    public float $reward_amount = 0.0;
    public bool $reward_paid = false;
    public ?string $reward_paid_at = null;
    public ?string $created_at = null;
    public ?string $updated_at = null;

    public static function findByReferrerId(int $referrerId): array
    {
        $pdo = Connection::getInstance();
        $stmt = $pdo->prepare('SELECT * FROM referrals WHERE referrer_id = ? ORDER BY created_at DESC');
        $stmt->execute([$referrerId]);
        
        $referrals = [];
        while ($data = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $referrals[] = self::fromArray($data);
        }

        return $referrals;
    }

    public static function findByReferredId(int $referredId): ?Referral
    {
        $pdo = Connection::getInstance();
        $stmt = $pdo->prepare('SELECT * FROM referrals WHERE referred_id = ?');
        $stmt->execute([$referredId]);
        
        $data = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!$data) {
            return null;
        }

        return self::fromArray($data);
    }

    public static function findById(int $id): ?Referral
    {
        $pdo = Connection::getInstance();
        $stmt = $pdo->prepare('SELECT * FROM referrals WHERE id = ?');
        $stmt->execute([$id]);
        
        $data = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!$data) {
            return null;
        }

        return self::fromArray($data);
    }

    public function save(): bool
    {
        $pdo = Connection::getInstance();
        
        if ($this->id === null) {
            return $this->insert($pdo);
        } else {
            return $this->update($pdo);
        }
    }

    private function insert(PDO $pdo): bool
    {
        $sql = '
            INSERT INTO referrals (
                referrer_id, referred_id, referral_code, status,
                reward_amount, reward_paid, reward_paid_at,
                created_at, updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, NOW(), NOW()
            )
        ';

        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            $this->referrer_id,
            $this->referred_id,
            $this->referral_code,
            $this->status,
            $this->reward_amount,
            $this->reward_paid,
            $this->reward_paid_at,
        ]);

        if ($result) {
            $this->id = (int)$pdo->lastInsertId();
        }

        return $result;
    }

    private function update(PDO $pdo): bool
    {
        $sql = '
            UPDATE referrals SET
                status = ?, reward_amount = ?, reward_paid = ?,
                reward_paid_at = ?, updated_at = NOW()
            WHERE id = ?
        ';

        $stmt = $pdo->prepare($sql);
        return $stmt->execute([
            $this->status,
            $this->reward_amount,
            $this->reward_paid,
            $this->reward_paid_at,
            $this->id,
        ]);
    }

    public static function fromArray(array $data): Referral
    {
        $referral = new Referral();
        $referral->id = $data['id'] ?? null;
        $referral->referrer_id = (int)$data['referrer_id'];
        $referral->referred_id = (int)$data['referred_id'];
        $referral->referral_code = $data['referral_code'];
        $referral->status = $data['status'];
        $referral->reward_amount = (float)$data['reward_amount'];
        $referral->reward_paid = (bool)$data['reward_paid'];
        $referral->reward_paid_at = $data['reward_paid_at'];
        $referral->created_at = $data['created_at'];
        $referral->updated_at = $data['updated_at'];

        return $referral;
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'referrer_id' => $this->referrer_id,
            'referred_id' => $this->referred_id,
            'referral_code' => $this->referral_code,
            'status' => $this->status,
            'reward_amount' => $this->reward_amount,
            'reward_paid' => $this->reward_paid,
            'reward_paid_at' => $this->reward_paid_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }

    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    public function isRewardPaid(): bool
    {
        return $this->reward_paid;
    }
}
