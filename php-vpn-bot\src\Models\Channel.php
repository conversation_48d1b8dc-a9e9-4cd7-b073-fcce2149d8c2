<?php

declare(strict_types=1);

namespace VpnBot\Models;

use VpnBot\Database\Connection;
use PDO;

class Channel
{
    public ?int $id = null;
    public string $channel_id;
    public string $channel_name;
    public ?string $channel_url = null;
    public ?string $invite_link = null;
    public bool $is_required = true;
    public bool $is_active = true;
    public int $priority = 0;
    public ?string $description = null;
    public int $subscriber_count = 0;
    public bool $advertising_enabled = false;
    public ?string $advertising_message = null;
    public ?string $created_at = null;
    public ?string $updated_at = null;

    public static function findAll(): array
    {
        $pdo = Connection::getInstance();
        $stmt = $pdo->query('SELECT * FROM channels ORDER BY priority ASC, created_at ASC');
        
        $channels = [];
        while ($data = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $channels[] = self::fromArray($data);
        }

        return $channels;
    }

    public static function findRequired(): array
    {
        $pdo = Connection::getInstance();
        $stmt = $pdo->prepare('SELECT * FROM channels WHERE is_required = ? AND is_active = ? ORDER BY priority ASC');
        $stmt->execute([true, true]);
        
        $channels = [];
        while ($data = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $channels[] = self::fromArray($data);
        }

        return $channels;
    }

    public static function findById(int $id): ?Channel
    {
        $pdo = Connection::getInstance();
        $stmt = $pdo->prepare('SELECT * FROM channels WHERE id = ?');
        $stmt->execute([$id]);
        
        $data = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!$data) {
            return null;
        }

        return self::fromArray($data);
    }

    public static function findByChannelId(string $channelId): ?Channel
    {
        $pdo = Connection::getInstance();
        $stmt = $pdo->prepare('SELECT * FROM channels WHERE channel_id = ?');
        $stmt->execute([$channelId]);
        
        $data = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!$data) {
            return null;
        }

        return self::fromArray($data);
    }

    public function save(): bool
    {
        $pdo = Connection::getInstance();
        
        if ($this->id === null) {
            return $this->insert($pdo);
        } else {
            return $this->update($pdo);
        }
    }

    private function insert(PDO $pdo): bool
    {
        $sql = '
            INSERT INTO channels (
                channel_id, channel_name, channel_url, invite_link,
                is_required, is_active, priority, description,
                subscriber_count, advertising_enabled, advertising_message,
                created_at, updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW()
            )
        ';

        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            $this->channel_id,
            $this->channel_name,
            $this->channel_url,
            $this->invite_link,
            $this->is_required,
            $this->is_active,
            $this->priority,
            $this->description,
            $this->subscriber_count,
            $this->advertising_enabled,
            $this->advertising_message,
        ]);

        if ($result) {
            $this->id = (int)$pdo->lastInsertId();
        }

        return $result;
    }

    private function update(PDO $pdo): bool
    {
        $sql = '
            UPDATE channels SET
                channel_name = ?, channel_url = ?, invite_link = ?,
                is_required = ?, is_active = ?, priority = ?,
                description = ?, subscriber_count = ?,
                advertising_enabled = ?, advertising_message = ?,
                updated_at = NOW()
            WHERE id = ?
        ';

        $stmt = $pdo->prepare($sql);
        return $stmt->execute([
            $this->channel_name,
            $this->channel_url,
            $this->invite_link,
            $this->is_required,
            $this->is_active,
            $this->priority,
            $this->description,
            $this->subscriber_count,
            $this->advertising_enabled,
            $this->advertising_message,
            $this->id,
        ]);
    }

    public static function fromArray(array $data): Channel
    {
        $channel = new Channel();
        $channel->id = $data['id'] ?? null;
        $channel->channel_id = $data['channel_id'];
        $channel->channel_name = $data['channel_name'];
        $channel->channel_url = $data['channel_url'];
        $channel->invite_link = $data['invite_link'];
        $channel->is_required = (bool)$data['is_required'];
        $channel->is_active = (bool)$data['is_active'];
        $channel->priority = (int)$data['priority'];
        $channel->description = $data['description'];
        $channel->subscriber_count = (int)$data['subscriber_count'];
        $channel->advertising_enabled = (bool)$data['advertising_enabled'];
        $channel->advertising_message = $data['advertising_message'];
        $channel->created_at = $data['created_at'];
        $channel->updated_at = $data['updated_at'];

        return $channel;
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'channel_id' => $this->channel_id,
            'channel_name' => $this->channel_name,
            'channel_url' => $this->channel_url,
            'invite_link' => $this->invite_link,
            'is_required' => $this->is_required,
            'is_active' => $this->is_active,
            'priority' => $this->priority,
            'description' => $this->description,
            'subscriber_count' => $this->subscriber_count,
            'advertising_enabled' => $this->advertising_enabled,
            'advertising_message' => $this->advertising_message,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }

    public function getUrl(): string
    {
        if ($this->invite_link) {
            return $this->invite_link;
        }
        
        if ($this->channel_url) {
            return $this->channel_url;
        }
        
        // Generate URL from channel_id
        $channelId = ltrim($this->channel_id, '@');
        return "https://t.me/{$channelId}";
    }
}
