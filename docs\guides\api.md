# API Documentation

This document provides comprehensive API documentation for the VPN Telegram Bot project, including internal services, external integrations, and admin panel endpoints.

## Table of Contents

1. [Bot API](#bot-api)
2. [Admin Panel API](#admin-panel-api)
3. [Marzban Integration](#marzban-integration)
4. [Payment Integration](#payment-integration)
5. [Database Schema](#database-schema)
6. [<PERSON><PERSON><PERSON>](#error-handling)
7. [Authentication](#authentication)
8. [Rate Limiting](#rate-limiting)

## Bot API

### Webhook Endpoint

#### POST /webhook

Receives updates from Telegram Bot API.

**Request Headers:**
```
Content-Type: application/json
X-Telegram-Bot-Api-Secret-Token: <webhook_secret>
```

**Request Body:**
```json
{
  "update_id": *********,
  "message": {
    "message_id": 1,
    "from": {
      "id": *********,
      "is_bot": false,
      "first_name": "<PERSON>",
      "username": "john_doe"
    },
    "chat": {
      "id": *********,
      "first_name": "<PERSON>",
      "username": "john_doe",
      "type": "private"
    },
    "date": **********,
    "text": "/start"
  }
}
```

**Response:**
```json
{
  "status": "ok"
}
```

### Health Check

#### GET /health

Returns application health status.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "services": {
    "database": true,
    "redis": true,
    "marzban": true,
    "telegram": true
  },
  "version": "1.0.0"
}
```

### Metrics

#### GET /metrics

Returns Prometheus metrics.

**Response:**
```
# HELP bot_requests_total Total bot requests
# TYPE bot_requests_total counter
bot_requests_total{method="start_command",endpoint="success"} 1234

# HELP bot_active_users Number of active users
# TYPE bot_active_users gauge
bot_active_users 567
```

## Admin Panel API

### Authentication

#### POST /api/auth/login

Authenticate admin user.

**Request Body:**
```json
{
  "username": "admin",
  "password": "secure_password"
}
```

**Response:**
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "username": "admin",
    "role": "admin",
    "permissions": ["users.read", "users.write", "vpn.manage"]
  }
}
```

#### POST /api/auth/refresh

Refresh authentication token.

**Request Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

#### POST /api/auth/logout

Logout admin user.

**Request Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

### Users Management

#### GET /api/users

Retrieve users list with pagination and filtering.

**Query Parameters:**
- `page` (integer): Page number (default: 1)
- `limit` (integer): Items per page (default: 20, max: 100)
- `search` (string): Search by username or name
- `status` (string): Filter by status (active, banned, trial)
- `sort` (string): Sort field (created_at, last_active)
- `order` (string): Sort order (asc, desc)

**Request Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": 1,
        "telegram_id": *********,
        "username": "john_doe",
        "first_name": "John",
        "last_name": "Doe",
        "status": "active",
        "is_premium": true,
        "trial_used": true,
        "created_at": "2024-01-01T12:00:00Z",
        "last_active": "2024-01-15T10:30:00Z",
        "vpn_accounts_count": 2,
        "total_spent": 50.00
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 150,
      "pages": 8
    }
  }
}
```

#### GET /api/users/{user_id}

Retrieve specific user details.

**Request Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "telegram_id": *********,
      "username": "john_doe",
      "first_name": "John",
      "last_name": "Doe",
      "status": "active",
      "is_premium": true,
      "trial_used": true,
      "created_at": "2024-01-01T12:00:00Z",
      "last_active": "2024-01-15T10:30:00Z",
      "subscription_status": {
        "subscribed_channels": [
          {"channel_id": "@channel1", "subscribed": true},
          {"channel_id": "@channel2", "subscribed": false}
        ],
        "all_subscribed": false
      },
      "vpn_accounts": [
        {
          "id": 1,
          "username": "user_*********_1",
          "status": "active",
          "expires_at": "2024-02-01T12:00:00Z",
          "data_limit": ***********,
          "used_traffic": **********,
          "created_at": "2024-01-01T12:00:00Z"
        }
      ],
      "payments": [
        {
          "id": 1,
          "amount": 25.00,
          "currency": "USD",
          "status": "completed",
          "plan_name": "Premium Monthly",
          "created_at": "2024-01-01T12:00:00Z"
        }
      ]
    }
  }
}
```

#### PUT /api/users/{user_id}

Update user information.

**Request Headers:**
```
Authorization: Bearer <token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "status": "banned",
  "ban_reason": "Violation of terms",
  "notes": "User reported for spam"
}
```

**Response:**
```json
{
  "success": true,
  "message": "User updated successfully",
  "data": {
    "user": {
      "id": 1,
      "status": "banned",
      "ban_reason": "Violation of terms",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  }
}
```

#### DELETE /api/users/{user_id}

Delete user and all associated data.

**Request Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "message": "User deleted successfully"
}
```

### VPN Accounts Management

#### GET /api/vpn/accounts

Retrieve VPN accounts list.

**Query Parameters:**
- `page` (integer): Page number
- `limit` (integer): Items per page
- `user_id` (integer): Filter by user ID
- `status` (string): Filter by status (active, expired, disabled)
- `search` (string): Search by username

**Request Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "accounts": [
      {
        "id": 1,
        "user_id": *********,
        "username": "user_*********_1",
        "status": "active",
        "expires_at": "2024-02-01T12:00:00Z",
        "data_limit": ***********,
        "used_traffic": **********,
        "created_at": "2024-01-01T12:00:00Z",
        "user": {
          "telegram_id": *********,
          "username": "john_doe",
          "first_name": "John"
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 500,
      "pages": 25
    }
  }
}
```

#### POST /api/vpn/accounts

Create new VPN account.

**Request Headers:**
```
Authorization: Bearer <token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "user_id": *********,
  "plan_type": "premium",
  "duration_days": 30,
  "data_limit_gb": 100,
  "note": "Manual account creation"
}
```

**Response:**
```json
{
  "success": true,
  "message": "VPN account created successfully",
  "data": {
    "account": {
      "id": 2,
      "username": "user_*********_2",
      "status": "active",
      "expires_at": "2024-02-15T12:00:00Z",
      "data_limit": ***********0,
      "subscription_url": "vless://...",
      "qr_code": "data:image/png;base64,..."
    }
  }
}
```

#### PUT /api/vpn/accounts/{account_id}

Update VPN account.

**Request Headers:**
```
Authorization: Bearer <token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "status": "disabled",
  "expires_at": "2024-03-01T12:00:00Z",
  "data_limit": ***********,
  "note": "Extended subscription"
}
```

**Response:**
```json
{
  "success": true,
  "message": "VPN account updated successfully",
  "data": {
    "account": {
      "id": 1,
      "status": "disabled",
      "expires_at": "2024-03-01T12:00:00Z",
      "data_limit": ***********,
      "updated_at": "2024-01-15T10:30:00Z"
    }
  }
}
```

#### DELETE /api/vpn/accounts/{account_id}

Delete VPN account.

**Request Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "message": "VPN account deleted successfully"
}
```

#### POST /api/vpn/accounts/{account_id}/reset-usage

Reset account usage statistics.

**Request Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "message": "Account usage reset successfully",
  "data": {
    "account": {
      "id": 1,
      "used_traffic": 0,
      "reset_at": "2024-01-15T10:30:00Z"
    }
  }
}
```

### Analytics

#### GET /api/analytics/dashboard

Retrieve dashboard analytics.

**Request Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "overview": {
      "total_users": 1500,
      "active_users": 1200,
      "premium_users": 800,
      "total_vpn_accounts": 2000,
      "active_vpn_accounts": 1800,
      "total_revenue": 15000.00,
      "monthly_revenue": 2500.00
    },
    "growth": {
      "new_users_today": 25,
      "new_users_this_week": 150,
      "new_users_this_month": 600,
      "revenue_growth_percentage": 15.5
    },
    "usage": {
      "total_traffic_gb": 50000,
      "traffic_this_month_gb": 8000,
      "average_usage_per_user_gb": 25
    }
  }
}
```

#### GET /api/analytics/users

Retrieve user analytics.

**Query Parameters:**
- `period` (string): Time period (day, week, month, year)
- `start_date` (string): Start date (YYYY-MM-DD)
- `end_date` (string): End date (YYYY-MM-DD)

**Request Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "registrations": [
      {"date": "2024-01-01", "count": 45},
      {"date": "2024-01-02", "count": 52},
      {"date": "2024-01-03", "count": 38}
    ],
    "active_users": [
      {"date": "2024-01-01", "count": 1200},
      {"date": "2024-01-02", "count": 1250},
      {"date": "2024-01-03", "count": 1180}
    ],
    "premium_conversions": [
      {"date": "2024-01-01", "count": 15},
      {"date": "2024-01-02", "count": 18},
      {"date": "2024-01-03", "count": 12}
    ]
  }
}
```

#### GET /api/analytics/revenue

Retrieve revenue analytics.

**Query Parameters:**
- `period` (string): Time period (day, week, month, year)
- `start_date` (string): Start date (YYYY-MM-DD)
- `end_date` (string): End date (YYYY-MM-DD)

**Request Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "revenue_by_period": [
      {"date": "2024-01-01", "amount": 450.00},
      {"date": "2024-01-02", "amount": 520.00},
      {"date": "2024-01-03", "amount": 380.00}
    ],
    "revenue_by_plan": [
      {"plan": "Premium Monthly", "amount": 8000.00, "count": 320},
      {"plan": "Premium Yearly", "amount": 6000.00, "count": 200},
      {"plan": "Premium Weekly", "amount": 1000.00, "count": 100}
    ],
    "total_revenue": 15000.00,
    "average_revenue_per_user": 18.75
  }
}
```

### System Settings

#### GET /api/settings

Retrieve system settings.

**Request Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "settings": {
      "bot": {
        "welcome_message": "Welcome to VPN Bot!",
        "trial_duration_hours": 24,
        "max_trial_accounts": 1,
        "required_channels": ["@channel1", "@channel2"]
      },
      "vpn": {
        "default_data_limit_gb": 10,
        "trial_data_limit_gb": 5,
        "max_accounts_per_user": 3
      },
      "payments": {
        "currency": "USD",
        "plans": [
          {
            "id": "weekly",
            "name": "Premium Weekly",
            "price": 5.00,
            "duration_days": 7,
            "data_limit_gb": 50
          },
          {
            "id": "monthly",
            "name": "Premium Monthly",
            "price": 15.00,
            "duration_days": 30,
            "data_limit_gb": 200
          }
        ]
      }
    }
  }
}
```

#### PUT /api/settings

Update system settings.

**Request Headers:**
```
Authorization: Bearer <token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "bot": {
    "welcome_message": "Welcome to our VPN service!",
    "trial_duration_hours": 48
  },
  "vpn": {
    "default_data_limit_gb": 15
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Settings updated successfully",
  "data": {
    "updated_settings": {
      "bot.welcome_message": "Welcome to our VPN service!",
      "bot.trial_duration_hours": 48,
      "vpn.default_data_limit_gb": 15
    }
  }
}
```

## Marzban Integration

### Authentication

#### POST /api/admin/token

Authenticate with Marzban panel.

**Request Body:**
```json
{
  "username": "admin",
  "password": "admin_password"
}
```

**Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer"
}
```

### User Management

#### POST /api/user

Create VPN user in Marzban.

**Request Headers:**
```
Authorization: Bearer <marzban_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "username": "user_*********_1",
  "proxies": {
    "vless": {
      "flow": "xtls-rprx-vision"
    },
    "vmess": {},
    "trojan": {}
  },
  "data_limit": ***********,
  "expire": 1706745600,
  "data_limit_reset_strategy": "no_reset",
  "status": "active",
  "note": "Created via Telegram Bot"
}
```

**Response:**
```json
{
  "username": "user_*********_1",
  "status": "active",
  "used_traffic": 0,
  "data_limit": ***********,
  "data_limit_reset_strategy": "no_reset",
  "expire": 1706745600,
  "links": [
    "vless://...",
    "vmess://...",
    "trojan://..."
  ],
  "subscription_url": "https://marzban.example.com/sub/token",
  "created_at": "2024-01-01T12:00:00Z"
}
```

#### GET /api/user/{username}

Retrieve VPN user information.

**Request Headers:**
```
Authorization: Bearer <marzban_token>
```

**Response:**
```json
{
  "username": "user_*********_1",
  "status": "active",
  "used_traffic": **********,
  "data_limit": ***********,
  "data_limit_reset_strategy": "no_reset",
  "expire": 1706745600,
  "created_at": "2024-01-01T12:00:00Z",
  "links": [
    "vless://...",
    "vmess://...",
    "trojan://..."
  ],
  "subscription_url": "https://marzban.example.com/sub/token"
}
```

#### PUT /api/user/{username}

Update VPN user.

**Request Headers:**
```
Authorization: Bearer <marzban_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "status": "disabled",
  "data_limit": ***********,
  "expire": 1709337600
}
```

**Response:**
```json
{
  "username": "user_*********_1",
  "status": "disabled",
  "used_traffic": **********,
  "data_limit": ***********,
  "expire": 1709337600,
  "modified_at": "2024-01-15T10:30:00Z"
}
```

#### DELETE /api/user/{username}

Delete VPN user.

**Request Headers:**
```
Authorization: Bearer <marzban_token>
```

**Response:**
```json
{
  "message": "User deleted successfully"
}
```

#### POST /api/user/{username}/reset

Reset user traffic usage.

**Request Headers:**
```
Authorization: Bearer <marzban_token>
```

**Response:**
```json
{
  "username": "user_*********_1",
  "used_traffic": 0,
  "reset_at": "2024-01-15T10:30:00Z"
}
```

### System Information

#### GET /api/system

Retrieve system information.

**Request Headers:**
```
Authorization: Bearer <marzban_token>
```

**Response:**
```json
{
  "version": "0.4.0",
  "users_count": 1500,
  "users_active": 1200,
  "incoming_bandwidth": ***********00,
  "outgoing_bandwidth": ***********00,
  "incoming_bandwidth_speed": 104857600,
  "outgoing_bandwidth_speed": 209715200
}
```

## Payment Integration

### Telegram Payments

#### Webhook Processing

Telegram sends payment updates to the bot webhook endpoint.

**Pre-checkout Query:**
```json
{
  "update_id": *********,
  "pre_checkout_query": {
    "id": "query_id",
    "from": {
      "id": *********,
      "first_name": "John"
    },
    "currency": "USD",
    "total_amount": 1500,
    "invoice_payload": "plan_monthly_*********"
  }
}
```

**Successful Payment:**
```json
{
  "update_id": 123456790,
  "message": {
    "message_id": 2,
    "from": {
      "id": *********,
      "first_name": "John"
    },
    "chat": {
      "id": *********,
      "type": "private"
    },
    "date": **********,
    "successful_payment": {
      "currency": "USD",
      "total_amount": 1500,
      "invoice_payload": "plan_monthly_*********",
      "telegram_payment_charge_id": "charge_id",
      "provider_payment_charge_id": "provider_charge_id"
    }
  }
}
```

### Invoice Creation

```python
# Example invoice creation
invoice_data = {
    "title": "Premium Monthly Plan",
    "description": "30 days of premium VPN access with 200GB data",
    "payload": "plan_monthly_*********",
    "provider_token": "PAYMENT_PROVIDER_TOKEN",
    "currency": "USD",
    "prices": [
        {"label": "Premium Monthly", "amount": 1500}  # $15.00
    ],
    "start_parameter": "premium_monthly",
    "photo_url": "https://example.com/premium-plan.jpg",
    "photo_size": 512,
    "photo_width": 512,
    "photo_height": 512,
    "need_name": False,
    "need_phone_number": False,
    "need_email": False,
    "need_shipping_address": False,
    "send_phone_number_to_provider": False,
    "send_email_to_provider": False,
    "is_flexible": False
}
```

## Database Schema

### Users Table

```sql
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    telegram_id BIGINT UNIQUE NOT NULL,
    username VARCHAR(255),
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    language_code VARCHAR(10) DEFAULT 'en',
    status VARCHAR(20) DEFAULT 'active',
    is_premium BOOLEAN DEFAULT FALSE,
    trial_used BOOLEAN DEFAULT FALSE,
    ban_reason TEXT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_active TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_users_telegram_id ON users(telegram_id);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_created_at ON users(created_at);
```

### VPN Accounts Table

```sql
CREATE TABLE vpn_accounts (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
    username VARCHAR(255) UNIQUE NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    account_type VARCHAR(20) DEFAULT 'trial',
    data_limit BIGINT,
    used_traffic BIGINT DEFAULT 0,
    expires_at TIMESTAMP,
    subscription_url TEXT,
    marzban_user_id VARCHAR(255),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_vpn_accounts_user_id ON vpn_accounts(user_id);
CREATE INDEX idx_vpn_accounts_username ON vpn_accounts(username);
CREATE INDEX idx_vpn_accounts_status ON vpn_accounts(status);
CREATE INDEX idx_vpn_accounts_expires_at ON vpn_accounts(expires_at);
```

### Payments Table

```sql
CREATE TABLE payments (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    status VARCHAR(20) DEFAULT 'pending',
    plan_id VARCHAR(50),
    plan_name VARCHAR(255),
    telegram_payment_charge_id VARCHAR(255),
    provider_payment_charge_id VARCHAR(255),
    invoice_payload TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP
);

CREATE INDEX idx_payments_user_id ON payments(user_id);
CREATE INDEX idx_payments_status ON payments(status);
CREATE INDEX idx_payments_created_at ON payments(created_at);
```

### Subscription Checks Table

```sql
CREATE TABLE subscription_checks (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
    channel_id VARCHAR(255) NOT NULL,
    is_subscribed BOOLEAN DEFAULT FALSE,
    checked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, channel_id)
);

CREATE INDEX idx_subscription_checks_user_id ON subscription_checks(user_id);
CREATE INDEX idx_subscription_checks_channel_id ON subscription_checks(channel_id);
```

## Error Handling

### Error Response Format

All API endpoints return errors in a consistent format:

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "email",
      "reason": "Invalid email format"
    },
    "timestamp": "2024-01-15T10:30:00Z",
    "request_id": "req_*********"
  }
}
```

### Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `VALIDATION_ERROR` | 400 | Invalid input data |
| `AUTHENTICATION_ERROR` | 401 | Invalid or missing authentication |
| `AUTHORIZATION_ERROR` | 403 | Insufficient permissions |
| `NOT_FOUND` | 404 | Resource not found |
| `RATE_LIMIT_EXCEEDED` | 429 | Too many requests |
| `INTERNAL_ERROR` | 500 | Internal server error |
| `SERVICE_UNAVAILABLE` | 503 | External service unavailable |
| `VPN_SERVICE_ERROR` | 502 | Marzban API error |
| `PAYMENT_ERROR` | 402 | Payment processing error |

### Error Handling Examples

```python
# Python error handling
try:
    response = await api_client.get('/api/users/123')
except APIError as e:
    if e.code == 'NOT_FOUND':
        print("User not found")
    elif e.code == 'AUTHORIZATION_ERROR':
        print("Access denied")
    else:
        print(f"API error: {e.message}")
```

```javascript
// JavaScript error handling
try {
  const response = await fetch('/api/users/123');
  if (!response.ok) {
    const error = await response.json();
    throw new APIError(error.error.code, error.error.message);
  }
  const data = await response.json();
} catch (error) {
  if (error.code === 'NOT_FOUND') {
    console.log('User not found');
  } else {
    console.error('API error:', error.message);
  }
}
```

## Authentication

### JWT Token Structure

```json
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "user_id": 1,
    "username": "admin",
    "role": "admin",
    "permissions": ["users.read", "users.write", "vpn.manage"],
    "iat": **********,
    "exp": **********
  }
}
```

### Permission System

| Permission | Description |
|------------|-------------|
| `users.read` | View user information |
| `users.write` | Create, update, delete users |
| `vpn.read` | View VPN accounts |
| `vpn.write` | Manage VPN accounts |
| `payments.read` | View payment information |
| `payments.write` | Process refunds |
| `analytics.read` | View analytics data |
| `settings.read` | View system settings |
| `settings.write` | Update system settings |
| `admin.full` | Full administrative access |

## Rate Limiting

### Rate Limit Headers

All API responses include rate limiting headers:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
X-RateLimit-Window: 3600
```

### Rate Limit Tiers

| Endpoint Category | Requests per Hour | Burst Limit |
|-------------------|-------------------|-------------|
| Authentication | 10 | 5 |
| User Management | 1000 | 50 |
| VPN Management | 500 | 25 |
| Analytics | 100 | 10 |
| Settings | 50 | 5 |

### Rate Limit Exceeded Response

```json
{
  "success": false,
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Too many requests",
    "details": {
      "limit": 100,
      "window": 3600,
      "reset_at": "2024-01-15T11:30:00Z"
    }
  }
}
```

This API documentation provides comprehensive coverage of all endpoints, request/response formats, error handling, and integration details for the VPN Telegram Bot project.