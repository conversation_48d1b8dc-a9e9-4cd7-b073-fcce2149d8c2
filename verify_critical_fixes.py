#!/usr/bin/env python3
"""
Simple verification script for critical fixes
"""

import sys
from pathlib import Path

# Add the bot directory to the Python path
sys.path.insert(0, str(Path(__file__).parent))

def test_localization():
    """Test localization fixes."""
    try:
        from bot.utils.helpers import get_text
        
        print("Testing localization fixes...")
        
        # Test the keys that were added
        test_keys = [
            'settings.language_changed',
            'settings.invalid_language', 
            'help.commands_title',
            'menu.title'
        ]
        
        languages = ['en', 'fa', 'ru', 'zh']
        
        for lang in languages:
            for key in test_keys:
                text = get_text(key, lang)
                if text.startswith('Missing translation:') or text.startswith('Translation error'):
                    print(f"❌ Missing localization key: {lang}.{key}")
                    return False
                else:
                    print(f"✅ {lang}.{key}: {text[:50]}...")
        
        print("✅ Localization verification completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Localization verification failed: {e}")
        return False

def test_command_handlers():
    """Test command handlers fixes."""
    try:
        from bot.handlers.commands import CommandHandlers
        
        print("Testing command handlers...")
        
        # Check if the class can be instantiated
        handler = CommandHandlers()
        
        # Check if the methods exist with correct signatures
        if hasattr(handler, '_handle_language_selection'):
            print("✅ _handle_language_selection method exists")
        
        if hasattr(handler, '_handle_language_selection_buttons'):
            print("✅ _handle_language_selection_buttons method exists")
        
        print("✅ Command handlers verification completed!")
        return True
        
    except Exception as e:
        print(f"❌ Command handlers verification failed: {e}")
        return False

def test_payment_service():
    """Test payment service fixes."""
    try:
        from bot.services.payment_service import PaymentService
        
        print("Testing payment service...")
        
        # Check if the class can be instantiated
        service = PaymentService()
        
        # Check if create_invoice method has the correct signature
        import inspect
        sig = inspect.signature(service.create_invoice)
        params = list(sig.parameters.keys())
        
        expected_params = ['plan_id', 'user_id', 'payment_method', 'amount']
        for param in expected_params:
            if param in params:
                print(f"✅ create_invoice has parameter: {param}")
            else:
                print(f"❌ create_invoice missing parameter: {param}")
                return False
        
        print("✅ Payment service verification completed!")
        return True
        
    except Exception as e:
        print(f"❌ Payment service verification failed: {e}")
        return False

def test_database_schema():
    """Test database schema files."""
    try:
        print("Testing database schema files...")
        
        # Check if migration file exists
        migration_file = Path("migrations/add_channels_missing_columns.sql")
        if migration_file.exists():
            print("✅ Migration file exists")
        else:
            print("❌ Migration file missing")
            return False
        
        # Check if init.sql has been updated
        init_file = Path("01-schema.sql/init.sql")
        if init_file.exists():
            with open(init_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'is_active BOOLEAN DEFAULT TRUE' in content:
                    print("✅ init.sql has been updated with is_active column")
                else:
                    print("❌ init.sql missing is_active column")
                    return False
        else:
            print("❌ init.sql file not found")
            return False
        
        print("✅ Database schema verification completed!")
        return True
        
    except Exception as e:
        print(f"❌ Database schema verification failed: {e}")
        return False

def main():
    """Main verification function."""
    print("🔧 Starting critical fixes verification...\n")
    
    tests = [
        ("Database Schema", test_database_schema),
        ("Localization", test_localization),
        ("Command Handlers", test_command_handlers),
        ("Payment Service", test_payment_service)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Testing {test_name}:")
        print("-" * 40)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - PASSED")
            else:
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            print(f"❌ {test_name} - ERROR: {e}")
    
    print("\n" + "="*50)
    print(f"📊 VERIFICATION SUMMARY: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All critical fixes verified successfully!")
        print("\nNext steps:")
        print("1. Run the database migration: python -c \"import asyncio; from run_critical_fixes import CriticalFixesRunner; asyncio.run(CriticalFixesRunner().run_database_migration())\"")
        print("2. Restart the bot")
        print("3. Test bot functionality manually")
        return True
    else:
        print("⚠️  Some fixes need attention. Please review the failed tests.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
