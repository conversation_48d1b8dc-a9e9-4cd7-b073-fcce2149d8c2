# Installation Guide

This guide will walk you through setting up the VPN Telegram Bot project from scratch.

## Prerequisites

### System Requirements
- **Operating System**: Linux, macOS, or Windows
- **Python**: 3.11 or higher
- **Node.js**: 18 or higher (for admin panel)
- **Package Manager**: pnpm (for Node.js packages)
- **Database**: PostgreSQL 13+
- **Cache**: Redis 6+
- **Docker**: Latest version (optional but recommended)

### Required Accounts and Services
1. **Telegram Bot Token**: Create a bot via [@<PERSON>t<PERSON>ather](https://t.me/botfather)
2. **Marzban Panel**: VPN management system
3. **PostgreSQL Database**: Local or cloud instance
4. **Redis Instance**: Local or cloud instance

## Installation Methods

### Method 1: Docker Installation (Recommended)

#### 1. Clone the Repository
```bash
git clone <repository-url>
cd vpn-telegram-bot
```

#### 2. Environment Configuration
Copy the environment template:
```bash
cp .env.example .env
```

Edit `.env` with your configuration:
```env
# Bot Configuration
BOT_TOKEN=your_telegram_bot_token
BOT_USERNAME=your_bot_username

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/vpn_bot
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=vpn_bot
DATABASE_USER=your_db_user
DATABASE_PASSWORD=your_db_password

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=your_redis_password

# Marzban API Configuration
MARZBAN_URL=https://your-marzban-panel.com
MARZBAN_USERNAME=admin
MARZBAN_PASSWORD=your_marzban_password

# Payment Configuration
PAYMENT_PROVIDER_TOKEN=your_payment_provider_token

# Admin Panel Configuration
ADMIN_PANEL_URL=http://localhost:3002
JWT_SECRET=your_jwt_secret

# Channel Configuration
REQUIRED_CHANNELS=@channel1,@channel2
```

#### 3. Build and Start Services
```bash
docker-compose up --build
```

This will start:
- PostgreSQL database (port 5432)
- Redis cache (port 6379)
- Telegram bot
- Admin panel (port 3002)

#### 4. Database Initialization
The database will be automatically initialized with the required schema.

### Method 2: Manual Installation

#### 1. Clone and Setup Python Environment
```bash
git clone <repository-url>
cd vpn-telegram-bot

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On Linux/macOS:
source venv/bin/activate

# Install Python dependencies
pip install -r requirements.txt
```

#### 2. Setup Admin Panel
```bash
cd admin-panel

# Install dependencies with pnpm
pnpm install

# Build the admin panel
pnpm run build
```

#### 3. Database Setup

**Install PostgreSQL:**
- **Ubuntu/Debian**: `sudo apt install postgresql postgresql-contrib`
- **CentOS/RHEL**: `sudo yum install postgresql postgresql-server`
- **macOS**: `brew install postgresql`
- **Windows**: Download from [PostgreSQL website](https://www.postgresql.org/download/windows/)

**Create Database:**
```sql
-- Connect to PostgreSQL as superuser
sudo -u postgres psql

-- Create database and user
CREATE DATABASE vpn_bot;
CREATE USER vpn_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE vpn_bot TO vpn_user;
\q
```

**Initialize Schema:**
```bash
# Run database migrations
python -c "from bot.database import init_db; import asyncio; asyncio.run(init_db())"
```

#### 4. Redis Setup

**Install Redis:**
- **Ubuntu/Debian**: `sudo apt install redis-server`
- **CentOS/RHEL**: `sudo yum install redis`
- **macOS**: `brew install redis`
- **Windows**: Use Docker or WSL

**Start Redis:**
```bash
# Start Redis service
sudo systemctl start redis

# Enable auto-start
sudo systemctl enable redis
```

#### 5. Environment Configuration
Create and configure `.env` file as shown in Docker method.

#### 6. Start Services

**Start the Bot:**
```bash
python -m bot.main
```

**Start Admin Panel:**
```bash
cd admin-panel
pnpm start
```

## Post-Installation Setup

### 1. Bot Configuration

#### Set Bot Commands
Send these commands to [@BotFather](https://t.me/botfather):

```
/setcommands

start - شروع ربات / Start the bot
help - راهنما / Help and support
dashboard - داشبورد / User dashboard
trial - اکانت آزمایشی / Trial account
premium - پلن های پریمیم / Premium plans
accounts - اکانت های من / My accounts
```

#### Set Bot Description
```
/setdescription

🔐 VPN Bot - Your Gateway to Internet Freedom

✅ Free trial accounts
💎 Premium VPN plans
🌍 Global server locations
⚡ High-speed connections
🛡️ Secure and private

Start with /start command!
```

### 2. Channel Setup

1. Create your Telegram channels
2. Add your bot as an administrator
3. Update `REQUIRED_CHANNELS` in `.env`
4. Restart the bot

### 3. Payment Setup

1. Contact [@BotFather](https://t.me/botfather)
2. Use `/setpayments` command
3. Choose a payment provider
4. Get your payment provider token
5. Update `PAYMENT_PROVIDER_TOKEN` in `.env`

### 4. Marzban Integration

1. Install and configure Marzban panel
2. Create an admin user
3. Update Marzban credentials in `.env`
4. Test the connection

## Verification

### 1. Test Bot Functionality
```bash
# Send /start to your bot
# Verify all commands work
# Test trial account creation
# Test premium plan display
```

### 2. Test Admin Panel
```bash
# Open http://localhost:3002
# Login with admin credentials
# Verify dashboard loads
# Check user statistics
```

### 3. Test Database Connection
```bash
# Check database tables
psql -U vpn_user -d vpn_bot -c "\dt"

# Verify data insertion
psql -U vpn_user -d vpn_bot -c "SELECT COUNT(*) FROM users;"
```

### 4. Test Redis Connection
```bash
# Test Redis connectivity
redis-cli ping
# Should return: PONG
```

## Troubleshooting

### Common Issues

**Bot not responding:**
- Check bot token validity
- Verify network connectivity
- Check logs for errors

**Database connection failed:**
- Verify PostgreSQL is running
- Check database credentials
- Ensure database exists

**Redis connection failed:**
- Verify Redis is running
- Check Redis configuration
- Test with `redis-cli`

**Admin panel not loading:**
- Check if pnpm build completed successfully
- Verify port 3002 is available
- Check admin panel logs

### Log Locations

- **Bot logs**: `logs/bot.log`
- **Admin panel logs**: Browser console
- **Database logs**: PostgreSQL log directory
- **Redis logs**: Redis log directory

## Next Steps

1. Read the [Configuration Guide](./configuration.md) for detailed settings
2. Follow the [Development Guide](./development.md) for customization
3. Check [Bot Features](./bot-features.md) for feature documentation
4. Review [Production Setup](./production-setup.md) for deployment

## Support

If you encounter issues during installation:

1. Check the [Troubleshooting Guide](./troubleshooting.md)
2. Review the [FAQ](./faq.md)
3. Create an issue in the project repository