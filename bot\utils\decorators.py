"""Decorators for common functionality like rate limiting, authentication, and error handling."""

import functools
import asyncio
import time
from typing import Callable, Any, Dict, Optional, Union
from datetime import datetime, timedelta
import logging

from telegram import Update
from telegram.ext import ContextTypes
from telegram.error import TelegramError

from ..database import get_db
from ..redis import get_redis
from ..config import Config
from .helpers import SecurityHelper

logger = logging.getLogger(__name__)


class RateLimitExceeded(Exception):
    """Exception raised when rate limit is exceeded."""
    pass


class AuthenticationRequired(Exception):
    """Exception raised when authentication is required."""
    pass


class PermissionDenied(Exception):
    """Exception raised when permission is denied."""
    pass


def rate_limit(max_calls: int = 10, window_seconds: int = 60, 
               per_user: bool = True, key_func: Callable = None):
    """Rate limiting decorator.
    
    Args:
        max_calls: Maximum number of calls allowed
        window_seconds: Time window in seconds
        per_user: Whether to apply rate limit per user or globally
        key_func: Custom function to generate rate limit key
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE, *args, **kwargs):
            redis_client = get_redis()
            
            # Generate rate limit key
            if key_func:
                rate_key = key_func(update, context)
            elif per_user:
                user_id = update.effective_user.id if update.effective_user else 'anonymous'
                rate_key = f"rate_limit:{func.__name__}:{user_id}"
            else:
                rate_key = f"rate_limit:{func.__name__}:global"
            
            try:
                # Get current count
                current_count = await redis_client.get(rate_key)
                current_count = int(current_count) if current_count else 0
                
                if current_count >= max_calls:
                    logger.warning(f"Rate limit exceeded for key: {rate_key}")
                    raise RateLimitExceeded(f"Rate limit exceeded. Try again in {window_seconds} seconds.")
                
                # Increment counter
                pipe = redis_client.pipeline()
                pipe.incr(rate_key)
                pipe.expire(rate_key, window_seconds)
                await pipe.execute()
                
                return await func(update, context, *args, **kwargs)
                
            except Exception as e:
                if isinstance(e, RateLimitExceeded):
                    if update.effective_chat:
                        await context.bot.send_message(
                            chat_id=update.effective_chat.id,
                            text=f"⚠️ You're sending requests too quickly. Please wait {window_seconds} seconds before trying again."
                        )
                    raise
                logger.error(f"Rate limit error: {e}")
                return await func(update, context, *args, **kwargs)
        
        return wrapper
    return decorator


def require_auth(admin_only: bool = False, premium_only: bool = False):
    """Authentication decorator.
    
    Args:
        admin_only: Require admin privileges
        premium_only: Require premium subscription
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE, *args, **kwargs):
            if not update.effective_user:
                raise AuthenticationRequired("User authentication required")
            
            user_id = update.effective_user.id
            
            # Get user from database
            async with get_db() as db:
                cursor = await db.execute(
                    "SELECT * FROM users WHERE user_id = ?", (user_id,)
                )
                user = await cursor.fetchone()
                
                if not user:
                    raise AuthenticationRequired("User not found in database")
                
                # Check if user is active
                if not user['is_active']:
                    raise PermissionDenied("User account is inactive")
                
                # Check admin requirement
                if admin_only and not user['is_admin']:
                    raise PermissionDenied("Admin privileges required")
                
                # Check premium requirement
                if premium_only:
                    cursor = await db.execute(
                        """SELECT * FROM premium_subscriptions 
                           WHERE user_id = ? AND is_active = 1 
                           AND expires_at > datetime('now')""",
                        (user_id,)
                    )
                    subscription = await cursor.fetchone()
                    
                    if not subscription:
                        raise PermissionDenied("Premium subscription required")
                
                # Store user data in context
                context.user_data['db_user'] = dict(user)
                
                return await func(update, context, *args, **kwargs)
        
        return wrapper
    return decorator


def handle_errors(send_error_message: bool = True, log_errors: bool = True):
    """Error handling decorator.
    
    Args:
        send_error_message: Whether to send error message to user
        log_errors: Whether to log errors
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE, *args, **kwargs):
            try:
                return await func(update, context, *args, **kwargs)
            
            except RateLimitExceeded:
                # Rate limit errors are already handled
                raise
            
            except AuthenticationRequired as e:
                if log_errors:
                    logger.warning(f"Authentication required in {func.__name__}: {e}")
                
                if send_error_message and update.effective_chat:
                    await context.bot.send_message(
                        chat_id=update.effective_chat.id,
                        text="🔐 Authentication required. Please start the bot with /start"
                    )
            
            except PermissionDenied as e:
                if log_errors:
                    logger.warning(f"Permission denied in {func.__name__}: {e}")
                
                if send_error_message and update.effective_chat:
                    await context.bot.send_message(
                        chat_id=update.effective_chat.id,
                        text=f"❌ Access denied: {e}"
                    )
            
            except TelegramError as e:
                if log_errors:
                    logger.error(f"Telegram error in {func.__name__}: {e}")
                
                if send_error_message and update.effective_chat:
                    await context.bot.send_message(
                        chat_id=update.effective_chat.id,
                        text="🤖 Telegram service error. Please try again later."
                    )
            
            except Exception as e:
                if log_errors:
                    logger.error(f"Unexpected error in {func.__name__}: {e}", exc_info=True)
                
                if send_error_message and update.effective_chat:
                    await context.bot.send_message(
                        chat_id=update.effective_chat.id,
                        text="❌ An unexpected error occurred. Please try again later."
                    )
        
        return wrapper
    return decorator


def retry(max_attempts: int = 3, delay: float = 1.0, backoff: float = 2.0, 
          exceptions: tuple = (Exception,)):
    """Retry decorator with exponential backoff.
    
    Args:
        max_attempts: Maximum number of retry attempts
        delay: Initial delay between retries
        backoff: Backoff multiplier
        exceptions: Tuple of exceptions to catch and retry
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_attempts):
                try:
                    return await func(*args, **kwargs)
                
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_attempts - 1:
                        logger.error(f"Function {func.__name__} failed after {max_attempts} attempts: {e}")
                        raise
                    
                    logger.warning(f"Attempt {attempt + 1} failed for {func.__name__}: {e}. Retrying in {current_delay}s...")
                    await asyncio.sleep(current_delay)
                    current_delay *= backoff
            
            raise last_exception
        
        return wrapper
    return decorator


def cache_result(ttl: int = 300, key_func: Callable = None, per_user: bool = True):
    """Cache result decorator.
    
    Args:
        ttl: Time to live in seconds
        key_func: Custom function to generate cache key
        per_user: Whether to cache per user or globally
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE, *args, **kwargs):
            redis_client = get_redis()
            
            # Generate cache key
            if key_func:
                cache_key = key_func(update, context, *args, **kwargs)
            elif per_user:
                user_id = update.effective_user.id if update.effective_user else 'anonymous'
                cache_key = f"cache:{func.__name__}:{user_id}:{hash(str(args) + str(kwargs))}"
            else:
                cache_key = f"cache:{func.__name__}:global:{hash(str(args) + str(kwargs))}"
            
            try:
                # Try to get cached result
                cached_result = await redis_client.get(cache_key)
                if cached_result:
                    logger.debug(f"Cache hit for key: {cache_key}")
                    return eval(cached_result)  # Note: In production, use proper serialization
                
                # Execute function and cache result
                result = await func(update, context, *args, **kwargs)
                
                # Cache the result
                await redis_client.setex(cache_key, ttl, str(result))
                logger.debug(f"Cached result for key: {cache_key}")
                
                return result
                
            except Exception as e:
                logger.error(f"Cache error: {e}")
                # If cache fails, execute function normally
                return await func(update, context, *args, **kwargs)
        
        return wrapper
    return decorator


def log_execution(log_args: bool = False, log_result: bool = False, 
                  log_duration: bool = True):
    """Execution logging decorator.
    
    Args:
        log_args: Whether to log function arguments
        log_result: Whether to log function result
        log_duration: Whether to log execution duration
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            
            # Log function call
            log_msg = f"Executing {func.__name__}"
            if log_args:
                log_msg += f" with args: {args}, kwargs: {kwargs}"
            logger.info(log_msg)
            
            try:
                result = await func(*args, **kwargs)
                
                # Log result
                if log_result:
                    logger.info(f"{func.__name__} returned: {result}")
                
                # Log duration
                if log_duration:
                    duration = time.time() - start_time
                    logger.info(f"{func.__name__} executed in {duration:.3f}s")
                
                return result
                
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"{func.__name__} failed after {duration:.3f}s: {e}")
                raise
        
        return wrapper
    return decorator


def validate_input(**validators):
    """Input validation decorator.
    
    Args:
        **validators: Keyword arguments mapping parameter names to validator functions
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Get function signature
            import inspect
            sig = inspect.signature(func)
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()
            
            # Validate arguments
            for param_name, validator in validators.items():
                if param_name in bound_args.arguments:
                    value = bound_args.arguments[param_name]
                    
                    if callable(validator):
                        if not validator(value):
                            raise ValueError(f"Invalid value for parameter '{param_name}': {value}")
                    elif hasattr(validator, 'validate'):
                        is_valid, error = validator.validate(value)
                        if not is_valid:
                            raise ValueError(f"Validation failed for parameter '{param_name}': {error}")
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


def require_subscription(channels: list = None, bypass_admin: bool = True):
    """Channel subscription requirement decorator.
    
    Args:
        channels: List of channel IDs/usernames to check (None = all required channels)
        bypass_admin: Whether to bypass check for admins
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE, *args, **kwargs):
            if not update.effective_user:
                raise AuthenticationRequired("User authentication required")
            
            user_id = update.effective_user.id
            
            # Check if user is admin and bypass is enabled
            if bypass_admin:
                async with get_db() as db:
                    cursor = await db.execute(
                        "SELECT is_admin FROM users WHERE user_id = ?", (user_id,)
                    )
                    user = await cursor.fetchone()
                    
                    if user and user['is_admin']:
                        return await func(update, context, *args, **kwargs)
            
            # Get required channels
            async with get_db() as db:
                if channels:
                    placeholders = ','.join('?' * len(channels))
                    cursor = await db.execute(
                        f"SELECT * FROM required_channels WHERE channel_id IN ({placeholders}) AND is_active = 1",
                        channels
                    )
                else:
                    cursor = await db.execute(
                        "SELECT * FROM required_channels WHERE is_active = 1"
                    )
                
                required_channels = await cursor.fetchall()
            
            # Check subscription status
            unsubscribed_channels = []
            
            for channel in required_channels:
                try:
                    member = await context.bot.get_chat_member(
                        chat_id=channel['channel_id'],
                        user_id=user_id
                    )
                    
                    if member.status in ['left', 'kicked']:
                        unsubscribed_channels.append(channel)
                
                except Exception as e:
                    logger.error(f"Error checking subscription for channel {channel['channel_id']}: {e}")
                    unsubscribed_channels.append(channel)
            
            if unsubscribed_channels:
                # Send subscription message
                message = "📢 To use this feature, you must join our required channels:\n\n"
                
                for channel in unsubscribed_channels:
                    channel_name = channel.get('channel_title', channel['channel_id'])
                    join_url = channel.get('join_url', f"https://t.me/{channel['channel_username']}")
                    message += f"🔗 {channel_name}: {join_url}\n"
                
                message += "\n✅ After joining all channels, use the 'Check Subscription' button from the reply keyboard below."
                
                await context.bot.send_message(
                    chat_id=update.effective_chat.id,
                    text=message
                )
                
                return
            
            return await func(update, context, *args, **kwargs)
        
        return wrapper
    return decorator


def measure_performance(threshold_ms: float = 1000.0):
    """Performance measurement decorator.
    
    Args:
        threshold_ms: Log warning if execution time exceeds this threshold (in milliseconds)
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.perf_counter()
            
            try:
                result = await func(*args, **kwargs)
                return result
            
            finally:
                end_time = time.perf_counter()
                duration_ms = (end_time - start_time) * 1000
                
                if duration_ms > threshold_ms:
                    logger.warning(f"Slow execution: {func.__name__} took {duration_ms:.2f}ms")
                else:
                    logger.debug(f"Performance: {func.__name__} took {duration_ms:.2f}ms")
        
        return wrapper
    return decorator


# Convenience decorators combining multiple functionalities
def secure_handler(max_calls: int = 5, window_seconds: int = 60, 
                  admin_only: bool = False, premium_only: bool = False,
                  require_channels: bool = True):
    """Secure handler decorator combining rate limiting, auth, and subscription checks."""
    def decorator(func: Callable) -> Callable:
        # Apply decorators in reverse order (innermost first)
        decorated_func = func
        
        if require_channels:
            decorated_func = require_subscription()(decorated_func)
        
        if admin_only or premium_only:
            decorated_func = require_auth(admin_only=admin_only, premium_only=premium_only)(decorated_func)
        
        decorated_func = rate_limit(max_calls=max_calls, window_seconds=window_seconds)(decorated_func)
        decorated_func = handle_errors()(decorated_func)
        decorated_func = log_execution(log_duration=True)(decorated_func)
        
        return decorated_func
    
    return decorator


def api_handler(max_calls: int = 10, window_seconds: int = 60, 
               retry_attempts: int = 3, cache_ttl: int = 300):
    """API handler decorator for external API calls."""
    def decorator(func: Callable) -> Callable:
        decorated_func = func
        
        decorated_func = retry(max_attempts=retry_attempts)(decorated_func)
        decorated_func = cache_result(ttl=cache_ttl, per_user=False)(decorated_func)
        decorated_func = rate_limit(max_calls=max_calls, window_seconds=window_seconds, per_user=False)(decorated_func)
        decorated_func = handle_errors(send_error_message=False)(decorated_func)
        decorated_func = measure_performance(threshold_ms=2000.0)(decorated_func)
        
        return decorated_func
    
    return decorator