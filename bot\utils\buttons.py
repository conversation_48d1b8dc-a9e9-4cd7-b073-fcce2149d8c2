"""Utility functions for building modular reply keyboards."""

from telegram import KeyboardButton, ReplyKeyboardMarkup
from bot.utils.helpers import get_text
from typing import List, Optional


class ReplyKeyboardBuilder:
    """Builder class for creating reply keyboards with navigation."""
    
    @staticmethod
    def create_main_menu(language: str) -> ReplyKeyboardMarkup:
        """Create the main menu reply keyboard."""
        keyboard = [
            [KeyboardButton(get_text('buttons.trial_vpn', language)),
             KeyboardButton(get_text('buttons.premium', language))],
            [KeyboardButton(get_text('buttons.dashboard', language)),
             KeyboardButton(get_text('buttons.my_accounts', language))],
            [KeyboardButton(get_text('buttons.referral', language)),
             KeyboardButton(get_text('buttons.earn', language))],
            [KeyboardButton(get_text('buttons.settings', language)),
             KeyboardButton(get_text('buttons.help', language))],
            [KeyboardButton(get_text('buttons.support', language))]
        ]
        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=False)
    
    @staticmethod
    def create_premium_packages_menu(language: str) -> ReplyKeyboardMarkup:
        """Create premium packages selection menu."""
        keyboard = [
            [KeyboardButton("💎 1 Month - $9.99"), KeyboardButton("💎 3 Months - $24.99")],
            [KeyboardButton("💎 6 Months - $44.99"), KeyboardButton("💎 1 Year - $79.99")],
            [KeyboardButton(get_text('buttons.back', language)), 
             KeyboardButton(get_text('buttons.main_menu', language))]
        ]
        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=False)
    
    @staticmethod
    def create_payment_methods_menu(language: str) -> ReplyKeyboardMarkup:
        """Create payment methods selection menu."""
        keyboard = [
            [KeyboardButton(get_text('buttons.pay_with_stars', language))],
            [KeyboardButton(get_text('buttons.card_payment', language))],
            [KeyboardButton(get_text('buttons.crypto_payment', language))],
            [KeyboardButton(get_text('buttons.ton_payment', language))],
            [KeyboardButton(get_text('buttons.back', language)), 
             KeyboardButton(get_text('buttons.main_menu', language))]
        ]
        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=False)
    
    @staticmethod
    def create_settings_menu(language: str) -> ReplyKeyboardMarkup:
        """Create settings menu."""
        keyboard = [
            [KeyboardButton(get_text('buttons.change_language', language))],
            [KeyboardButton(get_text('buttons.notifications', language))],
            [KeyboardButton(get_text('buttons.back', language)),
             KeyboardButton(get_text('buttons.main_menu', language))]
        ]
        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=False)

    @staticmethod
    def create_language_selection_menu(language: str) -> ReplyKeyboardMarkup:
        """Create language selection menu."""
        keyboard = [
            [KeyboardButton("🇮🇷 فارسی"), KeyboardButton("🇺🇸 English")],
            [KeyboardButton("🇷🇺 Русский"), KeyboardButton("🇨🇳 中文")],
            [KeyboardButton(get_text('buttons.back', language)),
             KeyboardButton(get_text('buttons.main_menu', language))]
        ]
        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=False)
    
    @staticmethod
    def create_accounts_menu(language: str) -> ReplyKeyboardMarkup:
        """Create accounts management menu."""
        keyboard = [
            [KeyboardButton(get_text('buttons.refresh', language))],
            [KeyboardButton(get_text('buttons.back', language)), 
             KeyboardButton(get_text('buttons.main_menu', language))]
        ]
        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=False)
    
    @staticmethod
    def create_back_main_menu(language: str) -> ReplyKeyboardMarkup:
        """Create simple back and main menu keyboard."""
        keyboard = [
            [KeyboardButton(get_text('buttons.back', language)),
             KeyboardButton(get_text('buttons.main_menu', language))]
        ]
        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=False)

    @staticmethod
    def create_referral_menu(language: str) -> ReplyKeyboardMarkup:
        """Create referral management menu."""
        keyboard = [
            [KeyboardButton(get_text('buttons.referral_link', language))],
            [KeyboardButton(get_text('buttons.referral_stats', language))],
            [KeyboardButton(get_text('buttons.back', language)),
             KeyboardButton(get_text('buttons.main_menu', language))]
        ]
        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=False)


# InlineKeyboardBuilder removed - all navigation now uses reply keyboards