#!/usr/bin/env pwsh
# VPN Bot Development Script
# This script provides development utilities for the VPN bot project

param(
    [Parameter(Position=0)]
    [ValidateSet('setup', 'admin', 'bot', 'test', 'clean', 'install')]
    [string]$Action = 'setup'
)

Write-Host "VPN Bot Development Manager" -ForegroundColor Magenta
Write-Host "==============================" -ForegroundColor Magenta

switch ($Action) {
    'setup' {
        Write-Host "Setting up development environment..." -ForegroundColor Green
        
        # Install Python dependencies
        Write-Host "Installing Python dependencies..." -ForegroundColor Cyan
        pip install -r requirements.txt
        
        # Setup admin panel
        Write-Host "Setting up admin panel..." -ForegroundColor Cyan
        Set-Location admin-panel
        pnpm install
        Set-Location ..
        
        Write-Host "Development environment setup complete!" -ForegroundColor Green
    }
    
    'admin' {
        Write-Host "Starting admin panel in development mode..." -ForegroundColor Blue
        Set-Location admin-panel
        pnpm start
        Set-Location ..
    }
    
    'bot' {
        Write-Host "Starting Telegram bot in development mode..." -ForegroundColor Blue
        python -m bot.main
    }
    
    'test' {
        Write-Host "Running tests..." -ForegroundColor Yellow
        
        # Test Python code
        Write-Host "Testing Python components..." -ForegroundColor Cyan
        python -m pytest tests/ -v
        
        # Test admin panel
        Write-Host "Testing admin panel..." -ForegroundColor Cyan
        Set-Location admin-panel
        pnpm test
        Set-Location ..
    }
    
    'clean' {
        Write-Host "Cleaning development environment..." -ForegroundColor Yellow
        
        # Clean Python cache
        Get-ChildItem -Path . -Recurse -Name "__pycache__" | Remove-Item -Recurse -Force
        Get-ChildItem -Path . -Recurse -Name "*.pyc" | Remove-Item -Force
        
        # Clean admin panel
        if (Test-Path "admin-panel/node_modules") {
            Remove-Item -Path "admin-panel/node_modules" -Recurse -Force
        }
        if (Test-Path "admin-panel/build") {
            Remove-Item -Path "admin-panel/build" -Recurse -Force
        }
        
        # Clean Docker
        docker system prune -f
        
        Write-Host "Cleanup completed!" -ForegroundColor Green
    }
    
    'install' {
        Write-Host "Installing/updating dependencies..." -ForegroundColor Blue
        
        # Update Python dependencies
        Write-Host "Updating Python dependencies..." -ForegroundColor Cyan
        pip install --upgrade -r requirements.txt
        
        # Update admin panel dependencies
        Write-Host "Updating admin panel dependencies..." -ForegroundColor Cyan
        Set-Location admin-panel
        pnpm update
        Set-Location ..
        
        Write-Host "Dependencies updated!" -ForegroundColor Green
    }
}

Write-Host "\nAvailable commands:" -ForegroundColor Magenta
Write-Host "  .\dev.ps1 setup     - Setup development environment" -ForegroundColor White
Write-Host "  .\dev.ps1 admin     - Start admin panel (dev mode)" -ForegroundColor White
Write-Host "  .\dev.ps1 bot       - Start Telegram bot (dev mode)" -ForegroundColor White
Write-Host "  .\dev.ps1 test      - Run tests" -ForegroundColor White
Write-Host "  .\dev.ps1 clean     - Clean environment" -ForegroundColor White
Write-Host "  .\dev.ps1 install   - Install/update dependencies" -ForegroundColor White