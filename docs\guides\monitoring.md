# Monitoring and Logging Guide

This guide covers monitoring, logging, alerting, and observability for the VPN Telegram Bot project.

## Table of Contents

1. [Overview](#overview)
2. [Application Logging](#application-logging)
3. [Metrics Collection](#metrics-collection)
4. [Health Checks](#health-checks)
5. [Performance Monitoring](#performance-monitoring)
6. [Error Tracking](#error-tracking)
7. [Alerting](#alerting)
8. [Log Aggregation](#log-aggregation)
9. [Dashboards](#dashboards)
10. [Troubleshooting](#troubleshooting)

## Overview

### Monitoring Stack

Our monitoring solution includes:

- **Prometheus**: Metrics collection and storage
- **Grafana**: Visualization and dashboards
- **ELK Stack**: Log aggregation and analysis
- **Sentry**: Error tracking and performance monitoring
- **Uptime Kuma**: Service availability monitoring
- **Custom Health Checks**: Application-specific monitoring

### Key Metrics

- **Application Metrics**: Request rates, response times, error rates
- **Business Metrics**: User registrations, trial conversions, revenue
- **Infrastructure Metrics**: CPU, memory, disk, network usage
- **Database Metrics**: Connection pools, query performance, locks
- **External Service Metrics**: Telegram API, Marzban API response times

## Application Logging

### Logging Configuration

```python
# bot/utils/logging.py
import logging
import logging.handlers
import json
import sys
from datetime import datetime
from typing import Dict, Any
from bot.config import settings

class JSONFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging."""
    
    def format(self, record: logging.LogRecord) -> str:
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
        }
        
        # Add extra fields
        if hasattr(record, 'user_id'):
            log_entry['user_id'] = record.user_id
        if hasattr(record, 'request_id'):
            log_entry['request_id'] = record.request_id
        if hasattr(record, 'duration'):
            log_entry['duration'] = record.duration
        
        # Add exception info
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        return json.dumps(log_entry)

class ContextFilter(logging.Filter):
    """Add contextual information to log records."""
    
    def filter(self, record: logging.LogRecord) -> bool:
        # Add correlation ID if available
        if hasattr(self, 'correlation_id'):
            record.correlation_id = self.correlation_id
        
        # Add service name
        record.service = 'vpn-telegram-bot'
        
        return True

def setup_logging():
    """Configure application logging."""
    
    # Root logger configuration
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.LOG_LEVEL.upper()))
    
    # Remove default handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    
    if settings.LOG_FORMAT == 'json':
        console_handler.setFormatter(JSONFormatter())
    else:
        console_handler.setFormatter(
            logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
        )
    
    # File handler for persistent logging
    if settings.LOG_FILE:
        file_handler = logging.handlers.RotatingFileHandler(
            settings.LOG_FILE,
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(JSONFormatter())
        root_logger.addHandler(file_handler)
    
    # Add context filter
    context_filter = ContextFilter()
    console_handler.addFilter(context_filter)
    
    root_logger.addHandler(console_handler)
    
    # Configure specific loggers
    logging.getLogger('telegram').setLevel(logging.WARNING)
    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('asyncpg').setLevel(logging.INFO)
    
    return root_logger

class LoggerMixin:
    """Mixin to add logging capabilities to classes."""
    
    @property
    def logger(self):
        if not hasattr(self, '_logger'):
            self._logger = logging.getLogger(self.__class__.__name__)
        return self._logger
    
    def log_with_context(self, level: int, message: str, **kwargs):
        """Log message with additional context."""
        extra = {k: v for k, v in kwargs.items() if v is not None}
        self.logger.log(level, message, extra=extra)
```

### Structured Logging Examples

```python
# bot/services/auth_service.py
import logging
from bot.utils.logging import LoggerMixin

class AuthService(LoggerMixin):
    
    async def create_user(self, telegram_id: int, username: str, first_name: str):
        """Create new user with logging."""
        self.log_with_context(
            logging.INFO,
            "Creating new user",
            user_telegram_id=telegram_id,
            username=username,
            action="user_creation"
        )
        
        try:
            user = await self._create_user_in_db(
                telegram_id, username, first_name
            )
            
            self.log_with_context(
                logging.INFO,
                "User created successfully",
                user_id=user['id'],
                user_telegram_id=telegram_id,
                action="user_creation_success"
            )
            
            return user
            
        except Exception as e:
            self.log_with_context(
                logging.ERROR,
                "Failed to create user",
                user_telegram_id=telegram_id,
                error=str(e),
                action="user_creation_error"
            )
            raise
    
    async def authenticate_user(self, telegram_id: int):
        """Authenticate user with performance logging."""
        import time
        start_time = time.time()
        
        try:
            user = await self.get_user_by_telegram_id(telegram_id)
            
            if user['status'] != 'active':
                self.log_with_context(
                    logging.WARNING,
                    "Authentication failed - user not active",
                    user_id=user['id'],
                    user_status=user['status'],
                    action="auth_failed"
                )
                raise AuthenticationError("User account is not active")
            
            duration = time.time() - start_time
            self.log_with_context(
                logging.INFO,
                "User authenticated successfully",
                user_id=user['id'],
                duration=duration,
                action="auth_success"
            )
            
            return user
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_with_context(
                logging.ERROR,
                "Authentication error",
                user_telegram_id=telegram_id,
                error=str(e),
                duration=duration,
                action="auth_error"
            )
            raise
```

## Metrics Collection

### Prometheus Metrics

```python
# bot/utils/metrics.py
from prometheus_client import (
    Counter, Histogram, Gauge, Info,
    CollectorRegistry, generate_latest
)
import time
from functools import wraps
from typing import Callable, Any

# Create custom registry
registry = CollectorRegistry()

# Application metrics
REQUEST_COUNT = Counter(
    'telegram_bot_requests_total',
    'Total number of requests',
    ['method', 'endpoint', 'status'],
    registry=registry
)

REQUEST_DURATION = Histogram(
    'telegram_bot_request_duration_seconds',
    'Request duration in seconds',
    ['method', 'endpoint'],
    registry=registry
)

ACTIVE_USERS = Gauge(
    'telegram_bot_active_users',
    'Number of active users',
    registry=registry
)

VPN_ACCOUNTS = Gauge(
    'telegram_bot_vpn_accounts_total',
    'Total number of VPN accounts',
    ['status', 'type'],
    registry=registry
)

PAYMENTS = Counter(
    'telegram_bot_payments_total',
    'Total number of payments',
    ['status', 'plan'],
    registry=registry
)

REVENUE = Gauge(
    'telegram_bot_revenue_total',
    'Total revenue',
    ['currency'],
    registry=registry
)

ERRORS = Counter(
    'telegram_bot_errors_total',
    'Total number of errors',
    ['error_type', 'service'],
    registry=registry
)

DATABASE_CONNECTIONS = Gauge(
    'telegram_bot_database_connections',
    'Number of database connections',
    ['pool', 'status'],
    registry=registry
)

EXTERNAL_API_CALLS = Counter(
    'telegram_bot_external_api_calls_total',
    'Total external API calls',
    ['service', 'endpoint', 'status'],
    registry=registry
)

EXTERNAL_API_DURATION = Histogram(
    'telegram_bot_external_api_duration_seconds',
    'External API call duration',
    ['service', 'endpoint'],
    registry=registry
)

# Application info
APP_INFO = Info(
    'telegram_bot_info',
    'Application information',
    registry=registry
)

class MetricsCollector:
    """Centralized metrics collection."""
    
    def __init__(self):
        self.registry = registry
    
    def record_request(self, method: str, endpoint: str, status: str, duration: float):
        """Record HTTP request metrics."""
        REQUEST_COUNT.labels(method=method, endpoint=endpoint, status=status).inc()
        REQUEST_DURATION.labels(method=method, endpoint=endpoint).observe(duration)
    
    def record_error(self, error_type: str, service: str):
        """Record error metrics."""
        ERRORS.labels(error_type=error_type, service=service).inc()
    
    def record_payment(self, status: str, plan: str, amount: float, currency: str):
        """Record payment metrics."""
        PAYMENTS.labels(status=status, plan=plan).inc()
        if status == 'completed':
            REVENUE.labels(currency=currency).inc(amount)
    
    def update_active_users(self, count: int):
        """Update active users count."""
        ACTIVE_USERS.set(count)
    
    def update_vpn_accounts(self, status: str, account_type: str, count: int):
        """Update VPN accounts count."""
        VPN_ACCOUNTS.labels(status=status, type=account_type).set(count)
    
    def record_external_api_call(self, service: str, endpoint: str, status: str, duration: float):
        """Record external API call metrics."""
        EXTERNAL_API_CALLS.labels(
            service=service, endpoint=endpoint, status=status
        ).inc()
        EXTERNAL_API_DURATION.labels(
            service=service, endpoint=endpoint
        ).observe(duration)
    
    def update_database_connections(self, pool: str, status: str, count: int):
        """Update database connection metrics."""
        DATABASE_CONNECTIONS.labels(pool=pool, status=status).set(count)
    
    def get_metrics(self) -> str:
        """Get metrics in Prometheus format."""
        return generate_latest(self.registry).decode('utf-8')

# Global metrics collector instance
metrics = MetricsCollector()

def track_time(metric_name: str = None):
    """Decorator to track function execution time."""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs) -> Any:
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                
                if metric_name:
                    REQUEST_DURATION.labels(
                        method='async', endpoint=metric_name
                    ).observe(duration)
                
                return result
            except Exception as e:
                duration = time.time() - start_time
                metrics.record_error(type(e).__name__, func.__name__)
                raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs) -> Any:
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                if metric_name:
                    REQUEST_DURATION.labels(
                        method='sync', endpoint=metric_name
                    ).observe(duration)
                
                return result
            except Exception as e:
                duration = time.time() - start_time
                metrics.record_error(type(e).__name__, func.__name__)
                raise
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator
```

### Metrics Middleware

```python
# bot/middleware/metrics.py
import time
from telegram.ext import BaseHandler
from bot.utils.metrics import metrics
from bot.utils.logging import LoggerMixin

class MetricsMiddleware(LoggerMixin):
    """Middleware to collect metrics from Telegram updates."""
    
    async def __call__(self, update, context):
        """Process update and collect metrics."""
        start_time = time.time()
        
        # Determine update type
        update_type = self._get_update_type(update)
        user_id = None
        
        if update.effective_user:
            user_id = update.effective_user.id
        
        try:
            # Process the update
            result = await self._process_update(update, context)
            
            # Record successful processing
            duration = time.time() - start_time
            metrics.record_request(
                method='telegram',
                endpoint=update_type,
                status='success',
                duration=duration
            )
            
            self.log_with_context(
                logging.INFO,
                f"Processed {update_type} update",
                user_id=user_id,
                update_type=update_type,
                duration=duration
            )
            
            return result
            
        except Exception as e:
            # Record error
            duration = time.time() - start_time
            metrics.record_request(
                method='telegram',
                endpoint=update_type,
                status='error',
                duration=duration
            )
            
            metrics.record_error(
                error_type=type(e).__name__,
                service='telegram_handler'
            )
            
            self.log_with_context(
                logging.ERROR,
                f"Error processing {update_type} update",
                user_id=user_id,
                update_type=update_type,
                error=str(e),
                duration=duration
            )
            
            raise
    
    def _get_update_type(self, update) -> str:
        """Determine the type of update."""
        if update.message:
            if update.message.text and update.message.text.startswith('/'):
                return f"command_{update.message.text.split()[0][1:]}"
            return 'message'
        elif update.callback_query:
            return 'callback_query'
        elif update.pre_checkout_query:
            return 'pre_checkout_query'
        elif update.successful_payment:
            return 'successful_payment'
        else:
            return 'unknown'
    
    async def _process_update(self, update, context):
        """Process the update through the application."""
        # This would be implemented by the main application
        pass
```

## Health Checks

### Application Health Checks

```python
# bot/health.py
import asyncio
import asyncpg
import aioredis
from datetime import datetime, timedelta
from typing import Dict, Any, List
from bot.config import settings
from bot.utils.logging import LoggerMixin

class HealthCheck(LoggerMixin):
    """Base health check class."""
    
    def __init__(self, name: str):
        self.name = name
    
    async def check(self) -> Dict[str, Any]:
        """Perform health check."""
        raise NotImplementedError

class DatabaseHealthCheck(HealthCheck):
    """Database connectivity health check."""
    
    def __init__(self, db_pool):
        super().__init__("database")
        self.db_pool = db_pool
    
    async def check(self) -> Dict[str, Any]:
        """Check database connectivity and performance."""
        start_time = datetime.utcnow()
        
        try:
            async with self.db_pool.acquire() as conn:
                # Test basic connectivity
                await conn.fetchval("SELECT 1")
                
                # Test query performance
                query_start = datetime.utcnow()
                await conn.fetchval("SELECT COUNT(*) FROM users")
                query_duration = (datetime.utcnow() - query_start).total_seconds()
                
                # Check connection pool status
                pool_size = self.db_pool.get_size()
                pool_free = self.db_pool.get_idle_size()
                
                duration = (datetime.utcnow() - start_time).total_seconds()
                
                return {
                    "status": "healthy",
                    "duration_ms": duration * 1000,
                    "query_duration_ms": query_duration * 1000,
                    "pool_size": pool_size,
                    "pool_free": pool_free,
                    "timestamp": start_time.isoformat()
                }
                
        except Exception as e:
            duration = (datetime.utcnow() - start_time).total_seconds()
            self.logger.error(f"Database health check failed: {e}")
            
            return {
                "status": "unhealthy",
                "error": str(e),
                "duration_ms": duration * 1000,
                "timestamp": start_time.isoformat()
            }

class RedisHealthCheck(HealthCheck):
    """Redis connectivity health check."""
    
    def __init__(self, redis_client):
        super().__init__("redis")
        self.redis_client = redis_client
    
    async def check(self) -> Dict[str, Any]:
        """Check Redis connectivity and performance."""
        start_time = datetime.utcnow()
        
        try:
            # Test basic connectivity
            await self.redis_client.ping()
            
            # Test read/write performance
            test_key = f"health_check_{start_time.timestamp()}"
            
            write_start = datetime.utcnow()
            await self.redis_client.set(test_key, "test_value", ex=60)
            write_duration = (datetime.utcnow() - write_start).total_seconds()
            
            read_start = datetime.utcnow()
            value = await self.redis_client.get(test_key)
            read_duration = (datetime.utcnow() - read_start).total_seconds()
            
            # Cleanup
            await self.redis_client.delete(test_key)
            
            # Get Redis info
            info = await self.redis_client.info()
            memory_usage = info.get('used_memory_human', 'unknown')
            connected_clients = info.get('connected_clients', 0)
            
            duration = (datetime.utcnow() - start_time).total_seconds()
            
            return {
                "status": "healthy",
                "duration_ms": duration * 1000,
                "write_duration_ms": write_duration * 1000,
                "read_duration_ms": read_duration * 1000,
                "memory_usage": memory_usage,
                "connected_clients": connected_clients,
                "timestamp": start_time.isoformat()
            }
            
        except Exception as e:
            duration = (datetime.utcnow() - start_time).total_seconds()
            self.logger.error(f"Redis health check failed: {e}")
            
            return {
                "status": "unhealthy",
                "error": str(e),
                "duration_ms": duration * 1000,
                "timestamp": start_time.isoformat()
            }

class ExternalServiceHealthCheck(HealthCheck):
    """External service health check."""
    
    def __init__(self, service_name: str, url: str, timeout: int = 5):
        super().__init__(f"external_{service_name}")
        self.service_name = service_name
        self.url = url
        self.timeout = timeout
    
    async def check(self) -> Dict[str, Any]:
        """Check external service availability."""
        import aiohttp
        start_time = datetime.utcnow()
        
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.get(self.url) as response:
                    duration = (datetime.utcnow() - start_time).total_seconds()
                    
                    return {
                        "status": "healthy" if response.status < 400 else "degraded",
                        "http_status": response.status,
                        "duration_ms": duration * 1000,
                        "url": self.url,
                        "timestamp": start_time.isoformat()
                    }
                    
        except Exception as e:
            duration = (datetime.utcnow() - start_time).total_seconds()
            self.logger.error(f"{self.service_name} health check failed: {e}")
            
            return {
                "status": "unhealthy",
                "error": str(e),
                "duration_ms": duration * 1000,
                "url": self.url,
                "timestamp": start_time.isoformat()
            }

class HealthChecker(LoggerMixin):
    """Main health checker that runs all health checks."""
    
    def __init__(self):
        self.checks: List[HealthCheck] = []
    
    def add_check(self, check: HealthCheck):
        """Add a health check."""
        self.checks.append(check)
    
    async def run_all_checks(self) -> Dict[str, Any]:
        """Run all health checks."""
        start_time = datetime.utcnow()
        results = {}
        overall_status = "healthy"
        
        # Run all checks concurrently
        tasks = [check.check() for check in self.checks]
        check_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        for check, result in zip(self.checks, check_results):
            if isinstance(result, Exception):
                results[check.name] = {
                    "status": "unhealthy",
                    "error": str(result),
                    "timestamp": start_time.isoformat()
                }
                overall_status = "unhealthy"
            else:
                results[check.name] = result
                if result["status"] == "unhealthy":
                    overall_status = "unhealthy"
                elif result["status"] == "degraded" and overall_status == "healthy":
                    overall_status = "degraded"
        
        duration = (datetime.utcnow() - start_time).total_seconds()
        
        return {
            "status": overall_status,
            "timestamp": start_time.isoformat(),
            "duration_ms": duration * 1000,
            "checks": results
        }
    
    async def run_check(self, check_name: str) -> Dict[str, Any]:
        """Run a specific health check."""
        for check in self.checks:
            if check.name == check_name:
                return await check.check()
        
        return {
            "status": "not_found",
            "error": f"Health check '{check_name}' not found",
            "timestamp": datetime.utcnow().isoformat()
        }

# Global health checker instance
health_checker = HealthChecker()
```

### Health Check Endpoints

```python
# bot/api/health.py
from aiohttp import web
from bot.health import health_checker
from bot.utils.metrics import metrics

async def health_check_handler(request):
    """Health check endpoint."""
    check_name = request.match_info.get('check_name')
    
    if check_name:
        result = await health_checker.run_check(check_name)
    else:
        result = await health_checker.run_all_checks()
    
    status_code = 200
    if result["status"] == "unhealthy":
        status_code = 503
    elif result["status"] == "degraded":
        status_code = 200  # Still serving traffic
    
    return web.json_response(result, status=status_code)

async def metrics_handler(request):
    """Prometheus metrics endpoint."""
    metrics_data = metrics.get_metrics()
    return web.Response(text=metrics_data, content_type='text/plain')

async def readiness_handler(request):
    """Kubernetes readiness probe."""
    # Quick check for essential services
    essential_checks = ['database', 'redis']
    
    for check in health_checker.checks:
        if check.name in essential_checks:
            result = await check.check()
            if result["status"] == "unhealthy":
                return web.json_response(
                    {"status": "not_ready", "reason": f"{check.name} unhealthy"},
                    status=503
                )
    
    return web.json_response({"status": "ready"})

async def liveness_handler(request):
    """Kubernetes liveness probe."""
    # Simple liveness check
    return web.json_response({"status": "alive"})

def setup_health_routes(app):
    """Setup health check routes."""
    app.router.add_get('/health', health_check_handler)
    app.router.add_get('/health/{check_name}', health_check_handler)
    app.router.add_get('/metrics', metrics_handler)
    app.router.add_get('/ready', readiness_handler)
    app.router.add_get('/live', liveness_handler)
```

## Performance Monitoring

### Performance Tracking

```python
# bot/utils/performance.py
import time
import asyncio
import psutil
import gc
from typing import Dict, Any, Optional
from dataclasses import dataclass
from bot.utils.logging import LoggerMixin
from bot.utils.metrics import metrics

@dataclass
class PerformanceMetrics:
    """Performance metrics data class."""
    cpu_percent: float
    memory_percent: float
    memory_mb: float
    disk_io_read: int
    disk_io_write: int
    network_io_sent: int
    network_io_recv: int
    active_connections: int
    gc_collections: Dict[int, int]
    timestamp: float

class PerformanceMonitor(LoggerMixin):
    """System performance monitoring."""
    
    def __init__(self, interval: int = 60):
        self.interval = interval
        self.process = psutil.Process()
        self.last_disk_io = None
        self.last_network_io = None
        self.running = False
    
    async def start_monitoring(self):
        """Start performance monitoring loop."""
        self.running = True
        self.logger.info("Starting performance monitoring")
        
        while self.running:
            try:
                metrics_data = await self.collect_metrics()
                await self.process_metrics(metrics_data)
                await asyncio.sleep(self.interval)
            except Exception as e:
                self.logger.error(f"Performance monitoring error: {e}")
                await asyncio.sleep(self.interval)
    
    def stop_monitoring(self):
        """Stop performance monitoring."""
        self.running = False
        self.logger.info("Stopping performance monitoring")
    
    async def collect_metrics(self) -> PerformanceMetrics:
        """Collect current performance metrics."""
        # CPU usage
        cpu_percent = self.process.cpu_percent()
        
        # Memory usage
        memory_info = self.process.memory_info()
        memory_percent = self.process.memory_percent()
        memory_mb = memory_info.rss / 1024 / 1024
        
        # Disk I/O
        disk_io = self.process.io_counters()
        disk_io_read = disk_io.read_bytes
        disk_io_write = disk_io.write_bytes
        
        if self.last_disk_io:
            disk_io_read = disk_io_read - self.last_disk_io.read_bytes
            disk_io_write = disk_io_write - self.last_disk_io.write_bytes
        
        self.last_disk_io = disk_io
        
        # Network I/O
        try:
            network_io = psutil.net_io_counters()
            network_io_sent = network_io.bytes_sent
            network_io_recv = network_io.bytes_recv
            
            if self.last_network_io:
                network_io_sent = network_io_sent - self.last_network_io.bytes_sent
                network_io_recv = network_io_recv - self.last_network_io.bytes_recv
            
            self.last_network_io = network_io
        except:
            network_io_sent = 0
            network_io_recv = 0
        
        # Connection count
        try:
            connections = self.process.connections()
            active_connections = len([c for c in connections if c.status == 'ESTABLISHED'])
        except:
            active_connections = 0
        
        # Garbage collection stats
        gc_stats = {i: gc.get_count()[i] for i in range(3)}
        
        return PerformanceMetrics(
            cpu_percent=cpu_percent,
            memory_percent=memory_percent,
            memory_mb=memory_mb,
            disk_io_read=disk_io_read,
            disk_io_write=disk_io_write,
            network_io_sent=network_io_sent,
            network_io_recv=network_io_recv,
            active_connections=active_connections,
            gc_collections=gc_stats,
            timestamp=time.time()
        )
    
    async def process_metrics(self, metrics_data: PerformanceMetrics):
        """Process and log performance metrics."""
        # Log performance data
        self.log_with_context(
            logging.INFO,
            "Performance metrics collected",
            cpu_percent=metrics_data.cpu_percent,
            memory_percent=metrics_data.memory_percent,
            memory_mb=metrics_data.memory_mb,
            active_connections=metrics_data.active_connections,
            action="performance_metrics"
        )
        
        # Update Prometheus metrics
        from prometheus_client import Gauge
        
        # Create gauges if they don't exist
        if not hasattr(self, '_cpu_gauge'):
            self._cpu_gauge = Gauge('process_cpu_percent', 'CPU usage percentage')
            self._memory_gauge = Gauge('process_memory_percent', 'Memory usage percentage')
            self._memory_mb_gauge = Gauge('process_memory_mb', 'Memory usage in MB')
            self._connections_gauge = Gauge('process_connections', 'Active connections')
        
        self._cpu_gauge.set(metrics_data.cpu_percent)
        self._memory_gauge.set(metrics_data.memory_percent)
        self._memory_mb_gauge.set(metrics_data.memory_mb)
        self._connections_gauge.set(metrics_data.active_connections)
        
        # Check for performance issues
        await self.check_performance_thresholds(metrics_data)
    
    async def check_performance_thresholds(self, metrics_data: PerformanceMetrics):
        """Check performance thresholds and alert if necessary."""
        alerts = []
        
        # CPU threshold
        if metrics_data.cpu_percent > 80:
            alerts.append(f"High CPU usage: {metrics_data.cpu_percent:.1f}%")
        
        # Memory threshold
        if metrics_data.memory_percent > 85:
            alerts.append(f"High memory usage: {metrics_data.memory_percent:.1f}%")
        
        # Connection threshold
        if metrics_data.active_connections > 1000:
            alerts.append(f"High connection count: {metrics_data.active_connections}")
        
        # Log alerts
        for alert in alerts:
            self.logger.warning(f"Performance alert: {alert}")
            metrics.record_error("performance_threshold", "system")

# Global performance monitor
performance_monitor = PerformanceMonitor()
```

## Error Tracking

### Sentry Integration

```python
# bot/utils/sentry.py
import sentry_sdk
from sentry_sdk.integrations.asyncio import AsyncioIntegration
from sentry_sdk.integrations.logging import LoggingIntegration
from sentry_sdk.integrations.sqlalchemy import SqlalchemyIntegration
from bot.config import settings

def setup_sentry():
    """Configure Sentry error tracking."""
    if not settings.SENTRY_DSN:
        return
    
    sentry_logging = LoggingIntegration(
        level=logging.INFO,        # Capture info and above as breadcrumbs
        event_level=logging.ERROR  # Send errors as events
    )
    
    sentry_sdk.init(
        dsn=settings.SENTRY_DSN,
        environment=settings.ENVIRONMENT,
        release=settings.VERSION,
        integrations=[
            sentry_logging,
            AsyncioIntegration(),
            SqlalchemyIntegration(),
        ],
        traces_sample_rate=0.1,  # 10% of transactions
        profiles_sample_rate=0.1,  # 10% of transactions for profiling
        before_send=filter_sensitive_data,
        before_send_transaction=filter_sensitive_transactions,
    )

def filter_sensitive_data(event, hint):
    """Filter sensitive data from Sentry events."""
    # Remove sensitive fields
    if 'extra' in event:
        sensitive_keys = ['password', 'token', 'api_key', 'secret']
        for key in sensitive_keys:
            if key in event['extra']:
                event['extra'][key] = '[Filtered]'
    
    # Filter user data
    if 'user' in event:
        if 'ip_address' in event['user']:
            event['user']['ip_address'] = '[Filtered]'
    
    return event

def filter_sensitive_transactions(event, hint):
    """Filter sensitive transaction data."""
    # Skip health check transactions
    if event.get('transaction') in ['/health', '/metrics', '/ready', '/live']:
        return None
    
    return event

def capture_exception_with_context(exception: Exception, **context):
    """Capture exception with additional context."""
    with sentry_sdk.push_scope() as scope:
        for key, value in context.items():
            scope.set_extra(key, value)
        
        sentry_sdk.capture_exception(exception)

def capture_message_with_context(message: str, level: str = 'info', **context):
    """Capture message with additional context."""
    with sentry_sdk.push_scope() as scope:
        for key, value in context.items():
            scope.set_extra(key, value)
        
        sentry_sdk.capture_message(message, level)
```

## Alerting

### Alert Configuration

```yaml
# monitoring/alerts.yml
groups:
  - name: telegram_bot_alerts
    rules:
      # High error rate
      - alert: HighErrorRate
        expr: rate(telegram_bot_errors_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} errors per second"
      
      # High response time
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(telegram_bot_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}s"
      
      # Database connection issues
      - alert: DatabaseConnectionHigh
        expr: telegram_bot_database_connections{status="active"} > 80
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High database connection usage"
          description: "Database connections: {{ $value }}"
      
      # Memory usage high
      - alert: HighMemoryUsage
        expr: process_memory_percent > 85
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value }}%"
      
      # Service down
      - alert: ServiceDown
        expr: up{job="telegram-bot"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Telegram bot service is down"
          description: "The telegram bot service has been down for more than 1 minute"
      
      # External service issues
      - alert: ExternalServiceDown
        expr: telegram_bot_external_api_calls_total{status="error"} > 0
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "External service issues detected"
          description: "External service {{ $labels.service }} is experiencing issues"
```

### Alert Manager Configuration

```yaml
# monitoring/alertmanager.yml
global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'
  routes:
    - match:
        severity: critical
      receiver: 'critical-alerts'
    - match:
        severity: warning
      receiver: 'warning-alerts'

receivers:
  - name: 'web.hook'
    webhook_configs:
      - url: 'http://localhost:5001/webhook'
  
  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: 'CRITICAL: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          {{ end }}
    
    slack_configs:
      - api_url: 'YOUR_SLACK_WEBHOOK_URL'
        channel: '#alerts'
        title: 'CRITICAL Alert'
        text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
  
  - name: 'warning-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: 'WARNING: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          {{ end }}

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'instance']
```

## Log Aggregation

### ELK Stack Configuration

```yaml
# docker-compose.monitoring.yml
version: '3.8'

services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - monitoring
  
  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    volumes:
      - ./monitoring/logstash.conf:/usr/share/logstash/pipeline/logstash.conf
    ports:
      - "5044:5044"
    environment:
      - "LS_JAVA_OPTS=-Xmx256m -Xms256m"
    networks:
      - monitoring
    depends_on:
      - elasticsearch
  
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    networks:
      - monitoring
    depends_on:
      - elasticsearch
  
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.8.0
    user: root
    volumes:
      - ./monitoring/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - monitoring
    depends_on:
      - logstash

volumes:
  elasticsearch_data:

networks:
  monitoring:
    driver: bridge
```

```yaml
# monitoring/filebeat.yml
filebeat.inputs:
- type: container
  paths:
    - '/var/lib/docker/containers/*/*.log'
  processors:
    - add_docker_metadata:
        host: "unix:///var/run/docker.sock"
    - decode_json_fields:
        fields: ["message"]
        target: ""
        overwrite_keys: true

output.logstash:
  hosts: ["logstash:5044"]

logging.level: info
logging.to_files: true
logging.files:
  path: /var/log/filebeat
  name: filebeat
  keepfiles: 7
  permissions: 0644
```

```ruby
# monitoring/logstash.conf
input {
  beats {
    port => 5044
  }
}

filter {
  if [container][name] == "vpn-telegram-bot" {
    # Parse JSON logs
    if [message] =~ /^\{/ {
      json {
        source => "message"
      }
    }
    
    # Add custom fields
    mutate {
      add_field => { "service" => "telegram-bot" }
    }
    
    # Parse timestamp
    if [timestamp] {
      date {
        match => [ "timestamp", "ISO8601" ]
      }
    }
    
    # Categorize log levels
    if [level] {
      mutate {
        lowercase => [ "level" ]
      }
    }
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "telegram-bot-logs-%{+YYYY.MM.dd}"
  }
  
  # Debug output
  stdout {
    codec => rubydebug
  }
}
```

## Dashboards

### Grafana Dashboard Configuration

```json
{
  "dashboard": {
    "id": null,
    "title": "VPN Telegram Bot Monitoring",
    "tags": ["telegram", "bot", "vpn"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(telegram_bot_requests_total[5m])",
            "legendFormat": "{{method}} {{endpoint}}"
          }
        ],
        "yAxes": [
          {
            "label": "Requests/sec"
          }
        ]
      },
      {
        "id": 2,
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(telegram_bot_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.50, rate(telegram_bot_request_duration_seconds_bucket[5m]))",
            "legendFormat": "50th percentile"
          }
        ],
        "yAxes": [
          {
            "label": "Seconds"
          }
        ]
      },
      {
        "id": 3,
        "title": "Error Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(telegram_bot_errors_total[5m])",
            "legendFormat": "{{error_type}} {{service}}"
          }
        ],
        "yAxes": [
          {
            "label": "Errors/sec"
          }
        ]
      },
      {
        "id": 4,
        "title": "Active Users",
        "type": "singlestat",
        "targets": [
          {
            "expr": "telegram_bot_active_users",
            "legendFormat": "Active Users"
          }
        ]
      },
      {
        "id": 5,
        "title": "VPN Accounts",
        "type": "graph",
        "targets": [
          {
            "expr": "telegram_bot_vpn_accounts_total",
            "legendFormat": "{{status}} {{type}}"
          }
        ]
      },
      {
        "id": 6,
        "title": "Revenue",
        "type": "graph",
        "targets": [
          {
            "expr": "telegram_bot_revenue_total",
            "legendFormat": "{{currency}}"
          }
        ]
      },
      {
        "id": 7,
        "title": "Database Connections",
        "type": "graph",
        "targets": [
          {
            "expr": "telegram_bot_database_connections",
            "legendFormat": "{{pool}} {{status}}"
          }
        ]
      },
      {
        "id": 8,
        "title": "System Resources",
        "type": "graph",
        "targets": [
          {
            "expr": "process_cpu_percent",
            "legendFormat": "CPU %"
          },
          {
            "expr": "process_memory_percent",
            "legendFormat": "Memory %"
          }
        ]
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "5s"
  }
}
```

## Troubleshooting

### Common Monitoring Issues

1. **High Memory Usage**
   - Check for memory leaks in application code
   - Monitor garbage collection metrics
   - Review database connection pooling

2. **High Response Times**
   - Analyze slow database queries
   - Check external API response times
   - Review application bottlenecks

3. **Missing Metrics**
   - Verify Prometheus configuration
   - Check metric registration
   - Review network connectivity

4. **Log Aggregation Issues**
   - Verify Filebeat configuration
   - Check Elasticsearch cluster health
   - Review Logstash pipeline errors

### Debugging Commands

```bash
# Check application logs
docker logs vpn-telegram-bot --tail 100 -f

# Check Prometheus metrics
curl http://localhost:8000/metrics

# Check health endpoints
curl http://localhost:8000/health
curl http://localhost:8000/ready

# Check Elasticsearch indices
curl http://localhost:9200/_cat/indices?v

# Check Grafana datasources
curl *********************************/api/datasources
```

This monitoring and logging guide provides comprehensive observability for the VPN Telegram Bot project. Implement these practices to ensure system reliability and performance.