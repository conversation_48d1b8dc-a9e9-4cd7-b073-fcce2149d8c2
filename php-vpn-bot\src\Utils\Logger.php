<?php

declare(strict_types=1);

namespace VpnBot\Utils;

use Monolog\Logger as MonologLogger;
use Monolog\Handler\StreamHandler;
use Monolog\Handler\RotatingFileHandler;
use Monolog\Formatter\LineFormatter;
use VpnBot\Config\Config;
use Psr\Log\LoggerInterface;

class Logger
{
    private static ?LoggerInterface $instance = null;

    public static function getInstance(): LoggerInterface
    {
        if (self::$instance === null) {
            self::$instance = self::createLogger();
        }
        return self::$instance;
    }

    private static function createLogger(): LoggerInterface
    {
        $config = Config::getInstance();
        $logLevel = $config->get('app.log_level', 'info');
        
        $logger = new MonologLogger('vpn-bot');
        
        // Console handler
        $consoleHandler = new StreamHandler('php://stdout', self::getLogLevel($logLevel));
        $consoleHandler->setFormatter(new LineFormatter(
            "[%datetime%] %channel%.%level_name%: %message% %context% %extra%\n",
            'Y-m-d H:i:s'
        ));
        $logger->pushHandler($consoleHandler);
        
        // File handler
        $fileHandler = new RotatingFileHandler(
            __DIR__ . '/../../logs/bot.log',
            0,
            self::getLogLevel($logLevel)
        );
        $fileHandler->setFormatter(new LineFormatter(
            "[%datetime%] %channel%.%level_name%: %message% %context% %extra%\n",
            'Y-m-d H:i:s'
        ));
        $logger->pushHandler($fileHandler);
        
        return $logger;
    }

    private static function getLogLevel(string $level): int
    {
        return match (strtolower($level)) {
            'debug' => MonologLogger::DEBUG,
            'info' => MonologLogger::INFO,
            'notice' => MonologLogger::NOTICE,
            'warning' => MonologLogger::WARNING,
            'error' => MonologLogger::ERROR,
            'critical' => MonologLogger::CRITICAL,
            'alert' => MonologLogger::ALERT,
            'emergency' => MonologLogger::EMERGENCY,
            default => MonologLogger::INFO,
        };
    }
}
