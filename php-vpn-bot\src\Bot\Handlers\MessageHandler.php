<?php

declare(strict_types=1);

namespace VpnBot\Bot\Handlers;

use <PERSON><PERSON>\TelegramBot\Entities\ServerResponse;
use <PERSON><PERSON>\TelegramBot\Request;
use VpnBot\Models\User;
use VpnBot\Utils\Localization;
use VpnBot\Bot\Keyboards\ReplyKeyboardBuilder;
use VpnBot\Services\VpnService;
use VpnBot\Services\ChannelService;
use VpnBot\Services\ReferralService;
use VpnBot\Services\PaymentService;
use VpnBot\Config\Config;
use Psr\Log\LoggerInterface;

class MessageHandler
{
    private Localization $localization;
    private ReplyKeyboardBuilder $keyboardBuilder;
    private VpnService $vpnService;
    private ChannelService $channelService;
    private ReferralService $referralService;
    private PaymentService $paymentService;
    private Config $config;
    private LoggerInterface $logger;

    // User navigation states
    private const STATE_MAIN_MENU = 'main_menu';
    private const STATE_PREMIUM_PACKAGES = 'premium_packages';
    private const STATE_PAYMENT_METHODS = 'payment_methods';
    private const STATE_SETTINGS = 'settings';
    private const STATE_ACCOUNTS = 'accounts';
    private const STATE_LANGUAGE_SELECTION = 'language_selection';
    private const STATE_REFERRAL = 'referral';

    public function __construct(Config $config, LoggerInterface $logger)
    {
        $this->config = $config;
        $this->logger = $logger;
        $this->localization = Localization::getInstance();
        $this->keyboardBuilder = new ReplyKeyboardBuilder();
        $this->vpnService = new VpnService($config, $logger);
        $this->channelService = new ChannelService($config, $logger);
        $this->referralService = new ReferralService($config, $logger);
        $this->paymentService = new PaymentService($config, $logger);
    }

    public function handleMessage(int $chatId, int $userId, string $messageText, string $firstName = ''): ServerResponse
    {
        try {
            // Get or create user
            $user = User::findByTelegramId($userId);
            if (!$user) {
                return $this->handleNewUser($chatId, $userId, $firstName, $messageText);
            }

            $user->updateLastSeen();
            $user->save();

            $language = $user->language_code;
            $currentState = $this->getUserState($userId);

            // Handle language selection (always available)
            if ($this->isLanguageSelection($messageText)) {
                return $this->handleLanguageSelection($chatId, $user, $messageText);
            }

            // Handle navigation buttons
            if ($messageText === $this->localization->get('buttons.main_menu', $language)) {
                return $this->showMainMenu($chatId, $language, $userId);
            } elseif ($messageText === $this->localization->get('buttons.back', $language)) {
                return $this->handleBackNavigation($chatId, $language, $userId, $currentState);
            }

            // Handle state-specific buttons
            return match ($currentState) {
                self::STATE_MAIN_MENU => $this->handleMainMenuButtons($chatId, $user, $messageText),
                self::STATE_PREMIUM_PACKAGES => $this->handlePremiumPackagesButtons($chatId, $user, $messageText),
                self::STATE_PAYMENT_METHODS => $this->handlePaymentMethodsButtons($chatId, $user, $messageText),
                self::STATE_SETTINGS => $this->handleSettingsButtons($chatId, $user, $messageText),
                self::STATE_ACCOUNTS => $this->handleAccountsButtons($chatId, $user, $messageText),
                self::STATE_LANGUAGE_SELECTION => $this->handleLanguageSelectionButtons($chatId, $user, $messageText),
                self::STATE_REFERRAL => $this->handleReferralButtons($chatId, $user, $messageText),
                default => $this->showMainMenu($chatId, $language, $userId),
            };

        } catch (\Exception $e) {
            $this->logger->error('Error handling message: ' . $e->getMessage(), [
                'chat_id' => $chatId,
                'user_id' => $userId,
                'message' => $messageText,
                'trace' => $e->getTraceAsString()
            ]);

            return Request::sendMessage([
                'chat_id' => $chatId,
                'text' => $this->localization->get('errors.general_error', 'en'),
                'reply_markup' => $this->keyboardBuilder->createMainMenu('en'),
            ]);
        }
    }

    private function handleNewUser(int $chatId, int $userId, string $firstName, string $messageText): ServerResponse
    {
        $user = new User();
        $user->telegram_id = $userId;
        $user->first_name = $firstName;
        $user->language_code = 'en'; // Default language
        $user->save();

        $this->setUserState($userId, self::STATE_MAIN_MENU);

        $welcomeText = $this->localization->get('welcome.title', 'en', ['name' => $firstName]);
        $welcomeText .= "\n\n" . $this->localization->get('welcome.description', 'en');

        return Request::sendMessage([
            'chat_id' => $chatId,
            'text' => $welcomeText,
            'reply_markup' => $this->keyboardBuilder->createMainMenu('en'),
            'parse_mode' => 'Markdown',
        ]);
    }

    private function isLanguageSelection(string $messageText): bool
    {
        $languages = ["🇮🇷 فارسی", "🇺🇸 English", "🇷🇺 Русский", "🇨🇳 中文"];
        return in_array($messageText, $languages);
    }

    private function handleLanguageSelection(int $chatId, User $user, string $messageText): ServerResponse
    {
        $languageMapping = [
            "🇮🇷 فارسی" => 'fa',
            "🇺🇸 English" => 'en',
            "🇷🇺 Русский" => 'ru',
            "🇨🇳 中文" => 'zh'
        ];

        $newLanguage = $languageMapping[$messageText] ?? 'en';
        $user->language_code = $newLanguage;
        $user->save();

        $this->setUserState($user->telegram_id, self::STATE_MAIN_MENU);

        return Request::sendMessage([
            'chat_id' => $chatId,
            'text' => $this->localization->get('settings.language_changed', $newLanguage),
            'reply_markup' => $this->keyboardBuilder->createMainMenu($newLanguage),
        ]);
    }

    private function showMainMenu(int $chatId, string $language, int $userId): ServerResponse
    {
        $this->setUserState($userId, self::STATE_MAIN_MENU);
        
        return Request::sendMessage([
            'chat_id' => $chatId,
            'text' => $this->localization->get('menu.title', $language),
            'reply_markup' => $this->keyboardBuilder->createMainMenu($language),
        ]);
    }

    private function handleBackNavigation(int $chatId, string $language, int $userId, string $currentState): ServerResponse
    {
        return match ($currentState) {
            self::STATE_PREMIUM_PACKAGES, self::STATE_SETTINGS, self::STATE_ACCOUNTS, self::STATE_REFERRAL => 
                $this->showMainMenu($chatId, $language, $userId),
            self::STATE_PAYMENT_METHODS => $this->showPremiumPackages($chatId, $language, $userId),
            self::STATE_LANGUAGE_SELECTION => $this->showSettings($chatId, $language, $userId),
            default => $this->showMainMenu($chatId, $language, $userId),
        };
    }

    private function handleMainMenuButtons(int $chatId, User $user, string $messageText): ServerResponse
    {
        $language = $user->language_code;

        if ($messageText === $this->localization->get('buttons.trial_vpn', $language)) {
            return $this->handleTrialVpn($chatId, $user);
        } elseif ($messageText === $this->localization->get('buttons.premium', $language)) {
            return $this->showPremiumPackages($chatId, $language, $user->telegram_id);
        } elseif ($messageText === $this->localization->get('buttons.dashboard', $language)) {
            return $this->handleDashboard($chatId, $user);
        } elseif ($messageText === $this->localization->get('buttons.my_accounts', $language)) {
            return $this->showAccounts($chatId, $language, $user->telegram_id);
        } elseif ($messageText === $this->localization->get('buttons.referral', $language)) {
            return $this->showReferral($chatId, $language, $user->telegram_id);
        } elseif ($messageText === $this->localization->get('buttons.settings', $language)) {
            return $this->showSettings($chatId, $language, $user->telegram_id);
        } elseif ($messageText === $this->localization->get('buttons.help', $language)) {
            return $this->handleHelp($chatId, $user);
        } elseif ($messageText === $this->localization->get('buttons.support', $language)) {
            return $this->handleSupport($chatId, $user);
        }

        return Request::sendMessage([
            'chat_id' => $chatId,
            'text' => $this->localization->get('errors.unknown_command', $language),
            'reply_markup' => $this->keyboardBuilder->createMainMenu($language),
        ]);
    }

    private function showPremiumPackages(int $chatId, string $language, int $userId): ServerResponse
    {
        $this->setUserState($userId, self::STATE_PREMIUM_PACKAGES);

        $premiumText = $this->localization->get('premium.title', $language) . "\n\n";
        $premiumText .= $this->localization->get('premium.description', $language) . "\n\n";

        // Add package details
        $premiumText .= "💎 **1 Month Premium** - $9.99\n";
        $premiumText .= "• 100GB Data • 30 Days • High Speed\n\n";
        
        $premiumText .= "💎 **3 Months Premium** - $24.99\n";
        $premiumText .= "• 300GB Data • 90 Days • High Speed\n\n";
        
        $premiumText .= "💎 **6 Months Premium** - $44.99\n";
        $premiumText .= "• 600GB Data • 180 Days • Ultra Speed\n\n";
        
        $premiumText .= "💎 **1 Year Premium** - $79.99\n";
        $premiumText .= "• Unlimited Data • 365 Days • Ultra Speed";

        return Request::sendMessage([
            'chat_id' => $chatId,
            'text' => $premiumText,
            'reply_markup' => $this->keyboardBuilder->createPremiumPlansMenu($language),
            'parse_mode' => 'Markdown',
        ]);
    }

    // State management helpers
    private function getUserState(int $userId): string
    {
        // In a real implementation, this would be stored in Redis or database
        // For now, return default state
        return self::STATE_MAIN_MENU;
    }

    private function setUserState(int $userId, string $state): void
    {
        // In a real implementation, this would be stored in Redis or database
        // For now, this is a placeholder
    }

    // Placeholder methods for other handlers
    private function handleTrialVpn(int $chatId, User $user): ServerResponse
    {
        // Implementation will be added later
        return Request::sendMessage([
            'chat_id' => $chatId,
            'text' => 'Trial VPN functionality coming soon...',
            'reply_markup' => $this->keyboardBuilder->createMainMenu($user->language_code),
        ]);
    }

    private function handleDashboard(int $chatId, User $user): ServerResponse
    {
        // Implementation will be added later
        return Request::sendMessage([
            'chat_id' => $chatId,
            'text' => 'Dashboard functionality coming soon...',
            'reply_markup' => $this->keyboardBuilder->createMainMenu($user->language_code),
        ]);
    }

    private function showAccounts(int $chatId, string $language, int $userId): ServerResponse
    {
        $this->setUserState($userId, self::STATE_ACCOUNTS);
        
        return Request::sendMessage([
            'chat_id' => $chatId,
            'text' => 'Your VPN accounts will be displayed here...',
            'reply_markup' => $this->keyboardBuilder->createAccountsMenu($language),
        ]);
    }

    private function showReferral(int $chatId, string $language, int $userId): ServerResponse
    {
        $this->setUserState($userId, self::STATE_REFERRAL);
        
        return Request::sendMessage([
            'chat_id' => $chatId,
            'text' => 'Referral system functionality coming soon...',
            'reply_markup' => $this->keyboardBuilder->createReferralMenu($language),
        ]);
    }

    private function showSettings(int $chatId, string $language, int $userId): ServerResponse
    {
        $this->setUserState($userId, self::STATE_SETTINGS);
        
        return Request::sendMessage([
            'chat_id' => $chatId,
            'text' => $this->localization->get('settings.title', $language),
            'reply_markup' => $this->keyboardBuilder->createSettingsMenu($language),
        ]);
    }

    private function handleHelp(int $chatId, User $user): ServerResponse
    {
        // Implementation will be added later
        return Request::sendMessage([
            'chat_id' => $chatId,
            'text' => 'Help functionality coming soon...',
            'reply_markup' => $this->keyboardBuilder->createMainMenu($user->language_code),
        ]);
    }

    private function handleSupport(int $chatId, User $user): ServerResponse
    {
        // Implementation will be added later
        return Request::sendMessage([
            'chat_id' => $chatId,
            'text' => 'Support functionality coming soon...',
            'reply_markup' => $this->keyboardBuilder->createMainMenu($user->language_code),
        ]);
    }

    // Placeholder methods for other button handlers
    private function handlePremiumPackagesButtons(int $chatId, User $user, string $messageText): ServerResponse
    {
        // Implementation will be added later
        return $this->showMainMenu($chatId, $user->language_code, $user->telegram_id);
    }

    private function handlePaymentMethodsButtons(int $chatId, User $user, string $messageText): ServerResponse
    {
        // Implementation will be added later
        return $this->showMainMenu($chatId, $user->language_code, $user->telegram_id);
    }

    private function handleSettingsButtons(int $chatId, User $user, string $messageText): ServerResponse
    {
        $language = $user->language_code;
        
        if ($messageText === $this->localization->get('buttons.change_language', $language)) {
            $this->setUserState($user->telegram_id, self::STATE_LANGUAGE_SELECTION);
            
            return Request::sendMessage([
                'chat_id' => $chatId,
                'text' => $this->localization->get('settings.language_selection', $language),
                'reply_markup' => $this->keyboardBuilder->createLanguageSelectionMenu($language),
            ]);
        }
        
        return $this->showMainMenu($chatId, $user->language_code, $user->telegram_id);
    }

    private function handleAccountsButtons(int $chatId, User $user, string $messageText): ServerResponse
    {
        // Implementation will be added later
        return $this->showMainMenu($chatId, $user->language_code, $user->telegram_id);
    }

    private function handleLanguageSelectionButtons(int $chatId, User $user, string $messageText): ServerResponse
    {
        // Language selection is handled in the main handler
        return $this->showMainMenu($chatId, $user->language_code, $user->telegram_id);
    }

    private function handleReferralButtons(int $chatId, User $user, string $messageText): ServerResponse
    {
        // Implementation will be added later
        return $this->showMainMenu($chatId, $user->language_code, $user->telegram_id);
    }
}
