#!/usr/bin/env python3
"""
Comprehensive debugging and testing script for the VPN bot project.
This script performs various checks and validations to ensure the bot is working correctly.
"""

import asyncio
import logging
import sys
import os
from typing import Dict, Any, List
import traceback

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('debug_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class BotDebugger:
    """Comprehensive bot debugging and testing class."""
    
    def __init__(self):
        self.results = {}
        self.errors = []
    
    async def run_all_tests(self):
        """Run all debugging and testing procedures."""
        logger.info("🚀 Starting comprehensive bot debugging and testing...")
        
        tests = [
            ("Configuration Check", self.test_configuration),
            ("Database Connection", self.test_database_connection),
            ("Redis Connection", self.test_redis_connection),
            ("Model Validation", self.test_model_validation),
            ("Service Imports", self.test_service_imports),
            ("Handler Imports", self.test_handler_imports),
            ("Localization System", self.test_localization),
            ("Marzban API Configuration", self.test_marzban_api),
            ("Database Schema Consistency", self.test_schema_consistency),
        ]
        
        for test_name, test_func in tests:
            logger.info(f"🔍 Running: {test_name}")
            try:
                result = await test_func()
                self.results[test_name] = result
                if result.get('success', False):
                    logger.info(f"✅ {test_name}: PASSED")
                else:
                    logger.error(f"❌ {test_name}: FAILED - {result.get('error', 'Unknown error')}")
                    self.errors.append(f"{test_name}: {result.get('error', 'Unknown error')}")
            except Exception as e:
                error_msg = f"Exception in {test_name}: {str(e)}"
                logger.error(f"💥 {error_msg}")
                self.errors.append(error_msg)
                self.results[test_name] = {'success': False, 'error': str(e)}
        
        # Generate summary report
        await self.generate_report()
    
    async def test_configuration(self) -> Dict[str, Any]:
        """Test bot configuration."""
        try:
            from bot.config import settings
            
            required_settings = [
                'BOT_TOKEN', 'POSTGRES_HOST', 'POSTGRES_USER', 
                'POSTGRES_PASSWORD', 'POSTGRES_DATABASE', 'REDIS_HOST'
            ]
            
            missing_settings = []
            for setting in required_settings:
                if not hasattr(settings, setting) or not getattr(settings, setting):
                    missing_settings.append(setting)
            
            if missing_settings:
                return {
                    'success': False,
                    'error': f"Missing required settings: {', '.join(missing_settings)}"
                }
            
            return {
                'success': True,
                'details': {
                    'postgres_host': settings.POSTGRES_HOST,
                    'postgres_db': settings.POSTGRES_DATABASE,
                    'redis_host': settings.REDIS_HOST,
                    'bot_token_configured': bool(settings.BOT_TOKEN)
                }
            }
        except Exception as e:
            return {'success': False, 'error': f"Configuration import error: {str(e)}"}
    
    async def test_database_connection(self) -> Dict[str, Any]:
        """Test database connection."""
        try:
            from bot.database import get_db_connection
            
            async with get_db_connection() as conn:
                # Test basic query
                result = await conn.fetchval("SELECT 1")
                if result == 1:
                    # Test if main tables exist
                    tables = await conn.fetch("""
                        SELECT table_name FROM information_schema.tables 
                        WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
                    """)
                    table_names = [row['table_name'] for row in tables]
                    
                    required_tables = [
                        'users', 'vpn_accounts', 'vpn_panels', 'channels', 
                        'channel_subscriptions', 'premium_plans', 'referrals'
                    ]
                    
                    missing_tables = [t for t in required_tables if t not in table_names]
                    
                    return {
                        'success': len(missing_tables) == 0,
                        'details': {
                            'connection': 'OK',
                            'tables_found': table_names,
                            'missing_tables': missing_tables
                        },
                        'error': f"Missing tables: {missing_tables}" if missing_tables else None
                    }
                else:
                    return {'success': False, 'error': 'Database query failed'}
        except Exception as e:
            return {'success': False, 'error': f"Database connection error: {str(e)}"}
    
    async def test_redis_connection(self) -> Dict[str, Any]:
        """Test Redis connection."""
        try:
            from bot.redis import redis_client
            
            # Test Redis connection
            await redis_client.set("test_key", "test_value", ex=10)
            value = await redis_client.get("test_key")
            
            if value == "test_value":
                await redis_client.delete("test_key")
                return {
                    'success': True,
                    'details': {'connection': 'OK', 'read_write': 'OK'}
                }
            else:
                return {'success': False, 'error': 'Redis read/write test failed'}
        except Exception as e:
            return {'success': False, 'error': f"Redis connection error: {str(e)}"}
    
    async def test_model_validation(self) -> Dict[str, Any]:
        """Test SQLAlchemy model validation."""
        try:
            from bot.models import User, VPNAccount, Channel, VPNPanel, PremiumPlan
            
            # Test model instantiation
            models_tested = []
            
            # Test User model
            user = User(
                telegram_id=*********,
                first_name="Test User",
                language_code="en"
            )
            models_tested.append("User")
            
            # Test VPNAccount model
            vpn_account = VPNAccount(
                user_id=1,
                vpn_panel_id=1,
                username="test_user",
                data_limit=**********  # 1GB
            )
            models_tested.append("VPNAccount")
            
            # Test Channel model
            channel = Channel(
                channel_id="@test_channel",
                channel_name="Test Channel"
            )
            models_tested.append("Channel")
            
            return {
                'success': True,
                'details': {'models_tested': models_tested}
            }
        except Exception as e:
            return {'success': False, 'error': f"Model validation error: {str(e)}"}
    
    async def test_service_imports(self) -> Dict[str, Any]:
        """Test service imports."""
        try:
            services = [
                'bot.services.auth_service',
                'bot.services.channel_service',
                'bot.services.payment_service',
                'bot.services.referral_service',
                'bot.services.referral_analytics_service',
                'bot.services.advertising_service',
                'bot.services.payment_verification_service'
            ]
            
            imported_services = []
            failed_imports = []
            
            for service in services:
                try:
                    __import__(service)
                    imported_services.append(service)
                except Exception as e:
                    failed_imports.append(f"{service}: {str(e)}")
            
            return {
                'success': len(failed_imports) == 0,
                'details': {
                    'imported_services': imported_services,
                    'failed_imports': failed_imports
                },
                'error': f"Failed imports: {failed_imports}" if failed_imports else None
            }
        except Exception as e:
            return {'success': False, 'error': f"Service import error: {str(e)}"}
    
    async def test_handler_imports(self) -> Dict[str, Any]:
        """Test handler imports."""
        try:
            handlers = [
                'bot.handlers.commands',
                'bot.handlers.payments',
                'bot.handlers.errors',
                'bot.handlers.language_selection',
                'bot.handlers.referral'
            ]
            
            imported_handlers = []
            failed_imports = []
            
            for handler in handlers:
                try:
                    __import__(handler)
                    imported_handlers.append(handler)
                except Exception as e:
                    failed_imports.append(f"{handler}: {str(e)}")
            
            return {
                'success': len(failed_imports) == 0,
                'details': {
                    'imported_handlers': imported_handlers,
                    'failed_imports': failed_imports
                },
                'error': f"Failed imports: {failed_imports}" if failed_imports else None
            }
        except Exception as e:
            return {'success': False, 'error': f"Handler import error: {str(e)}"}
    
    async def test_localization(self) -> Dict[str, Any]:
        """Test localization system."""
        try:
            from bot.utils.helpers import get_text
            
            languages = ['en', 'fa', 'ru', 'zh']
            test_keys = ['welcome.title', 'buttons.trial_vpn', 'errors.general_error']
            
            results = {}
            errors = []
            
            for lang in languages:
                results[lang] = {}
                for key in test_keys:
                    try:
                        text = get_text(key, lang)
                        results[lang][key] = 'OK' if text and not text.startswith('Translation error') else 'MISSING'
                    except Exception as e:
                        results[lang][key] = f'ERROR: {str(e)}'
                        errors.append(f"{lang}.{key}: {str(e)}")
            
            return {
                'success': len(errors) == 0,
                'details': results,
                'error': f"Localization errors: {errors}" if errors else None
            }
        except Exception as e:
            return {'success': False, 'error': f"Localization test error: {str(e)}"}
    
    async def test_marzban_api(self) -> Dict[str, Any]:
        """Test Marzban API configuration."""
        try:
            from bot.marzban_api import MarzbanAPI
            
            # Test API class instantiation
            api = MarzbanAPI("https://example.com", "test_user", "test_pass")
            
            return {
                'success': True,
                'details': {
                    'api_class': 'OK',
                    'base_url': api.base_url,
                    'username': api.username
                }
            }
        except Exception as e:
            return {'success': False, 'error': f"Marzban API test error: {str(e)}"}
    
    async def test_schema_consistency(self) -> Dict[str, Any]:
        """Test database schema consistency with models."""
        try:
            from bot.database import get_db_connection
            from bot.models import Base
            
            async with get_db_connection() as conn:
                # Get table columns from database
                tables_info = {}
                
                # Check users table
                users_columns = await conn.fetch("""
                    SELECT column_name, data_type, is_nullable
                    FROM information_schema.columns
                    WHERE table_name = 'users' AND table_schema = 'public'
                    ORDER BY ordinal_position
                """)
                tables_info['users'] = [dict(col) for col in users_columns]
                
                # Check vpn_accounts table
                vpn_columns = await conn.fetch("""
                    SELECT column_name, data_type, is_nullable
                    FROM information_schema.columns
                    WHERE table_name = 'vpn_accounts' AND table_schema = 'public'
                    ORDER BY ordinal_position
                """)
                tables_info['vpn_accounts'] = [dict(col) for col in vpn_columns]
                
                return {
                    'success': True,
                    'details': {
                        'tables_analyzed': list(tables_info.keys()),
                        'schema_info': tables_info
                    }
                }
        except Exception as e:
            return {'success': False, 'error': f"Schema consistency test error: {str(e)}"}
    
    async def generate_report(self):
        """Generate a comprehensive debugging report."""
        logger.info("\n" + "="*80)
        logger.info("🔍 COMPREHENSIVE DEBUGGING REPORT")
        logger.info("="*80)
        
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results.values() if r.get('success', False))
        failed_tests = total_tests - passed_tests
        
        logger.info(f"📊 SUMMARY: {passed_tests}/{total_tests} tests passed")
        
        if failed_tests > 0:
            logger.error(f"❌ FAILED TESTS ({failed_tests}):")
            for error in self.errors:
                logger.error(f"   • {error}")
        
        logger.info("\n📋 DETAILED RESULTS:")
        for test_name, result in self.results.items():
            status = "✅ PASS" if result.get('success', False) else "❌ FAIL"
            logger.info(f"   {status} {test_name}")
            if result.get('details'):
                for key, value in result['details'].items():
                    logger.info(f"      {key}: {value}")
        
        logger.info("\n" + "="*80)
        
        if failed_tests == 0:
            logger.info("🎉 ALL TESTS PASSED! The bot appears to be configured correctly.")
        else:
            logger.error(f"⚠️  {failed_tests} ISSUES FOUND. Please review the errors above.")
        
        logger.info("="*80)


async def main():
    """Main function to run the debugging suite."""
    debugger = BotDebugger()
    await debugger.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
